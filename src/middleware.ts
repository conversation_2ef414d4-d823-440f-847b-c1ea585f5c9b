import { NextRequest, NextResponse } from "next/server";
import createMiddleware from "next-intl/middleware";

import { globalDefaultLang, langs } from "@/support/lang/config";
import { Locale } from "@/support/lang/lang";

import { determineLocale } from "./support/lang/strategy";
import { extractLocaleFromString } from "./utils/extractLocaleFromString";

const intlMiddleware = createMiddleware({
  locales: langs,
  defaultLocale: globalDefaultLang,
});

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)"],
};

export default function middleware(req: NextRequest) {
  const urlPath = new URL(req.url);
  const pathLocale = extractLocaleFromString(urlPath.pathname);
  const headerLocale = req.headers.get("accept-language");
  let userLocale = null;

  if (pathLocale) {
    userLocale = pathLocale;
  }

  if (!pathLocale && headerLocale) {
    userLocale = extractLocaleFromString(headerLocale);
    if (userLocale) {
      const checkedLocale = determineLocale(Locale.fromString(userLocale));
      const url = new URL(req.url);
      url.pathname = `/${checkedLocale}${url.pathname}`;
      return NextResponse.redirect(url.toString());
    }
  }

  if (userLocale === null) {
    const url = new URL(req.url);
    url.pathname = `/${globalDefaultLang}${urlPath.pathname}`;
    return NextResponse.redirect(url.toString());
  }

  const checkedLocale = determineLocale(Locale.fromString(userLocale));
  if (userLocale !== checkedLocale) {
    return NextResponse.redirect(req.url.replace(userLocale, checkedLocale));
  }

  return intlMiddleware(req);
}
