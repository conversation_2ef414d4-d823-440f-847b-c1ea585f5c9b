import dayjs from "dayjs";

export const months = Array.from({ length: 12 }, (_, i) => ({
  id: String(i + 1),
  name: String(i + 1).padStart(2, "0"),
}));

const generateYears = () => {
  const currentYear = dayjs().year();
  return Array.from({ length: 21 }, (_, i) => {
    const year = currentYear + i;
    return { id: String(year), name: String(year) };
  });
};

export const years = generateYears();
