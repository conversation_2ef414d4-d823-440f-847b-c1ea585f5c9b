@import url("https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Rethink Sans", sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.35 30.91% 10.78%;
    --card: 0 0% 100%;
    --card-foreground: 222.35 30.91% 10.78%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.35 30.91% 10.78%;
    --primary: 227.78 93.25% 53.53%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 14.29% 95.88%;
    --secondary-foreground: 222.35 30.91% 10.78%;
    --muted: 217.89 10.61% 64.9%;
    --muted-foreground: 0 0% 100%;
    --accent: 220 14.29% 95.88%;
    --accent-foreground: 222.35 30.91% 10.78%;
    --destructive: 350.05 78.9% 46.47%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13.04 90.98%;
    --input: 220 13.04 90.98%;
    --ring: 227.78 93.25% 53.53%;
    --radius: 0rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
}
.dark {
  --background: 240 5.66% 10.39%;
  --foreground: 0 0% 100%;
  --card: 240 5.66% 10.39%;
  --card-foreground: 0 0% 100%;
  --popover: 224 71.4% 4.1%;
  --popover-foreground: 0 0% 100%;
  --primary: 226.76 100% 56.47%;
  --primary-foreground: 0 0% 100%;
  --secondary: 240 4.55% 17.25%;
  --secondary-foreground: 0 0% 100%;
  --muted: 240 5.2% 33.92%;
  --muted-foreground: 0 0% 100%;
  --accent: 240 4.55% 17.25%;
  --accent-foreground: 0 0% 100%;
  --destructive: 350.05 78.9% 46.47%;
  --destructive-foreground: 0 0% 100%;
  --border: 240 5.26% 26.08%;
  --input: 240 5.26% 26.08%;
  --ring: 263.4 70% 50.4%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  text-size-adjust: 100%;
}

html,
body {
  min-height: 100vh;

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
