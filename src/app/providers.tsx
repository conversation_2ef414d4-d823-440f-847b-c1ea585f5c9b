"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactNode } from "react";

import ErrorBoundary from "@/components/ui/ErrorBoundary";
import { useSocketManager } from "@/hooks/useSocketManager";
import queryClient from "@/lib/queryClient";

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: Readonly<ProvidersProps>) {
  // Inicializa o WebSocket de forma centralizada
  useSocketManager({
    enabled: true,
    onConnectionReady: () => {
      if (process.env.NODE_ENV === "development") {
        console.log("WebSocket connection ready");
      }
    },
    onConnectionError: error => {
      console.error("WebSocket connection failed:", error);
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>{children}</ErrorBoundary>
    </QueryClientProvider>
  );
}
