"use client";

import { QueryClientProvider } from "@tanstack/react-query";
import { ReactNode, useEffect } from "react";

import ErrorBoundary from "@/components/ui/ErrorBoundary";
import queryClient from "@/lib/queryClient";
import { createSocketConnection } from "@/ws/socket";

interface ProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: Readonly<ProvidersProps>) {
  useEffect(() => {
    createSocketConnection();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary>{children}</ErrorBoundary>
    </QueryClientProvider>
  );
}
