import { render } from "@testing-library/react";

import { Providers } from "../providers";

jest.mock("@tanstack/react-query", () => ({
  QueryClientProvider: jest.fn(({ children }) => <div>{children}</div>),
}));

jest.mock("@/lib/queryClient", () => ({
  __esModule: true,
  default: {},
}));

describe("Providers", () => {
  it("should render children within QueryClientProvider", () => {
    const { getByText } = render(
      <Providers>
        <div>Test Child</div>
      </Providers>
    );

    expect(getByText("Test Child")).toBeInTheDocument();
  });
});
