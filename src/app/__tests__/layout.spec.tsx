import { render } from "@testing-library/react";
import React from "react";

import { createSocketConnection } from "@/ws/socket";

import RootLayout from "../layout";

jest.mock("@/ws/socket", () => ({
  createSocketConnection: jest.fn(),
}));

describe("RootLayout", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("calls createSocketConnection and sets the html lang attribute from navigator.language", () => {
    Object.defineProperty(window.navigator, "language", {
      value: "pt-BR",
      configurable: true,
    });

    render(
      <RootLayout>
        <div>Child</div>
      </RootLayout>
    );

    expect(createSocketConnection).toHaveBeenCalled();
    expect(document.documentElement.getAttribute("lang")).toBe("pt-BR");
  });

  it("renders the preconnect links in the head", () => {
    render(
      <RootLayout>
        <div />
      </RootLayout>
    );

    const preconnectLinks = document.head.querySelectorAll(
      'link[rel="preconnect"]'
    );
    expect(preconnectLinks).toHaveLength(2);
    expect(preconnectLinks[0]).toHaveAttribute(
      "href",
      "https://fonts.googleapis.com"
    );
    expect(preconnectLinks[1]).toHaveAttribute(
      "href",
      "https://fonts.gstatic.com"
    );
    expect(preconnectLinks[1].getAttribute("crossorigin")).toBe("anonymous");
  });

  it("wraps children in <Providers>", () => {
    const { getByText } = render(
      <RootLayout>
        <span>Content</span>
      </RootLayout>
    );

    expect(getByText("Content")).toBeInTheDocument();
  });
});
