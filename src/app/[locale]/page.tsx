"use client";

import React, { useState } from "react";

import { useSocket } from "@/hooks/useSocket";

const Page = () => {
  useSocket({
    callBack: (data: any) => setState(data),
    channel: "orders",
    event: "created",
    type: "public",
  });

  const [state, setState] = useState<any>(null);
  return (
    <div>
      <pre>{JSON.stringify(state, null, 2)}</pre>
    </div>
  );
};

export default Page;
