import "@/styles/globals.css";

import { NextIntlClientProvider, useMessages } from "next-intl";
import { Children } from "react";
import React from "react";

export const metadata = {
  title: {
    default: "The Bank",
    template: "%s | The Bank",
  },
  description: "Pagamento",
};

interface RootLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    locale: string;
  }>;
}

const ClientLayout = ({
  locale,
  children,
}: {
  locale: string;
  children: React.ReactNode;
}) => {
  const messages = useMessages();

  return (
    <NextIntlClientProvider locale={locale} messages={messages}>
      {Children.only(children)}
    </NextIntlClientProvider>
  );
};

const RootLayout = async ({ children, params }: Readonly<RootLayoutProps>) => {
  const { locale } = await params;

  return <ClientLayout locale={locale}>{children}</ClientLayout>;
};

export default RootLayout;
