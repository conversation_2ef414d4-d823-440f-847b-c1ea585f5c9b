import "@testing-library/jest-dom";

import { act, render, screen } from "@testing-library/react";
import React from "react";

import RootLayout, { metadata } from "../layout";

jest.mock("next-intl", () => ({
  NextIntlClientProvider: ({
    children,
    locale,
  }: {
    children: React.ReactNode;
    locale: string;
  }) => (
    <div data-testid="next-intl-client-provider" data-locale={locale}>
      {children}
    </div>
  ),
  useMessages: () => ({}),
}));

describe("RootLayout", () => {
  it("renders correctly with locale", async () => {
    const childText = "Test content";
    const child = <div>{childText}</div>;
    const params = Promise.resolve({ locale: "pt" });

    await act(async () => {
      const element = await RootLayout({ children: child, params });
      render(element);
    });

    expect(screen.getByText(childText)).toBeInTheDocument();

    const provider = screen.getByTestId("next-intl-client-provider");
    expect(provider.getAttribute("data-locale")).toBe("pt");
  });

  it("metadata deve estar definido corretamente", () => {
    expect(metadata).toEqual({
      title: {
        default: "The Bank",
        template: "%s | The Bank",
      },
      description: "Pagamento",
    });
  });
});
