import { headers } from "next/headers";
import { notFound } from "next/navigation";
import Script from "next/script";
import React from "react";

import { Footer } from "@/components";
import BuyerForm from "@/components/BuyerForm";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import ProductCard from "@/components/ProductCard";
import AlertSellerRegistration from "@/components/ui/AlertSellerRegistration";
import { cn } from "@/lib/utils";
import {
  GoogleAdsCredential,
  OrganizationStatus,
  Pixel,
  PixelCredential,
  PixelProviders,
  Product,
} from "@/types/product";

type PageProps = {
  params: Promise<{
    id: string;
    locale: string;
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata({ params }: PageProps) {
  const { id } = await params;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${id}`,
    {
      cache: "no-store",
      headers: {
        accept: "application/json",
      },
    }
  );

  if (!response.ok) {
    notFound();
  }

  const product: Product = await response.json();

  return {
    "@context": "http://schema.org",
    "@type": "Product",
    title: product.title,
    name: product.title,
    description: product.description,
    brand: "Exemplo de Marca",
    offers: {
      "@type": "Offer",
      priceCurrency: "BRL",
      price: (product.price / 100).toFixed(2),
      url: product.sales_page,
      availability: "http://schema.org/InStock",
    },
  };
}

async function getProductData(productId: string, acceptLanguage: string) {
  const fetchConfig = {
    cache: "no-store" as const,
    headers: {
      "accept-language": acceptLanguage,
    },
  };

  try {
    const [product, productInstallments] = await Promise.all([
      fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${productId}`,
        fetchConfig
      ).then(async res => {
        if (res.status === 404) {
          notFound();
        }
        if (!res.ok) throw new Error("Failed to fetch product");
        return res.json();
      }),
      fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${productId}/installments`,
        fetchConfig
      ).then(async res => {
        if (res.status === 404) {
          notFound();
        }
        if (!res.ok) throw new Error("Failed to fetch product installments");
        return res.json();
      }),
    ]);

    return { product, productInstallments };
  } catch (error) {
    console.error("Error fetching product data:", error);
    throw error;
  }
}

export default async function Page({ params }: PageProps) {
  const { id } = await params;
  const headersList = await headers();
  const acceptLanguage = headersList.get("accept-language");

  if (!acceptLanguage) {
    return <div>No accept language</div>;
  }

  const { product, productInstallments } = await getProductData(
    id,
    acceptLanguage
  );

  const metaPixel = product.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel.provider === PixelProviders.META
  );

  const googleAnalytics = product.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel.provider === PixelProviders.GOOGLE_ANALYTICS
  );

  const tiktokPixel = product.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel.provider === PixelProviders.TIKTOK
  );

  const googleAds = product.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel.provider === PixelProviders.GOOGLE_ADS
  );

  return (
    <>
      {googleAnalytics && (
        <>
          <Script
            async
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAnalytics?.credentials?.track_id}`}
          />
          <Script id="google-analytics">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());

              gtag('config', '${googleAnalytics?.credentials?.track_id}');
            `}
          </Script>
        </>
      )}
      {googleAds && (
        <>
          {googleAds.credentials.map((credential: GoogleAdsCredential) => (
            <React.Fragment key={credential.pixel_id}>
              <Script
                async
                src={`https://www.googletagmanager.com/gtag/js?id=${credential.pixel_id}`}
              />
              <Script id={`google-ads-${credential.pixel_id}`}>
                {`
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());

                  gtag('config', '${credential.pixel_id}');

                  ${
                    credential.events.includes("view_item")
                      ? `
                    gtag("event", "view_item", {
                      currency: "BRL",
                      value: ${(product.price / 100).toFixed(2)},
                      items: [
                        {
                          item_id: "${product.id}",
                          item_name: "${product.title.replace(/"/g, '\\"')}",
                          price: ${(product.price / 100).toFixed(2)},
                          quantity: 1
                        }
                      ]
                    });
                  `
                      : ""
                  }
                `}
              </Script>
            </React.Fragment>
          ))}
        </>
      )}
      {metaPixel?.credentials.pixels.map((pixel: PixelCredential) => (
        <Script key={pixel?.id} id={`pixel-${pixel?.id}`}>
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${pixel.id}');
            fbq('track', 'PageView');
          `}
        </Script>
      ))}
      {tiktokPixel?.credentials.pixels.map((pixel: PixelCredential) => (
        <Script key={pixel?.id} id={`pixel-${pixel?.id}`}>
          {`
            !function (w, d, t) {
            w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(
            var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var r="https://analytics.tiktok.com/i18n/pixel/events.js",o=n&&n.partner;ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=r,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script")
            ;n.type="text/javascript",n.async=!0,n.src=r+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
            ttq.load('${pixel.id}');
            ttq.page();
            }(window, document, 'ttq');
            `}
        </Script>
      ))}
      {product?.organization?.status?.value !==
        OrganizationStatus.COMPLETED && (
        <AlertSellerRegistration
          message={product?.organization?.status?.message}
        />
      )}
      <div className="flex min-h-screen items-start justify-center bg-accent">
        <main
          className={cn(
            "bg-white",
            "rounded-lg",
            "w-full",
            "sm:w-[678px]",
            "h-auto",
            "p-4",
            "sm:p-5",
            "m-4",
            "sm:my-5",
            "lg:my-12"
          )}
        >
          <div className="flex flex-col-reverse justify-between md:flex-row">
            <ProductCard
              product={product}
              productInstallments={productInstallments}
            />
            <LanguageSwitcher />
          </div>
          <BuyerForm
            product={product}
            productInstallments={productInstallments}
          />
          <Footer organization={product?.platform?.name || "-"} />
        </main>
      </div>
    </>
  );
}
