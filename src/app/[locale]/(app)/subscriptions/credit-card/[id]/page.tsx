"use client";

import { useParams, useRouter } from "next/navigation";

import { CreditCardErrorModal } from "@/components/CreditCardErrorModal";
import { CreditCardPage } from "@/components/CreditCardPage";
import { useAwaitingPaymentData } from "@/hooks/useAwaitingPaymentData";
import { useAwaitingPaymentSubscription } from "@/hooks/useAwaitingPaymentSubscription";
import { listen } from "@/hooks/useSocket";

export default function SubscriptionPendingPage() {
  const router = useRouter();
  const params = useParams();
  const subscriptionId = params.id as string;

  const { subscriptionData } = useAwaitingPaymentSubscription(subscriptionId);

  const { isOpenModal, socketCallback, setError, setIsOpenModal } =
    useAwaitingPaymentData(subscriptionData, subscriptionId);

  listen({
    channel: `subscription.${subscriptionId}`,
    event: "payment.status",
    type: "public",
    callBack: socketCallback,
  });

  const onCloseModal = () => {
    setError();
    setIsOpenModal(false);

    router.replace(`/${params.locale}/subscriptions/${subscriptionId}`);
  };

  return (
    <>
      <CreditCardErrorModal isOpen={isOpenModal} onClose={onCloseModal} />
      <CreditCardPage data={subscriptionData} id={subscriptionId} />
    </>
  );
}
