import { headers } from "next/headers";
import { notFound } from "next/navigation";
import React from "react";

import { Footer } from "@/components";
import { SubscriptionHeader } from "@/components/SubscriptionHeader";
import SubscriptionRenewalPaymentForm from "@/components/SubscriptionRenewalPaymentForm";
import { cn } from "@/lib/utils";
import { getSubscriptionData } from "@/services/subscriptionApi";
import { createSubscriptionProductForComponents } from "@/types/subscription";

type PageProps = {
  params: Promise<{
    id: string;
    locale: string;
  }>;
  searchParams: Promise<{
    signature?: string;
  }>;
};

export async function generateMetadata({ params }: PageProps) {
  const { id } = await params;

  return {
    title: "Renovar Assinatura",
    description: `Renovar sua assinatura - ID: ${id}`,
  };
}

async function getSubscriptionPageData(
  subscriptionId: string,
  signature?: string,
  acceptLanguage?: string
) {
  try {
    const subscriptionData = await getSubscriptionData(
      subscriptionId,
      signature,
      acceptLanguage
    );
    return { subscriptionData };
  } catch (error) {
    console.error("Error fetching subscription data:", error);

    if (
      error instanceof Error &&
      (error.message === "SUBSCRIPTION_NOT_FOUND" ||
        error.message === "Subscription not found")
    ) {
      notFound();
    }

    notFound();
  }
}

export default async function SubscriptionsPage({
  params,
  searchParams,
}: PageProps) {
  const { id } = await params;
  const { signature } = await searchParams;
  const headersList = await headers();
  const acceptLanguage = headersList.get("accept-language");

  const { subscriptionData } = await getSubscriptionPageData(
    id,
    signature,
    acceptLanguage || undefined
  );

  const adaptedProductInstallments = {
    installments: subscriptionData.credit_card_installments.map(
      installment => ({
        installments: installment.installments.toString(),
        total: installment.total.toString(),
        description: installment.description,
      })
    ),
    bumps: [],
  };

  const subscriptionProduct = createSubscriptionProductForComponents(
    id,
    subscriptionData
  );

  return (
    <div className="flex min-h-screen items-start justify-center bg-accent">
      <main
        className={cn(
          "bg-white",
          "rounded-lg",
          "w-full",
          "sm:w-[678px]",
          "h-auto",
          "px-6",
          "pb-6",
          "sm:px-5",
          "sm:pb-5",
          "m-4",
          "sm:my-5",
          "lg:my-12"
        )}
      >
        <SubscriptionHeader
          subscriptionApiData={subscriptionData}
          product={subscriptionProduct}
          productInstallments={adaptedProductInstallments}
        />
        <div className="mt-6">
          <SubscriptionRenewalPaymentForm
            product={subscriptionProduct}
            productInstallments={adaptedProductInstallments}
            subscriptionId={id}
            signature={signature}
          />
        </div>
        <Footer organization={subscriptionData.product.platform.name} />
      </main>
    </div>
  );
}
