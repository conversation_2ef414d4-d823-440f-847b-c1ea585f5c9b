import "@testing-library/jest-dom";

import { render, screen } from "@testing-library/react";

import SubscriptionNotFound from "../not-found";

describe("SubscriptionNotFound Component", () => {
  it("renders 404 text", () => {
    render(<SubscriptionNotFound />);

    const heading = screen.getByText("404");
    expect(heading).toBeInTheDocument();
  });

  it("renders subscription-specific error message", () => {
    render(<SubscriptionNotFound />);

    const message = screen.getByText("Ops! Subscription not found.");
    expect(message).toBeInTheDocument();
  });

  it("renders additional context message", () => {
    render(<SubscriptionNotFound />);

    const contextMessage = screen.getByText(
      "The subscription youre looking for doesnt exist or has expired."
    );
    expect(contextMessage).toBeInTheDocument();
  });
});
