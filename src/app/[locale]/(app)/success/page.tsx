"use client";
import { getCreditCardNameByNumber } from "@thebank/creditcard-js";
import { useSearchParams } from "next/navigation";
import Script from "next/script";
import { useTranslations } from "next-intl";
import React, { useMemo } from "react";

import { ConfettiSideCannons } from "@/components/ConfettiSideCannons";
import { FooterImage } from "@/components/FooterImage";
import { HelpSuccessSection } from "@/components/HelpSuccessSection";
import { OrderInfo } from "@/components/OrderInfo";
import { ProductOrderDetails } from "@/components/ProductOrderDetails";
import { SuccessHeader } from "@/components/SuccessHeader";
import { useProduct } from "@/hooks/useGetProduct";
import { Pixel, PixelCredential, PixelProviders } from "@/types/product";
import { OrderPaymentResponse } from "@/types/successPage";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";
import { clearString } from "@/utils/stringUtils";
import { fromBase64 } from "@/utils/stringUtils/enconding";

enum ContentType {
  SUBSCRIPTION = "subscription",
  SUBSCRIPTION_TRIAL = "trialed",
  ORDER = "order",
}

interface BuyerData {
  email: string;
  confirm_email: string;
  document?: { type: "cpf" | "cnpj"; number: string };
  name: string;
  phone: { ddi: string; number: string };
  instalment: number;
  cardHolderName?: string;
  cardNumber?: string;
  cardCvv?: string;
  cardExpirationDateMonth?: string;
  cardExpirationDateYear?: string;
  selectedInstalmentDescription?: string;
}

function inferCardBrand(cardNumber: string): string {
  const cleanedNumber = clearString(cardNumber);
  const brand = getCreditCardNameByNumber(cleanedNumber);

  if (brand && brand !== "Credit card is invalid!") {
    return brand.toLowerCase();
  }

  return "unknown";
}

function getLastFourDigits(cardNumber: string): string {
  const cleanedNumber = clearString(cardNumber);
  return cleanedNumber.slice(-4);
}

export default function Success() {
  const t = useTranslations("success");

  const searchParams = useSearchParams();

  const encodedData = searchParams.get("d");

  const originalSuccessData: OrderPaymentResponse = JSON.parse(
    fromBase64(encodedData as string)
  );

  const productId = originalSuccessData?.product?.id;
  const subscriptionId = originalSuccessData?.subscription_id;

  const { data: productData } = useProduct(productId);

  const successData = useMemo(() => {
    const isTrialProduct =
      productData?.charge_type === "subscription" &&
      productData?.subscription?.trial_days !== null &&
      productData?.subscription?.trial_days > 0;

    if (!isTrialProduct || originalSuccessData.payment?.brand) {
      return originalSuccessData;
    }

    let buyerData: BuyerData | null = null;
    if (typeof window !== "undefined") {
      const savedData = sessionStorage.getItem("buyerData");
      buyerData = savedData ? JSON.parse(savedData) : null;
    }

    if (!buyerData) {
      return originalSuccessData;
    }

    const updatedSuccessData = { ...originalSuccessData };

    if (!updatedSuccessData.customer_email && buyerData.email) {
      updatedSuccessData.customer_email = buyerData.email;
    }

    if (
      buyerData.cardNumber &&
      updatedSuccessData.payment_method === "credit_card"
    ) {
      const cardBrand = inferCardBrand(buyerData.cardNumber);
      const lastFourDigits = getLastFourDigits(buyerData.cardNumber);

      updatedSuccessData.payment = {
        ...updatedSuccessData.payment,
        brand: cardBrand,
        first_digits: clearString(buyerData.cardNumber).slice(0, 4),
        last_digits: lastFourDigits,
        description:
          updatedSuccessData.payment?.description ||
          buyerData.selectedInstalmentDescription ||
          `${buyerData.instalment}x de ${formatToBRL((productData?.price || 0) / CENTS_IN_REAL / buyerData.instalment)}`,
      };
    }

    return updatedSuccessData;
  }, [originalSuccessData, productData]);

  const isSubscriptionWithTrial = useMemo(() => {
    return (
      successData?.product?.charge_type === "subscription" &&
      successData?.product?.subscription?.trial_days !== null &&
      successData?.product?.subscription?.trial_days &&
      successData?.product?.subscription?.trial_days > 0 &&
      (successData?.payment?.status === "trialed" || subscriptionId)
    );
  }, [successData, subscriptionId]);

  const isSubscription = useMemo(() => {
    return (
      successData?.product?.charge_type === "subscription" ||
      subscriptionId ||
      successData?.payment?.status === "trialed"
    );
  }, [successData, subscriptionId]);

  const successTitle = useMemo(() => {
    if (isSubscriptionWithTrial) {
      return t("subscription_title");
    }
    return t("title");
  }, [isSubscriptionWithTrial, t]);

  const contentType = useMemo(() => {
    if (isSubscription) {
      return isSubscriptionWithTrial
        ? ContentType.SUBSCRIPTION_TRIAL
        : ContentType.SUBSCRIPTION;
    }
    return ContentType.ORDER;
  }, [isSubscription, isSubscriptionWithTrial]);

  const contentRenderers = {
    [ContentType.SUBSCRIPTION]: (
      <p className="mb-6 mt-6 text-xs leading-5 text-gray-600">
        {t("subscription_success_message")}
      </p>
    ),
    [ContentType.SUBSCRIPTION_TRIAL]: (
      <OrderInfo
        description={t.rich("subscription_trial_message", {
          email: successData?.customer_email,
          bold: (chunks: any) => <strong>{chunks}</strong>,
        })}
      />
    ),
    [ContentType.ORDER]: (
      <OrderInfo
        description={t.rich("description", {
          email: successData?.customer_email,
          bold: (chunks: any) => <strong>{chunks}</strong>,
        })}
      />
    ),
  };

  const metaPixel = productData?.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel?.provider === PixelProviders.META
  );

  const tiktokPixel = productData?.pixels?.find(
    (pixel: { provider: string }): pixel is Pixel =>
      pixel?.provider === PixelProviders.TIKTOK
  );

  return (
    <>
      {metaPixel?.credentials.pixels.map((pixel: PixelCredential) => (
        <Script key={pixel?.id} id={`pixel-${pixel?.id}`}>
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${pixel?.id}');
            fbq('track', 'Purchase', {
              value: '${successData?.payment?.description}',
              currency: 'BRL'
            });
          `}
        </Script>
      ))}

      {tiktokPixel?.credentials.pixels.map((pixel: PixelCredential) => (
        <Script key={pixel?.id} id={`pixel-${pixel?.id}`}>
          {`
            !function (w, d, t) {
            w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(
            var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var r="https://analytics.tiktok.com/i18n/pixel/events.js",o=n&&n.partner;ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=r,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};n=document.createElement("script")
            ;n.type="text/javascript",n.async=!0,n.src=r+"?sdkid="+e+"&lib="+t;e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(n,e)};
            ttq.load('${pixel.id}');
            ttq.page();
            ttq.track("CompletePayment", {
              contents: [
                {
                  content_id: "${productId}",
                  content_type: "product",
                  content_name: "${productData?.name}"
                }
              ],
              value: '${successData?.payment?.description}',
              currency: "BRL"
            });
            }(window, document, 'ttq');
            `}
        </Script>
      ))}

      <div className="flex min-h-screen flex-col items-center justify-center bg-accent pl-2 pr-2">
        <ConfettiSideCannons />
        <SuccessHeader title={successTitle} />
        <div className="flex w-full max-w-[678px] flex-col bg-white p-6">
          {contentRenderers[contentType]}
          <ProductOrderDetails data={successData} />
          <HelpSuccessSection isSubscription={!!isSubscription} />
        </div>
        <FooterImage />
      </div>
    </>
  );
}
