"use client";

import { Launch } from "@carbon/icons-react";
import Image from "next/image";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import Barcode from "react-barcode";

import { AlertBox } from "@/components/AlertBox";
import ButtonBack from "@/components/ButtonBack";
import { CopyButtonBoleto } from "@/components/CopyButtonBoleto";
import HeaderBoleto from "@/components/HeaderBoleto";
import HelpSection from "@/components/HelpSection";
import ProductInfo from "@/components/ProductInfo";
import { Button } from "@/components/ui/Button";
import { listen } from "@/hooks/useSocket";
import { cn } from "@/lib/utils";
import { BoletoPaymentDetails } from "@/types/paymentDetails";
import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { CENTS_IN_REAL } from "@/utils/miscellaneous";
import { fromBase64, toBase64 } from "@/utils/stringUtils/enconding";
import { convertWebSocketSubscriptionToSuccessData } from "@/utils/subscriptionUtils/convertWebSocketSubscriptionToSuccessData";
import { createSocketConnection } from "@/ws/socket";

export default function BoletoPage() {
  createSocketConnection();

  const router = useRouter();
  const params = useParams();

  const t = useTranslations("boleto_page");

  const searchParams = useSearchParams();

  const encodedData = searchParams.get("d");

  const boletoData: BoletoPaymentDetails = JSON.parse(
    fromBase64(encodedData as string)
  );

  const channel = boletoData?.subscription_id
    ? `subscription.${boletoData.subscription_id}`
    : `order.${boletoData?.order_id}`;

  listen({
    channel,
    event: "payment.status",
    callBack: (data: any) => {
      const isApproved =
        data.payment?.status === SubscriptionRenewalStatus.APPROVED;

      if (isApproved) {
        if (data?.thank_you_page_url) {
          window.location.href = data.thank_you_page_url;
          return;
        }

        let queryData;

        if (boletoData?.subscription_id) {
          queryData = convertWebSocketSubscriptionToSuccessData(
            data,
            boletoData.subscription_id
          );
        } else {
          queryData = data;
        }

        const query = encodeURIComponent(toBase64(JSON.stringify(queryData)));

        router.push(`/${params.locale}/success?d=${query}`);
      }
    },
    type: "public",
  });

  return (
    <div className="flex min-h-screen flex-col items-center justify-start bg-accent p-5">
      <div className="w-full max-w-[678px] md:mt-12">
        <HeaderBoleto />
        <main
          className={cn(
            "bg-white",
            "rounded-b-lg",
            "shadow-lg",
            "w-full",
            "h-auto",
            "p-8"
          )}
        >
          <div className="flex flex-col-reverse justify-between md:flex-row">
            <p
              className={`w-full max-w-[300px] leading-6 text-gray-900 ${
                boletoData?.subscription_id
                  ? "sm:max-w-[600px]"
                  : "sm:max-w-[590px]"
              }`}
            >
              {boletoData?.subscription_id
                ? t("subscription_order_confirmation")
                : t.rich("order_confirmation", {
                    email: boletoData?.customer_email,
                    bold: chunks => <strong>{chunks}</strong>,
                  })}
            </p>
          </div>
          <div className="mt-2 flex flex-wrap gap-6">
            <div className="flex w-full flex-col gap-4">
              <AlertBox
                className="w-full"
                variant="secondary"
                icon={
                  <Image
                    src="/images/information.svg"
                    alt="Check"
                    width={20}
                    height={20}
                  />
                }
                description={t("approval_delay")}
              />
              <div className="flex gap-8">
                <div className="flex flex-col">
                  <p className="text-sm font-medium leading-4 text-gray-900">
                    {t("due_date")}
                  </p>
                  <span className="text-sm leading-5 text-gray-600">
                    {boletoData?.boleto?.expires_date}
                  </span>
                </div>
                <div className="flex flex-col">
                  <p className="text-sm font-medium leading-4 text-gray-900">
                    {t("amount")}
                  </p>
                  <span className="text-sm font-medium leading-4 text-emerald-700">
                    {formatToBRL(boletoData?.boleto?.amount / CENTS_IN_REAL)}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6">
            <p className="mb-1 text-sm font-medium leading-4 text-gray-900 sm:block">
              {t("copy_boleto_code")}
            </p>
            <p className="break-words text-sm leading-5 text-gray-600">
              {boletoData?.boleto?.barcode_data}
            </p>
            <div className="mx-auto ml-[-10px] w-full overflow-hidden">
              <Barcode
                value={boletoData?.boleto?.barcode_data}
                height={60}
                width={2.9}
                displayValue={false}
                renderer="svg"
              />
            </div>

            <div className="flex w-full flex-col items-center justify-center gap-2 sm:flex-row sm:gap-4">
              <CopyButtonBoleto
                boletoData={boletoData}
                text={t("copy_boleto_code")}
                className="h-12 min-w-[150px] flex-1 justify-center bg-primary text-white sm:bg-gray-100 sm:text-gray-900"
              />
              <Button
                onClick={() =>
                  window.open(boletoData?.boleto?.barcode_image, "_blank")
                }
                className="h-12 w-full flex-1 justify-center gap-2 bg-gray-100 p-3 text-gray-900 sm:mt-4 sm:bg-primary sm:text-white"
              >
                {t("view_boleto")}
                <Launch size={18} />
              </Button>
            </div>
            <ProductInfo
              infoData={boletoData?.product}
              additionalProductInfo={boletoData?.bumps}
            />

            <HelpSection />
          </div>
        </main>
      </div>
      <div className="mb-12 mt-6 w-full max-w-[678px]">
        <div
          className={cn(
            "bg-white",
            "rounded-b-lg",
            "shadow-lg",
            "w-full",
            "h-auto",
            "p-4"
          )}
        >
          <ButtonBack
            subscriptionId={boletoData?.subscription_id}
            text={t("change_payment_method")}
            orderId={boletoData?.order_id}
            productId={boletoData?.product?.id}
          />
        </div>
      </div>
    </div>
  );
}
