import { headers } from "next/headers";
import { notFound } from "next/navigation";

import BuyerForm from "@/components/BuyerForm";
import { Footer } from "@/components/Footer";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import ProductCard from "@/components/ProductCard";
import AlertSellerRegistration from "@/components/ui/AlertSellerRegistration";
import { cn } from "@/lib/utils";
import { OrganizationStatus, Product } from "@/types/product";

type PageProps = {
  params: Promise<{
    id: string;
    locale: string;
  }>;
  searchParams: Promise<{
    product?: string;
  }>;
};

export async function generateMetadata({ params, searchParams }: PageProps) {
  const { id } = await params;
  const { product: productId } = await searchParams;
  const finalProductId = productId || id;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${finalProductId}`,
    {
      cache: "no-store",
      headers: {
        accept: "application/json",
      },
    }
  );

  if (!response.ok) {
    notFound();
  }

  const productData: Product = await response.json();

  return {
    "@context": "http://schema.org",
    "@type": "Product",
    title: productData.title,
    name: productData.title,
    description: productData.description,
    brand: "Exemplo de Marca",
    offers: {
      "@type": "Offer",
      priceCurrency: "BRL",
      price: (productData.price / 100).toFixed(2),
      url: productData.sales_page,
      availability: "http://schema.org/InStock",
    },
  };
}

async function getProductData(productId: string, acceptLanguage: string) {
  const fetchConfig = {
    cache: "no-store" as const,
    headers: {
      "accept-language": acceptLanguage,
    },
  };

  try {
    const [product, productInstallments] = await Promise.all([
      fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${productId}`,
        fetchConfig
      ).then(async res => {
        if (res.status === 404) {
          notFound();
        }
        if (!res.ok) throw new Error("Failed to fetch product");
        return res.json();
      }),
      fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/products/${productId}/installments`,
        fetchConfig
      ).then(async res => {
        if (res.status === 404) {
          notFound();
        }
        if (!res.ok) throw new Error("Failed to fetch product installments");
        return res.json();
      }),
    ]);

    return { product, productInstallments };
  } catch (error) {
    console.error("Error fetching product data:", error);
    throw error;
  }
}

export default async function Page({ params, searchParams }: PageProps) {
  const { id } = await params;
  const { product: productId } = await searchParams;
  const finalProductId = productId || id;
  const headersList = await headers();
  const acceptLanguage = headersList.get("accept-language");

  if (!acceptLanguage) {
    return <div>No accept language</div>;
  }

  const { product, productInstallments } = await getProductData(
    finalProductId,
    acceptLanguage
  );

  return (
    <>
      {product?.organization?.status?.value !==
        OrganizationStatus.COMPLETED && (
        <AlertSellerRegistration
          message={product?.organization?.status?.message}
        />
      )}
      <div className="flex min-h-screen items-start justify-center bg-accent">
        <main
          className={cn(
            "bg-white",
            "rounded-lg",
            "w-full",
            "sm:w-[678px]",
            "h-auto",
            "p-4",
            "sm:p-5",
            "m-4",
            "sm:my-5",
            "lg:my-12"
          )}
        >
          <div className="flex flex-col-reverse justify-between md:flex-row">
            <ProductCard
              product={product}
              productInstallments={productInstallments}
            />
            <LanguageSwitcher isRetryOrderPage={true} />
          </div>
          <BuyerForm
            product={product}
            productInstallments={productInstallments}
          />
          <Footer organization={product?.platform?.name || "-"} />
        </main>
      </div>
    </>
  );
}
