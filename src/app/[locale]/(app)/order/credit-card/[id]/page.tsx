"use client";

import { useParams, useRouter, useSearchParams } from "next/navigation";

import { CreditCardErrorModal } from "@/components/CreditCardErrorModal";
import { CreditCardPage } from "@/components/CreditCardPage";
import { useAwaitingPaymentData } from "@/hooks/useAwaitingPaymentData";
import { useAwaitingPaymentOrder } from "@/hooks/useAwaitingPaymentOrder";
import { listen } from "@/hooks/useSocket";
import { makeQuery } from "@/utils/stringUtils/enconding";
import { createSocketConnection } from "@/ws/socket";

export default function OrderPendingPage() {
  createSocketConnection();

  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const orderId = params.id as string;
  const productId = searchParams.get("product");

  const { orderData } = useAwaitingPaymentOrder(orderId);

  const { isOpenModal, socketCallback, setError, data, setIsOpenModal } =
    useAwaitingPaymentData(orderData, orderId);

  listen({
    channel: `order.${orderId}`,
    event: "payment.status",
    type: "public",
    callBack: socketCallback,
  });

  const onCloseModal = () => {
    const query = makeQuery(data);

    setError();
    setIsOpenModal(false);

    router.replace(
      `/${params.locale}/order/${orderId}?product=${productId}&d=${query}`
    );
  };

  return (
    <>
      <CreditCardErrorModal isOpen={isOpenModal} onClose={onCloseModal} />
      <CreditCardPage data={orderData} id={orderId} />
    </>
  );
}
