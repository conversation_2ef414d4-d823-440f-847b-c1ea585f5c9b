"use client";

import Image from "next/image";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

import { AlertBox } from "@/components/AlertBox";
import ButtonBack from "@/components/ButtonBack";
import { CopyButton } from "@/components/CopyButton";
import Header from "@/components/HeaderPix";
import HelpSection from "@/components/HelpSection";
import PaymentInstructions from "@/components/PaymentInstructions";
import PaymentValue from "@/components/PaymentValue";
import ProductInfo from "@/components/ProductInfo";
import { QRCodeSection } from "@/components/QRCodeSection";
import { listen } from "@/hooks/useSocket";
import { cn } from "@/lib/utils";
import { PixPaymentDetails } from "@/types/paymentDetails";
import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";
import { fromBase64, toBase64 } from "@/utils/stringUtils/enconding";
import { convertWebSocketSubscriptionToSuccessData } from "@/utils/subscriptionUtils/convertWebSocketSubscriptionToSuccessData";

export default function PixPage() {
  const t = useTranslations("pix_page");
  const router = useRouter();
  const params = useParams();

  const searchParams = useSearchParams();

  const encodedData = searchParams.get("d");

  const pixData: PixPaymentDetails = JSON.parse(
    fromBase64(encodedData as string)
  );

  const channel = pixData.subscription_id
    ? `subscription.${pixData.subscription_id}`
    : `order.${pixData.order_id}`;

  listen({
    channel,
    event: "payment.status",
    callBack: (data: any) => {
      if (data.payment.status === SubscriptionRenewalStatus.APPROVED) {
        if (data?.thank_you_page_url) {
          window.location.href = data.thank_you_page_url;
          return;
        }

        let queryData;

        if (pixData.subscription_id) {
          queryData = convertWebSocketSubscriptionToSuccessData(
            data,
            pixData.subscription_id,
            "Customer"
          );
        } else {
          queryData = data;
        }

        const query = encodeURIComponent(toBase64(JSON.stringify(queryData)));

        router.push(`/${params.locale}/success?d=${query}`);
      }
    },
    type: "public",
  });

  return (
    <div className="flex min-h-screen flex-col items-center justify-start bg-accent p-5">
      <>
        <div className="w-full max-w-[678px] md:mt-12">
          <Header pixExpiresIn={pixData?.pix?.expires_in} />
          <main
            className={cn(
              "bg-white",
              "rounded-b-lg",
              "shadow-lg",
              "w-full",
              "h-auto",
              "p-8"
            )}
          >
            <div className="flex flex-col-reverse justify-between md:flex-row">
              <p className="w-full max-w-[300px] leading-6 text-gray-900 sm:max-w-[550px]">
                {pixData.subscription_id
                  ? t("subscription_success_message")
                  : t("order_success_message")}
              </p>
            </div>
            <div className="mt-2 flex flex-wrap gap-6">
              <Image
                src={pixData?.pix?.qr_code}
                alt="QR Code"
                width={200}
                height={200}
                className="hidden sm:block"
              />
              <div className="flex w-full max-w-[340px] flex-col gap-4">
                <PaymentValue
                  amount={formatToBRL(pixData?.pix?.amount / CENTS_IN_REAL)}
                />
                <AlertBox
                  className="w-[300px] sm:w-full"
                  variant="secondary"
                  icon={
                    <Image
                      src="/images/information.svg"
                      alt="Check"
                      width={20}
                      height={20}
                    />
                  }
                  description={t("approval_time")}
                />
                <PaymentInstructions hiddenOnSmall={false} />
              </div>
            </div>
            <div className="mt-6">
              <p className="mb-1 hidden text-sm leading-5 text-gray-900 sm:block">
                {t("copy_code_instruction")}
              </p>
              <p className="break-words text-sm leading-5 text-gray-600">
                {pixData?.pix?.copy_paste}
              </p>
              <CopyButton
                pixData={pixData}
                text={t("copy_pix_code")}
                className="justify-center bg-primary text-white sm:w-[180px] sm:justify-start sm:bg-gray-100 sm:text-gray-900"
              />
              <QRCodeSection isVisibleOnSmall />
              <ProductInfo
                infoData={pixData?.product}
                additionalProductInfo={pixData?.bumps}
              />
              <HelpSection />
            </div>
          </main>
        </div>
        <div className="mb-12 mt-6 w-full max-w-[678px]">
          <div
            className={cn(
              "bg-white",
              "rounded-b-lg",
              "shadow-lg",
              "w-full",
              "h-auto",
              "p-4"
            )}
          >
            <ButtonBack
              subscriptionId={pixData?.subscription_id}
              productId={pixData?.product?.id}
              text={t("change_payment_method")}
              orderId={pixData?.order_id}
            />
          </div>
        </div>
      </>
    </div>
  );
}
