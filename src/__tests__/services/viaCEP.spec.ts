import axios from "axios";

import { viaCEP } from "@/services/viaCEP";
import { ViaCepResponse } from "@/types/viaCepResponse";

jest.mock("axios");

describe("viaCEP", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call onAddressFound when the data is valid", async () => {
    const cep = "12345678";
    const data: ViaCepResponse = {
      cep: "12345678",
      logradouro: "Rua Exemplo",
      complemento: "",
      bairro: "Bairro Exemplo",
      localidade: "Cidade Exemplo",
      uf: "EX",
      estado: "Exemplo",
      erro: false,
    };
    (axios.get as jest.Mock).mockResolvedValueOnce({ data });

    const onAddressFound = jest.fn();
    const onError = jest.fn();
    const onAddressNotFound = jest.fn();

    await viaCEP(cep, { onAddressFound, onError, onAddressNotFound });

    expect(axios.get).toHaveBeenCalledWith(
      `https://viacep.com.br/ws/${cep}/json/`
    );
    expect(onAddressFound).toHaveBeenCalledWith(data);
    expect(onError).not.toHaveBeenCalled();
    expect(onAddressNotFound).not.toHaveBeenCalled();
  });

  it("should call onAddressNotFound when data.erro is truthy", async () => {
    const cep = "12345678";
    const data: ViaCepResponse = {
      cep: "12345678",
      logradouro: "",
      complemento: "",
      bairro: "",
      localidade: "",
      uf: "",
      estado: "",
      erro: true,
    };
    (axios.get as jest.Mock).mockResolvedValueOnce({ data });

    const onAddressFound = jest.fn();
    const onError = jest.fn();
    const onAddressNotFound = jest.fn();

    await viaCEP(cep, { onAddressFound, onError, onAddressNotFound });

    expect(axios.get).toHaveBeenCalledWith(
      `https://viacep.com.br/ws/${cep}/json/`
    );
    expect(onAddressFound).not.toHaveBeenCalled();
    expect(onAddressNotFound).toHaveBeenCalled();
    expect(onError).not.toHaveBeenCalled();
  });

  it("should call onError when a specific error occurs in the request", async () => {
    const cep = "12345678";
    const specificError = new Error("Specific network error");
    (axios.get as jest.Mock).mockRejectedValueOnce(specificError);

    const onAddressFound = jest.fn();
    const onError = jest.fn();
    const onAddressNotFound = jest.fn();

    await viaCEP(cep, { onAddressFound, onError, onAddressNotFound });

    expect(axios.get).toHaveBeenCalledWith(
      `https://viacep.com.br/ws/${cep}/json/`
    );
    expect(onAddressFound).not.toHaveBeenCalled();
    expect(onAddressNotFound).not.toHaveBeenCalled();
    expect(onError).toHaveBeenCalled();
  });

  it("should catch and handle any type of error in the request", async () => {
    const cep = "12345678";
    const nonStandardError = { message: "Non-standard error object" };
    (axios.get as jest.Mock).mockRejectedValueOnce(nonStandardError);

    const onAddressFound = jest.fn();
    const onError = jest.fn();
    const onAddressNotFound = jest.fn();

    await viaCEP(cep, { onAddressFound, onError, onAddressNotFound });

    expect(axios.get).toHaveBeenCalledWith(
      `https://viacep.com.br/ws/${cep}/json/`
    );
    expect(onAddressFound).not.toHaveBeenCalled();
    expect(onAddressNotFound).not.toHaveBeenCalled();
    expect(onError).toHaveBeenCalled();
  });
});
