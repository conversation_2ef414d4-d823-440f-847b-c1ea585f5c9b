import { api } from "@/http/api-client";
import { SubscriptionService } from "@/services/subscription";
import { PaymentOption } from "@/types/paymentOptions";
import { SubscriptionRenewalPayload } from "@/utils/createOrderSubmitHandler/buildSubscriptionPayload";

jest.mock("@/http/api-client");

const mockApi = api as jest.Mocked<typeof api>;

describe("SubscriptionService", () => {
  let subscriptionService: SubscriptionService;

  const mockPayload: SubscriptionRenewalPayload = {
    subscription_id: "SB1B4H1AR",
    payment: {
      method: PaymentOption.CreditCard,
      credit_card: {
        token: "test-token",
        installments: 1,
        first_digits: "1234",
        last_digits: "5678",
        brand: "visa",
      },
    },
    signature: "test-signature",
  };

  const mockResponse = {
    data: {
      transaction: {
        id: "123",
        status: "approved",
        amount: 10000,
        payment_method: "credit_card",
        description: "1x de R$ 100,00",
      },
    },
  };

  beforeEach(() => {
    subscriptionService = new SubscriptionService();
    jest.clearAllMocks();
  });

  it("should renew subscription successfully with signature", async () => {
    mockApi.post.mockResolvedValueOnce(mockResponse);

    const result = await subscriptionService.renewSubscription(
      "SB1B4H1AR",
      mockPayload,
      "test-signature"
    );

    expect(mockApi.post).toHaveBeenCalledWith(
      "/v1/checkout/subscriptions/SB1B4H1AR",
      mockPayload,
      {
        params: { signature: "test-signature" },
      }
    );
    expect(result).toEqual(mockResponse.data);
  });

  it("should renew subscription successfully without signature", async () => {
    mockApi.post.mockResolvedValueOnce(mockResponse);

    const result = await subscriptionService.renewSubscription(
      "SB1B4H1AR",
      mockPayload
    );

    expect(mockApi.post).toHaveBeenCalledWith(
      "/v1/checkout/subscriptions/SB1B4H1AR",
      mockPayload,
      { params: {} }
    );
    expect(result).toEqual(mockResponse.data);
  });

  it("should handle API errors", async () => {
    const error = new Error("API Error");
    mockApi.post.mockRejectedValueOnce(error);

    await expect(
      subscriptionService.renewSubscription("SB1B4H1AR", mockPayload)
    ).rejects.toThrow("API Error");

    expect(mockApi.post).toHaveBeenCalledWith(
      "/v1/checkout/subscriptions/SB1B4H1AR",
      mockPayload,
      { params: {} }
    );
  });
});
