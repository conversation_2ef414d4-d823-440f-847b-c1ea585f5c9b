import { api } from "@/http/api-client";
import { fetchStatesList } from "@/services/statesList";
import { State } from "@/types/locations";

jest.mock("@/http/api-client", () => ({
  api: {
    get: jest.fn(),
  },
}));

describe("fetchStatesList", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return the list of states for a given country", async () => {
    const fakeStates: State[] = [
      { id: 1, name: "Estado 1", code: "E1" },
      { id: 2, name: "Estado 2", code: "E2" },
    ];

    (api.get as jest.Mock).mockResolvedValue({ data: fakeStates });

    const country = "br";
    const result = await fetchStatesList(country);

    expect(api.get).toHaveBeenCalledWith(`/locations/${country}/states`);
    expect(result).toEqual(fakeStates);
  });

  it("should propagate the error if the API call fails", async () => {
    const errorMessage = "Network error";
    (api.get as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const country = "us";

    await expect(fetchStatesList(country)).rejects.toThrow(errorMessage);
    expect(api.get).toHaveBeenCalledWith(`/locations/${country}/states`);
  });
});
