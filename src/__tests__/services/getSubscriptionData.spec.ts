import { getSubscriptionData } from "@/services/subscriptionApi";

describe("getSubscriptionData", () => {
  beforeEach(() => {
    jest.resetAllMocks();
    process.env.NEXT_PUBLIC_BASE_URL = "https://api.example.com";
  });

  it("should fetch without signature and default accept-language", async () => {
    const mockResponse = { status: "active" };
    (global.fetch as jest.Mock) = jest.fn().mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue(mockResponse),
    });

    const result = await getSubscriptionData("sub1");

    expect(global.fetch).toHaveBeenCalledWith(
      "https://api.example.com/v1/checkout/subscriptions/sub1",
      {
        cache: "no-store",
        headers: {
          "accept-language": "pt-BR",
          accept: "application/json",
        },
      }
    );
    expect(result).toEqual(mockResponse);
  });

  it("should append signature and use custom accept-language", async () => {
    const mockResponse = { status: "active" };
    (global.fetch as jest.Mock) = jest.fn().mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue(mockResponse),
    });

    const result = await getSubscriptionData("sub2", "sig123", "en-US");

    expect(global.fetch).toHaveBeenCalledWith(
      "https://api.example.com/v1/checkout/subscriptions/sub2?signature=sig123",
      {
        cache: "no-store",
        headers: {
          "accept-language": "en-US",
          accept: "application/json",
        },
      }
    );
    expect(result).toEqual(mockResponse);
  });

  it("should throw SUBSCRIPTION_NOT_FOUND when response is 404", async () => {
    (global.fetch as jest.Mock) = jest.fn().mockResolvedValue({
      ok: false,
      status: 404,
    });

    await expect(getSubscriptionData("missing")).rejects.toThrow(
      "SUBSCRIPTION_NOT_FOUND"
    );
  });

  it("should throw SUBSCRIPTION_FETCH_ERROR for non-404 errors", async () => {
    (global.fetch as jest.Mock) = jest.fn().mockResolvedValue({
      ok: false,
      status: 500,
    });

    await expect(getSubscriptionData("error")).rejects.toThrow(
      "SUBSCRIPTION_FETCH_ERROR"
    );
  });
});
