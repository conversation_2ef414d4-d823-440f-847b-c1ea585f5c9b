import { makeAwaitingPaymentData } from "@/factories/awaitingPayment";
import { api } from "@/http/api-client";
import { AwaitingPaymentService } from "@/services/awaiting-payment";
import { OrderStatus } from "@/types/orderResponse";

jest.mock("@/http/api-client", () => ({
  api: {
    get: jest.fn(),
  },
}));

describe("AwaitingPaymentService", () => {
  let awaitingPaymentService: AwaitingPaymentService;
  const mockApiGet = api.get as jest.Mock;

  beforeEach(() => {
    awaitingPaymentService = new AwaitingPaymentService();
    jest.clearAllMocks();
  });

  describe("getOrder", () => {
    const orderId = "order_123456789";
    const mockOrderData = makeAwaitingPaymentData("order", {
      order_id: orderId,
      transaction: {
        status: OrderStatus.PENDING,
        amount: 2990,
        description: "1x de R$29,90",
      },
    });

    it("should successfully get order awaiting payment data", async () => {
      mockApiGet.mockResolvedValue({ data: mockOrderData });

      const result = await awaitingPaymentService.getOrder(orderId);

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/orders/${orderId}/awaiting-payment`
      );
      expect(result).toEqual(mockOrderData);
    });

    it("should propagate error when API call fails", async () => {
      const mockError = new Error("Network error");
      mockApiGet.mockRejectedValue(mockError);

      await expect(awaitingPaymentService.getOrder(orderId)).rejects.toThrow(
        mockError
      );

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/orders/${orderId}/awaiting-payment`
      );
    });

    it("should handle API error responses", async () => {
      const apiError = {
        response: {
          status: 404,
          data: { message: "Order not found" },
        },
      };
      mockApiGet.mockRejectedValue(apiError);

      await expect(awaitingPaymentService.getOrder(orderId)).rejects.toEqual(
        apiError
      );
    });

    it("should call API with correct endpoint for different order IDs", async () => {
      const differentOrderId = "order_987654321";
      const mockData = makeAwaitingPaymentData("order", {
        order_id: differentOrderId,
      });
      mockApiGet.mockResolvedValue({ data: mockData });

      await awaitingPaymentService.getOrder(differentOrderId);

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/orders/${differentOrderId}/awaiting-payment`
      );
    });
  });

  describe("getSubscription", () => {
    const subscriptionCode = "SB1B4H16G";
    const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
      subscription_code: subscriptionCode,
      transaction: {
        status: OrderStatus.PENDING,
        amount: 3560,
        description: "1x de R$35,60",
      },
    });

    it("should successfully get subscription awaiting payment data", async () => {
      mockApiGet.mockResolvedValue({ data: mockSubscriptionData });

      const result =
        await awaitingPaymentService.getSubscription(subscriptionCode);

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/subscriptions/${subscriptionCode}/awaiting-payment`
      );
      expect(result).toEqual(mockSubscriptionData);
    });

    it("should propagate error when API call fails", async () => {
      const mockError = new Error("Subscription service unavailable");
      mockApiGet.mockRejectedValue(mockError);

      await expect(
        awaitingPaymentService.getSubscription(subscriptionCode)
      ).rejects.toThrow(mockError);

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/subscriptions/${subscriptionCode}/awaiting-payment`
      );
    });

    it("should handle subscription not found error", async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { message: "Subscription not found" },
        },
      };
      mockApiGet.mockRejectedValue(notFoundError);

      await expect(
        awaitingPaymentService.getSubscription(subscriptionCode)
      ).rejects.toEqual(notFoundError);
    });

    it("should handle unauthorized access error", async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { message: "Unauthorized access" },
        },
      };
      mockApiGet.mockRejectedValue(unauthorizedError);

      await expect(
        awaitingPaymentService.getSubscription(subscriptionCode)
      ).rejects.toEqual(unauthorizedError);
    });

    it("should call API with correct endpoint for different subscription codes", async () => {
      const differentSubscriptionCode = "SB9Z8Y7X6W";
      const mockData = makeAwaitingPaymentData("subscription", {
        subscription_code: differentSubscriptionCode,
      });
      mockApiGet.mockResolvedValue({ data: mockData });

      await awaitingPaymentService.getSubscription(differentSubscriptionCode);

      expect(mockApiGet).toHaveBeenCalledWith(
        `/v1/checkout/subscriptions/${differentSubscriptionCode}/awaiting-payment`
      );
    });
  });

  describe("Service instance", () => {
    it("should be an instance of AwaitingPaymentService", () => {
      expect(awaitingPaymentService).toBeInstanceOf(AwaitingPaymentService);
    });

    it("should have getOrder method", () => {
      expect(typeof awaitingPaymentService.getOrder).toBe("function");
    });

    it("should have getSubscription method", () => {
      expect(typeof awaitingPaymentService.getSubscription).toBe("function");
    });
  });

  describe("API integration", () => {
    it("should use GET method for order requests", async () => {
      const orderId = "test_order";
      mockApiGet.mockResolvedValue({ data: makeAwaitingPaymentData("order") });

      await awaitingPaymentService.getOrder(orderId);

      expect(mockApiGet).toHaveBeenCalledWith(
        expect.stringContaining("/awaiting-payment")
      );
    });

    it("should use GET method for subscription requests", async () => {
      const subscriptionCode = "test_subscription";
      mockApiGet.mockResolvedValue({
        data: makeAwaitingPaymentData("subscription"),
      });

      await awaitingPaymentService.getSubscription(subscriptionCode);

      expect(mockApiGet).toHaveBeenCalledWith(
        expect.stringContaining("/awaiting-payment")
      );
    });

    it("should return response data directly", async () => {
      const mockData = makeAwaitingPaymentData("order");
      mockApiGet.mockResolvedValue({ data: mockData });

      const result = await awaitingPaymentService.getOrder("test_id");

      expect(result).toBe(mockData);
    });
  });

  describe("Error handling edge cases", () => {
    it("should handle network timeout errors", async () => {
      const timeoutError = new Error("Request timeout");
      timeoutError.name = "TimeoutError";
      mockApiGet.mockRejectedValue(timeoutError);

      await expect(
        awaitingPaymentService.getOrder("test_order")
      ).rejects.toThrow("Request timeout");
    });

    it("should handle malformed response data", async () => {
      mockApiGet.mockResolvedValue({ data: null });

      const result = await awaitingPaymentService.getOrder("test_order");

      expect(result).toBeNull();
    });

    it("should handle empty string parameters", async () => {
      mockApiGet.mockResolvedValue({ data: makeAwaitingPaymentData("order") });

      await awaitingPaymentService.getOrder("");

      expect(mockApiGet).toHaveBeenCalledWith(
        "/v1/checkout/orders//awaiting-payment"
      );
    });
  });
});
