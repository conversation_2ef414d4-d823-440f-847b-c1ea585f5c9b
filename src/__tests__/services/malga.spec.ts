import axios from "axios";

import { MalgaService } from "@/services/malga";

jest.mock("axios");

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("MalgaService", () => {
  let malgaService: MalgaService;
  let mockAxiosInstance: jest.Mocked<typeof axios>;

  beforeEach(() => {
    mockAxiosInstance = {
      post: jest.fn(),
    } as unknown as jest.Mocked<typeof axios>;

    mockedAxios.create.mockReturnValue(mockAxiosInstance);
    malgaService = new MalgaService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("tokenizeCreditCard", () => {
    const sampleCardDetails = {
      cardHolderName: "John Doe",
      cardNumber: "****************",
      cardCvv: "123",
      cardExpirationDate: "12/24",
    };
    const clientId = "test-client-id";
    const apiKey = "test-api-key";

    it("should return a token when the request is successful", async () => {
      const mockResponse = { data: { token: "mocked-token" } };
      mockAxiosInstance.post.mockResolvedValueOnce(mockResponse);

      const result = await malgaService.tokenizeCreditCard(
        sampleCardDetails,
        clientId,
        apiKey
      );

      expect(result).toEqual({ token: "mocked-token" });
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        "/tokens",
        sampleCardDetails,
        {
          headers: {
            "X-Client-Id": clientId,
            "X-Api-Key": apiKey,
          },
        }
      );
    });

    it("should throw an error when the request fails", async () => {
      const mockError = new Error("Request failed");
      mockAxiosInstance.post.mockRejectedValueOnce(mockError);

      await expect(
        malgaService.tokenizeCreditCard(sampleCardDetails, clientId, apiKey)
      ).rejects.toThrow("Request failed");

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        "/tokens",
        sampleCardDetails,
        {
          headers: {
            "X-Client-Id": clientId,
            "X-Api-Key": apiKey,
          },
        }
      );
    });
  });
});
