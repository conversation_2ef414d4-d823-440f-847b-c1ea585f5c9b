import { api } from "@/http/api-client";
import { TheBankService } from "@/services";
import { MalgaService } from "@/services/malga";
import { CardProps } from "@/types/card";
import { OrchestratorResponse } from "@/types/fetchOrchestratorData";

jest.mock("@/http/api-client", () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
  },
}));

jest.mock("../../services/malga");

describe("TheBankService", () => {
  let theBankService: TheBankService;
  let malgaServiceMock: jest.Mocked<MalgaService>;

  beforeEach(() => {
    theBankService = new TheBankService();
    malgaServiceMock = new MalgaService() as jest.Mocked<MalgaService>;
    (theBankService as any).malgaService = malgaServiceMock;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("fetchOrchestratorData", () => {
    it("should return data when the request is successful", async () => {
      const mockData: OrchestratorResponse = {
        client_id: "123",
        public_key: "456",
        code: "789",
      };
      (api.get as jest.Mock).mockResolvedValueOnce({ data: mockData });

      const result =
        await theBankService.fetchOrchestratorData("test-orchestrator");

      expect(result).toEqual(mockData);
      expect(api.get).toHaveBeenCalledWith(
        "v1/checkout/orchestrators/test-orchestrator"
      );
    });

    it("should return response.data directly (coverage for lines 13-15)", async () => {
      const mockResponse = {
        data: {
          client_id: "test-client",
          public_key: "test-key",
          code: "test-code",
        },
        status: 200,
        statusText: "OK",
      };
      (api.get as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result =
        await theBankService.fetchOrchestratorData("orchestrator-123");

      expect(result).toBe(mockResponse.data);
      expect(result).not.toBe(mockResponse);
    });
  });

  describe("tokenizeCreditCard", () => {
    it("should call malgaService.tokenizeCreditCard with the correct parameters", async () => {
      const card: CardProps = {
        cardHolderName: "John Doe",
        cardNumber: "****************",
        cardCvv: "123",
        cardExpirationDate: "12/24",
      };
      const clientId = "test-client-id";
      const publicKey = "test-public-key";
      malgaServiceMock.tokenizeCreditCard.mockResolvedValueOnce({
        tokenId: "mocked-token",
      });

      const result = await theBankService.tokenizeCreditCard(
        card,
        clientId,
        publicKey
      );

      expect(result).toEqual({ tokenId: "mocked-token" });
      expect(malgaServiceMock.tokenizeCreditCard).toHaveBeenCalledWith(
        card,
        clientId,
        publicKey
      );
    });
  });

  describe("fetchCreditCardList", () => {
    it("should return the credit card list from the API", async () => {
      const mockCreditCardList = [
        {
          brand: "Visa",
          icon_url: "https://example.com/visa.png",
          priority: 1,
        },
        {
          brand: "MasterCard",
          icon_url: "https://example.com/mastercard.png",
          priority: 2,
        },
      ];
      (api.get as jest.Mock).mockResolvedValueOnce({
        data: mockCreditCardList,
      });

      const result = await theBankService.fetchCreditCardList();

      expect(result).toEqual(mockCreditCardList);
      expect(api.get).toHaveBeenCalledWith("/brands");
    });

    it("should return response.data directly (coverage for lines 31-33)", async () => {
      const mockResponse = {
        data: [
          {
            brand: "Amex",
            icon_url: "https://example.com/amex.png",
            priority: 3,
          },
          {
            brand: "Elo",
            icon_url: "https://example.com/elo.png",
            priority: 4,
          },
        ],
        status: 200,
        statusText: "OK",
      };
      (api.get as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await theBankService.fetchCreditCardList();

      expect(result).toBe(mockResponse.data);
      expect(result).not.toBe(mockResponse);
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe("fetchProduct", () => {
    it("should return product data when the request is successful", async () => {
      const productId = "product-123";
      const mockProductData = {
        id: productId,
        name: "Test Product",
        price: 99.99,
      };
      (api.get as jest.Mock).mockResolvedValueOnce({ data: mockProductData });

      const result = await theBankService.fetchProduct(productId);

      expect(result).toEqual(mockProductData);
      expect(api.get).toHaveBeenCalledWith(
        `/v1/checkout/products/${productId}`
      );
    });
  });
});
