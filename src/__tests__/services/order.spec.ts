import { api } from "@/http/api-client";
import { OrderService } from "@/services/order";
import { OrderPayload } from "@/types/order";

jest.mock("@/http/api-client", () => ({
  api: {
    post: jest.fn(),
  },
}));

describe("OrderService", () => {
  let orderService: OrderService;
  const mockPayload: OrderPayload = {
    products: [],
    buyer: {
      name: "<PERSON>",
      email: "<EMAIL>",
      confirm_email: "<EMAIL>",
      phone: {
        ddi: "55",
        number: "1234567890",
      },
      credit_card: {
        card_token: "1234567890",
        first_digits: "1234",
        last_digits: "5678",
        brand: "visa",
      },
    },
    main_product_id: "",
    payment: {
      method: "credit_card",
      card_token: "1234567890",
      installments: 1,
    },
  };
  const mockResponse = { data: { id: "123", status: "created" } };

  beforeEach(() => {
    orderService = new OrderService();
    jest.clearAllMocks();
  });

  describe("createOrder", () => {
    it("should successfully create an order", async () => {
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await orderService.createOrder(mockPayload);

      expect(api.post).toHaveBeenCalledWith("/v1/checkout/orders", mockPayload);
      expect(result).toEqual(mockResponse.data);
    });

    it("should propagate an error if order creation fails", async () => {
      const mockError = new Error("Failed to create order");
      (api.post as jest.Mock).mockRejectedValue(mockError);

      await expect(orderService.createOrder(mockPayload)).rejects.toThrow(
        mockError
      );
    });
  });

  describe("retryOrder", () => {
    const mockOrderId = "123";

    it("should successfully retry an order", async () => {
      (api.post as jest.Mock).mockResolvedValue(mockResponse);

      const result = await orderService.retryOrder(mockOrderId, mockPayload);

      expect(api.post).toHaveBeenCalledWith(
        `/v1/checkout/orders/${mockOrderId}/checkout`,
        mockPayload
      );
      expect(result).toEqual(mockResponse.data);
    });

    it("should propagate an error if retrying the order fails", async () => {
      const mockError = new Error("Failed to retry order");
      (api.post as jest.Mock).mockRejectedValue(mockError);

      await expect(
        orderService.retryOrder(mockOrderId, mockPayload)
      ).rejects.toThrow(mockError);
    });
  });
});
