import { getDocument<PERSON><PERSON><PERSON> } from "@/factories/documentHandlerFactory";
import { DocumentType } from "@/types/document";
import { formatToCNPJ, formatToCPF } from "@/utils/formatting";
import { isCNPJ, isCPF } from "@/utils/validations";

describe("getDocumentHandler", () => {
  it("should return the CPF handler with correct validate and format functions", () => {
    const handler = getDocumentHandler(DocumentType.CPF);

    expect(handler.validate).toBe(isCPF);
    expect(handler.format).toBe(formatToCPF);
  });

  it("should return the CNPJ handler with correct validate and format functions", () => {
    const handler = getDocumentHandler(DocumentType.CNPJ);

    expect(handler.validate).toBe(isCNPJ);
    expect(handler.format).toBe(formatToCNPJ);
  });

  it("should throw an error if the document type is unsupported", () => {
    expect(() => getDocumentHandler("INVALID_TYPE" as DocumentType)).toThrow(
      "Unsupported document type: INVALID_TYPE"
    );
  });
});
