import { Locale } from "@/support/lang/lang";

describe("Locale", () => {
  test.each([
    ["en", "US", "en-US"],
    ["es", "ES", "es-ES"],
    ["pt", "BR", "pt-BR"],
    ["fr", "FR", "fr-FR"],
  ])(
    "should return %s-%s for langCode %s and countryCode %s",
    (langCode, countryCode, expected) => {
      const locale = new Locale(langCode, countryCode);
      expect(locale.toString()).toBe(expected);
    }
  );

  test.each([
    ["en-US", "en", "US"],
    ["es-ES", "es", "ES"],
    ["pt-BR", "pt", "BR"],
    ["fr-FR", "fr", "FR"],
  ])(
    "should create Locale with langCode %s and countryCode %s from %s",
    (localeString, expectedLangCode, expectedCountryCode) => {
      const locale = Locale.fromString(localeString);
      expect(locale.langCode).toBe(expectedLangCode);
      expect(locale.countryCode).toBe(expectedCountryCode);
    }
  );
});
