import { defaultLangs, globalDefaultLang, langs } from "@/support/lang/config";

describe("Config", () => {
  test.each([
    ["en", "en-US"],
    ["es", "es-ES"],
    ["pt", "pt-BR"],
  ])("should map %s to %s", (key, expectedValue) => {
    expect(defaultLangs[key]).toBe(expectedValue);
  });

  test("ensure en-US as the global default locale'", () => {
    expect(globalDefaultLang).toBe("en-US");
  });

  test.each([["pt-BR"], ["en-US"], ["es-ES"]])(
    "ensure that %s is a supported locale",
    lang => {
      expect(langs).toContain(lang);
    }
  );
});
