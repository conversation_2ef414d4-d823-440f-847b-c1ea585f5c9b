/* eslint-disable @typescript-eslint/no-var-requires */
import { Locale } from "@/support/lang/lang";

describe("lang strategy", () => {
  beforeEach(() => {
    jest.resetModules();
  });

  test.each(["en-US", "pt-BR", "es-ES", "xu-XA"])(
    "should return %s when the locale is supported",
    (lang: string) => {
      jest.doMock("@/support/lang/config", () => ({
        langs: [lang],
      }));
      const { determineLocale } = require("@/support/lang/strategy");

      const locale = Locale.fromString(lang);
      const result = determineLocale(locale);
      expect(result).toBe(lang);
    }
  );

  test.each([
    ["en-US", "en-UK"],
    ["es-ES", "es-MX"],
    ["pt-BR", "pt-PT"],
  ])(
    "should return %s when %s is not supported",
    (defaultLang: string, pretendLang: string) => {
      jest.doMock("@/support/lang/config", () => ({
        langs: [defaultLang],
        defaultLangs: {
          [defaultLang.split("-")[0]]: defaultLang,
        },
        globalDefaultLang: "en-US",
      }));
      const { determineLocale } = require("@/support/lang/strategy");

      const locale = Locale.fromString(pretendLang);
      const result = determineLocale(locale);
      expect(result).toBe(defaultLang);
    }
  );

  test.each([["es-UK"], ["es-MX"], ["fr-FR"], ["pt-BR"]])(
    "should return en-US when the locale is not supported",
    (lang: string) => {
      jest.doMock("@/support/lang/config", () => ({
        langs: ["en-US"],
        defaultLangs: {
          en: "en-US",
        },
        globalDefaultLang: "en-US",
      }));

      const { determineLocale } = require("@/support/lang/strategy");

      const locale = Locale.fromString(lang);
      const result = determineLocale(locale);
      expect(result).toBe("en-US");
    }
  );
});
