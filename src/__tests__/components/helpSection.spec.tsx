import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import HelpSection from "@/components/HelpSection";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

jest.mock("next/link", () => {
  const Link = ({ children, href }: { children: ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  );
  Link.displayName = "MockedNextLink";
  return Link;
});

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("HelpSection Component", () => {
  it.each(pixMessages)(
    "should render the HelpSection component with translations in %s",
    (locale, messages) => {
      renderWithIntl(<HelpSection />, locale, messages);

      const cantFinalizePaymentText = messages.pix_page.cant_finalize_payment;
      expect(screen.getByText(cantFinalizePaymentText)).toBeInTheDocument();

      const accessHelpPageText = messages.pix_page.access_help_page;
      const linkElement = screen.getByText(accessHelpPageText);
      expect(linkElement).toBeInTheDocument();
      expect(linkElement).toHaveAttribute(
        "href",
        "https://site.themembers.com.br/thebank/comprador/termos-de-uso"
      );
    }
  );
});
