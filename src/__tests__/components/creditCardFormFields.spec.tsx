import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { CreditCardFormFields } from "@/components/CreditCardFormFields";
import { BuyerFormValues } from "@/schemas/createBuyerSchema";
import { Locales } from "@/types/locales";

import { buyerFormMessages } from "../datasets/buyerFormMessages";

if (global.PointerEvent === undefined) {
  class PointerEvent extends MouseEvent {
    public height = 0;
    public isPrimary = false;
    public pointerId = 1;
    public pointerType = "mouse";
    public pressure = 0;
    public tangentialPressure = 0;
    public tiltX = 0;
    public tiltY = 0;
    public twist = 0;
    public width = 0;

    constructor(type: string, props?: PointerEventInit) {
      super(type, props);
    }
  }
  global.PointerEvent =
    PointerEvent as unknown as typeof globalThis.PointerEvent;
}

function renderCreditCardFormFieldsWithSubmit(
  locale: string,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const methods = useForm<BuyerFormValues>({
      defaultValues: {
        name: "",
        email: "",
        document: undefined,
        phone: undefined,
        cardNumber: "",
        cardHolderName: "",
        cardCvv: "",
        cardExpirationDateMonth: "",
        cardExpirationDateYear: "",
        instalment: undefined,
      },
    });
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          <FormProvider {...methods}>
            <form onSubmit={methods.handleSubmit(jest.fn())}>
              <CreditCardFormFields
                months={[{ id: "01", name: "01" }]}
                years={[{ id: "2025", name: "2025" }]}
                instalments={[
                  { installments: "1", total: "100", description: "1x" },
                ]}
                setSelectedInstalmentDescription={jest.fn()}
                setSelectedInstallment={jest.fn()}
              />
              <button type="submit">{messages.buyer_form.submit_button}</button>
            </form>
          </FormProvider>
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const formMethods = useForm();
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          <FormProvider {...formMethods}>{component}</FormProvider>
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("CreditCardFormFields Component", () => {
  beforeEach(() => {
    global.ResizeObserver = class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };

    Element.prototype.scrollIntoView = jest.fn();
  });

  it.each(buyerFormMessages)(
    "should render help icon when hovering over it in %s",
    async (locale, messages) => {
      renderCreditCardFormFieldsWithSubmit(locale, messages);

      const helpIcon = screen.getByTestId("cvv-help-icon");
      fireEvent.mouseOver(helpIcon);

      expect(helpIcon).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should render CreditCardFormFields with translations in %s",
    (locale, messages) => {
      const months = [
        { id: "01", name: "01" },
        { id: "02", name: "02" },
      ];
      const years = [
        { id: "2024", name: "2024" },
        { id: "2025", name: "2025" },
      ];
      const instalments = [
        { installments: "1", total: "100", description: "1x de 100" },
        { installments: "2", total: "50", description: "2x de 50" },
      ];
      const setSelectedInstalmentDescription = jest.fn();

      const setSelectedInstallment = jest.fn();

      renderWithIntl(
        <CreditCardFormFields
          months={months}
          years={years}
          instalments={instalments}
          setSelectedInstalmentDescription={setSelectedInstalmentDescription}
          setSelectedInstallment={setSelectedInstallment}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.card_number)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.buyer_form.owner_card)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.buyer_form.cvv_card)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.buyer_form.installment)
      ).toBeInTheDocument();

      expect(
        screen.getByPlaceholderText("0000 0000 0000 0000")
      ).toBeInTheDocument();
      expect(screen.getByPlaceholderText("123")).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should show errors when fields are empty and submitted in %s",
    async (locale, messages) => {
      renderCreditCardFormFieldsWithSubmit(locale, messages);

      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(
          screen.getByText(messages.buyer_form.card_number)
        ).toBeInTheDocument();
        expect(
          screen.getByText(messages.buyer_form.cvv_card)
        ).toBeInTheDocument();
      });
    }
  );

  it("should update the installment description when an installment is selected", async () => {
    const setSelectedInstalmentDescription = jest.fn();
    const instalments = [
      { installments: "1", total: "100", description: "1x de R$100" },
      { installments: "2", total: "50", description: "2x de R$50" },
    ];

    const setSelectedInstallment = jest.fn();

    renderWithIntl(
      <CreditCardFormFields
        months={[{ id: "01", name: "01" }]}
        years={[{ id: "2025", name: "2025" }]}
        instalments={instalments}
        setSelectedInstalmentDescription={setSelectedInstalmentDescription}
        setSelectedInstallment={setSelectedInstallment}
      />,
      Locales.PT_BR,
      buyerFormMessages[0][1]
    );

    const selectTrigger = screen.getByTestId("installment-select-trigger");
    fireEvent.click(selectTrigger);

    const option = await screen.findAllByText("1x de R$100");
    fireEvent.click(option[0]);

    expect(setSelectedInstalmentDescription).toHaveBeenCalledWith(
      "1x de R$100"
    );
  });

  it("should call setSelectedInstalmentDescription with the correct value when an installment is selected", async () => {
    const setSelectedInstalmentDescription = jest.fn();
    const instalments = [
      { installments: "1", total: "100", description: "1x de R$100" },
      { installments: "2", total: "50", description: "2x de R$50" },
    ];
    const setSelectedInstallment = jest.fn();
    renderWithIntl(
      <CreditCardFormFields
        months={[{ id: "01", name: "01" }]}
        years={[{ id: "2025", name: "2025" }]}
        instalments={instalments}
        setSelectedInstalmentDescription={setSelectedInstalmentDescription}
        setSelectedInstallment={setSelectedInstallment}
      />,
      Locales.PT_BR,
      buyerFormMessages[0][1]
    );

    const selectInstalment = screen.getByTestId("installment-select-trigger");
    fireEvent.click(selectInstalment);

    const option = await screen.findAllByText("1x de R$100");
    fireEvent.click(option[0]);

    expect(setSelectedInstalmentDescription).toHaveBeenCalledWith(
      "1x de R$100"
    );
  });

  it("should initialize with first installment value automatically", () => {
    const setSelectedInstalmentDescription = jest.fn();
    const setSelectedInstallment = jest.fn();
    const instalments = [
      { installments: "1", total: "100", description: "1x de R$100" },
      { installments: "2", total: "50", description: "2x de R$50" },
    ];

    renderWithIntl(
      <CreditCardFormFields
        months={[{ id: "01", name: "01" }]}
        years={[{ id: "2025", name: "2025" }]}
        instalments={instalments}
        setSelectedInstalmentDescription={setSelectedInstalmentDescription}
        setSelectedInstallment={setSelectedInstallment}
      />,
      Locales.PT_BR,
      buyerFormMessages[0][1]
    );

    expect(setSelectedInstalmentDescription).toHaveBeenCalledWith("2x de R$50");
    expect(setSelectedInstallment).toHaveBeenCalledWith("2");
  });
});
