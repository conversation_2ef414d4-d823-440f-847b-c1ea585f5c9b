import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { SubscriptionFooter } from "@/components/SubscriptionFooter";
import { Locales } from "@/types/locales";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  return render(
    <IntlProvider locale={locale} messages={messages}>
      {component}
    </IntlProvider>
  );
}

const mockMessages = {
  home: {
    purchase_disclaimer:
      "Ao concluir a compra, você declara que leu e concorda (i) que o checkout TheMembers está processando este pedido em nome de {organization} e não possui responsabilidade pelo conteúdo e/ou faz controle prévio deste; (ii) com nossa",
    privacy_policy: "política de privacidade",
    and: "e com os",
    terms_of_use: "termos de uso",
    purchase_disclaimer_credit_card: "O valor parcelado possui acréscimo.",
    help_text: "Não consegue finalizar essa compra?",
    help_page: "Acesse nossa página de ajuda",
    all_rights_reserved: "Todos os direitos reservados",
  },
};

describe("SubscriptionFooter Component", () => {
  it("should render the footer with organization name", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText(/Test Organization/)).toBeInTheDocument();
  });

  it("should render privacy policy link", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    const privacyLink = screen.getByText("política de privacidade");
    expect(privacyLink).toBeInTheDocument();
    expect(privacyLink.closest("a")).toHaveAttribute(
      "href",
      "https://site.themembers.com.br/thebank/infoprodutor/termos-de-uso"
    );
  });

  it("should render terms of use link", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    const termsLink = screen.getByText("termos de uso");
    expect(termsLink).toBeInTheDocument();
    expect(termsLink.closest("a")).toHaveAttribute(
      "href",
      "https://site.themembers.com.br/thebank/comprador/termos-de-uso"
    );
  });

  it("should render help page link", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    const helpLink = screen.getByText("Acesse nossa página de ajuda");
    expect(helpLink).toBeInTheDocument();
    expect(helpLink.closest("a")).toHaveAttribute(
      "href",
      "https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
    );
  });

  it("should render credit card disclaimer", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    expect(
      screen.getByText("O valor parcelado possui acréscimo.")
    ).toBeInTheDocument();
  });

  it("should render logo image", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    const logo = screen.getByAltText("Checkout TheMembers");
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute("src", "/images/logo.svg");
  });

  it("should render copyright text", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    expect(
      screen.getByText(/© 2025 - Todos os direitos reservados/)
    ).toBeInTheDocument();
  });

  it("should render all links with correct styling", () => {
    renderWithIntl(
      <SubscriptionFooter organization="Test Organization" />,
      Locales.PT_BR,
      mockMessages
    );

    const links = screen.getAllByRole("link");
    links.forEach(link => {
      expect(link).toHaveClass(
        "text-xs",
        "font-medium",
        "leading-4",
        "text-blue-700"
      );
    });
  });
});
