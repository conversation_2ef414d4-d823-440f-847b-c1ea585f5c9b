import { fireEvent, render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";

import { CreditCardErrorModal } from "@/components/CreditCardErrorModal";
import { Locales } from "@/types/locales";

import { creditCardMessages } from "../datasets/creditCardMessages";

const defaultLocale = "pt-BR";
const defaultMessages = creditCardMessages[0][1] as Record<string, any>;

describe("CreditCardErrorModal", () => {
  it.each(creditCardMessages)(
    "should render modal with correct translation in %s",
    (locale: Locales, messages: Record<string, any>) => {
      const onClose = jest.fn();

      render(
        <IntlProvider locale={locale} messages={messages}>
          <CreditCardErrorModal isOpen={true} onClose={onClose} />
        </IntlProvider>
      );

      expect(
        screen.getByText(messages.credit_card_page.error.title)
      ).toBeInTheDocument();

      expect(
        screen.getByText(messages.credit_card_page.error.description)
      ).toBeInTheDocument();

      const button = screen.getByRole("button", {
        name: messages.credit_card_page.error.button,
      });
      expect(button).toBeInTheDocument();
    }
  );

  it("should call onClose when close button is clicked in %s", () => {
    const onClose = jest.fn();

    render(
      <IntlProvider locale={defaultLocale} messages={defaultMessages}>
        <CreditCardErrorModal isOpen={true} onClose={onClose} />
      </IntlProvider>
    );

    const closeButton = screen.getByRole("button", { name: /close/i });
    fireEvent.click(closeButton);

    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it("should not render modal when isOpen is false", () => {
    const onClose = jest.fn();

    render(
      <IntlProvider locale={defaultLocale} messages={defaultMessages}>
        <CreditCardErrorModal isOpen={false} onClose={onClose} />
      </IntlProvider>
    );

    expect(
      screen.queryByText(defaultMessages.credit_card_page.error.title)
    ).not.toBeInTheDocument();
  });

  it("should not fail if onClose is not provided", () => {
    render(
      <IntlProvider locale={defaultLocale} messages={defaultMessages}>
        <CreditCardErrorModal isOpen={true} />
      </IntlProvider>
    );

    expect(
      screen.getByText(defaultMessages.credit_card_page.error.title)
    ).toBeInTheDocument();

    const button = screen.getByRole("button", {
      name: defaultMessages.credit_card_page.error.button,
    });
    fireEvent.click(button);

    expect(button).toBeInTheDocument();
  });
});
