import { render, screen } from "@testing-library/react";
import { NextIntlClientProvider } from "next-intl";

import { TrialInfo } from "@/components/SubscriptionInfo";

const mockMessages = {
  buyer_form: {
    subscription: {
      renews_every: "Renews every",
      month: "month",
      months: "months",
    },
    free_trial: "Free trial of {days} days",
  },
};

const mockSubscriptionWithTrial = {
  periodicity: "monthly" as const,
  interval: 1,
  trial_days: 7,
};

const mockSubscriptionWithoutTrial = {
  periodicity: "monthly" as const,
  interval: 1,
};

const mockSubscriptionWithLongTrial = {
  periodicity: "monthly" as const,
  interval: 3,
  trial_days: 30,
};

describe("TrialInfo Component", () => {
  it("should render trial button when trial_days is provided", () => {
    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={mockSubscriptionWithTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.getByText("Free trial of 7 days")).toBeInTheDocument();
    expect(screen.getByRole("button")).toHaveClass(
      "inline-block rounded-full bg-emerald-700 px-2 py-1 text-xs leading-none text-white"
    );
  });

  it("should not render trial button when trial_days is not provided", () => {
    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={mockSubscriptionWithoutTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.queryByText(/Free trial/)).not.toBeInTheDocument();
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("should not render trial button when trial_days is 0", () => {
    const subscriptionWithZeroTrial = {
      ...mockSubscriptionWithTrial,
      trial_days: 0,
    };

    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={subscriptionWithZeroTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.queryByText(/Free trial/)).not.toBeInTheDocument();
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("should not render trial button when trial_days is negative", () => {
    const subscriptionWithNegativeTrial = {
      ...mockSubscriptionWithTrial,
      trial_days: -5,
    };

    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={subscriptionWithNegativeTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.queryByText(/Free trial/)).not.toBeInTheDocument();
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("should render correct trial days for different values", () => {
    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={mockSubscriptionWithLongTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.getByText("Free trial of 30 days")).toBeInTheDocument();
  });

  it("should apply custom className", () => {
    const { container } = render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo
          subscription={mockSubscriptionWithTrial}
          className="custom-class"
        />
      </NextIntlClientProvider>
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should render empty div when no trial_days and no className", () => {
    const { container } = render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={mockSubscriptionWithoutTrial} />
      </NextIntlClientProvider>
    );

    expect(container.firstChild).toBeEmptyDOMElement();
    expect(container.firstChild).toHaveAttribute("class", "");
  });

  it("should handle different subscription intervals (component should accept any interval)", () => {
    const subscriptionWithDifferentInterval = {
      periodicity: "yearly" as const,
      interval: 12,
      trial_days: 14,
    };

    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={subscriptionWithDifferentInterval} />
      </NextIntlClientProvider>
    );

    expect(screen.getByText("Free trial of 14 days")).toBeInTheDocument();
  });

  it("should handle different periodicity values", () => {
    const subscriptionWithWeeklyPeriodicity = {
      periodicity: "weekly" as const,
      interval: 2,
      trial_days: 5,
    };

    render(
      <NextIntlClientProvider locale="en" messages={mockMessages}>
        <TrialInfo subscription={subscriptionWithWeeklyPeriodicity} />
      </NextIntlClientProvider>
    );

    expect(screen.getByText("Free trial of 5 days")).toBeInTheDocument();
  });

  it("should use translation function correctly", () => {
    const customMessages = {
      buyer_form: {
        free_trial: "Teste gratuito de {days} dias",
      },
    };

    render(
      <NextIntlClientProvider locale="pt" messages={customMessages}>
        <TrialInfo subscription={mockSubscriptionWithTrial} />
      </NextIntlClientProvider>
    );

    expect(screen.getByText("Teste gratuito de 7 dias")).toBeInTheDocument();
  });
});
