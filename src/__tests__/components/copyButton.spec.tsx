import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { CopyButton } from "@/components/CopyButton";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

const pixDataMock: any = {
  payment_method: "pix",
  order_id: "123456789",
  status: "success",
  pix: {
    amount: 159.99,
    expires_in: 300,
    qr_code: "https://example.com/qr/code",
    copy_paste: "exemple.com",
  },
  product: {
    id: "123456789",
    title: "Pix",
    description: "Pix description",
    platform: {
      name: "Pix",
    },
  },
};

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("CopyButton Component", () => {
  it.each(pixMessages)(
    "should render the CopyButton component with translations in %s",
    (locale, messages) => {
      renderWithIntl(
        <CopyButton
          text={messages.pix_page.copy_pix_code}
          pixData={pixDataMock}
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.pix_page.copy_pix_code);
      expect(button).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should trigger onClick event when CopyButton is clicked in %s",
    (locale, messages) => {
      const handleClick = jest.fn();
      renderWithIntl(
        <CopyButton
          text={messages.pix_page.copy_pix_code}
          onClick={handleClick}
          pixData={pixDataMock}
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.pix_page.copy_pix_code);
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    }
  );

  it.each(pixMessages)(
    "should copy text and change button text when clicked in %s",
    async (locale, messages) => {
      renderWithIntl(
        <CopyButton
          pixData={pixDataMock}
          text={messages.pix_page.copy_pix_code}
          copiedTextKey="copied_text"
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.pix_page.copy_pix_code);

      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValueOnce(undefined),
        },
      });

      fireEvent.click(button);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
        expect.any(String)
      );

      await waitFor(() =>
        expect(
          screen.getByText(messages.pix_page.copied_text)
        ).toBeInTheDocument()
      );

      await waitFor(
        () =>
          expect(
            screen.getByText(messages.pix_page.copy_pix_code)
          ).toBeInTheDocument(),
        {
          timeout: 7000,
        }
      );
    },
    10000
  );
});
