import { fireEvent, render, screen } from "@testing-library/react";

import { PaymentOption } from "@/types/paymentOptions";

import { PaymentSelectCard } from "../../components/PaymentSelectCard";

jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => <img {...props} />,
}));

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

describe("PaymentSelectCard", () => {
  const mockSetSelectedOption = jest.fn();
  const defaultProps = {
    title: "credit_card" as PaymentOption,
    setSelectedOption: mockSetSelectedOption,
    selectedOption: "credit_card" as PaymentOption,
    getCardStyles: (value: string) => `test-style-${value}`,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render correctly with default props", () => {
    render(<PaymentSelectCard {...defaultProps} />);

    expect(screen.getByText("credit_card")).toBeInTheDocument();
    expect(screen.getByAltText("credit_card")).toBeInTheDocument();
  });

  it("should call setSelectedOption when clicked", () => {
    render(<PaymentSelectCard {...defaultProps} />);

    const card = screen.getByRole("button");
    fireEvent.click(card);

    expect(mockSetSelectedOption).toHaveBeenCalledWith("credit_card");
  });

  it("should display the selection indicator when selectedOption matches the title", () => {
    render(<PaymentSelectCard {...defaultProps} />);

    const selectionIndicator = screen.getByTestId("selection-indicator");
    expect(selectionIndicator).toBeInTheDocument();
  });

  it("should not display the selection indicator when selectedOption does not match the title", () => {
    render(
      <PaymentSelectCard
        {...defaultProps}
        selectedOption={"pix" as PaymentOption}
      />
    );

    const selectionIndicator = screen.queryByTestId("selection-indicator");
    expect(selectionIndicator).not.toBeInTheDocument();
  });

  it("should apply styles correctly through the getCardStyles function", () => {
    render(<PaymentSelectCard {...defaultProps} />);

    const card = screen.getByRole("button");
    expect(card).toHaveClass("test-style-credit_card");
  });
});
