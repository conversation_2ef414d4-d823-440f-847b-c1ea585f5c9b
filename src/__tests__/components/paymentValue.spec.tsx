import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import PaymentValue from "@/components/PaymentValue";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("PaymentValue Component", () => {
  it.each(pixMessages)(
    "should render the PaymentValue component with translations in %s",
    (locale, messages) => {
      const amountValue = 123.45;
      renderWithIntl(<PaymentValue amount={amountValue} />, locale, messages);

      expect(screen.getByText(messages.pix_page.amount)).toBeInTheDocument();

      expect(screen.getByText(String(amountValue))).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should render the PaymentValue component with string amount in %s",
    (locale, messages) => {
      const amountValue = "R$ 123,45";
      renderWithIntl(<PaymentValue amount={amountValue} />, locale, messages);

      expect(screen.getByText(messages.pix_page.amount)).toBeInTheDocument();

      expect(screen.getByText(amountValue)).toBeInTheDocument();
    }
  );
});
