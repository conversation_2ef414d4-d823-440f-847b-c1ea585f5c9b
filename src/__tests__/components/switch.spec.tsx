import "@testing-library/jest-dom";

import { fireEvent, render, screen } from "@testing-library/react";

import { Switch } from "@/components/ui/Switch";

describe("Switch Component", () => {
  it("should render the Switch component", () => {
    render(<Switch />);

    const switchElement = screen.getByRole("switch");
    expect(switchElement).toBeInTheDocument();
  });

  it("should toggle the checked state when clicked", () => {
    render(<Switch />);

    const switchElement = screen.getByRole("switch");
    expect(switchElement).toHaveAttribute("data-state", "unchecked");

    fireEvent.click(switchElement);
    expect(switchElement).toHaveAttribute("data-state", "checked");

    fireEvent.click(switchElement);
    expect(switchElement).toHaveAttribute("data-state", "unchecked");
  });

  it("should not toggle when disabled", () => {
    render(<Switch disabled />);

    const switchElement = screen.getByRole("switch");
    expect(switchElement).toHaveAttribute("data-state", "unchecked");

    fireEvent.click(switchElement);
    expect(switchElement).toHaveAttribute("data-state", "unchecked");
  });
});
