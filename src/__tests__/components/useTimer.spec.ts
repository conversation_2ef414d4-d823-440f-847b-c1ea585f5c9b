import { act, renderHook } from "@testing-library/react";

import useTimer from "@/hooks/useTimer";

jest.useFakeTimers();

describe("useTimer hook", () => {
  it("should initialize with the given initial time", () => {
    const { result } = renderHook(() => useTimer(120));
    expect(result.current.timeInSeconds).toBe(120);
  });

  it("should decrease time every second", () => {
    const { result } = renderHook(() => useTimer(3));

    act(() => {
      jest.advanceTimersByTime(1000);
    });
    expect(result.current.timeInSeconds).toBe(2);

    act(() => {
      jest.advanceTimersByTime(1000);
    });
    expect(result.current.timeInSeconds).toBe(1);

    act(() => {
      jest.advanceTimersByTime(1000);
    });
    expect(result.current.timeInSeconds).toBe(0);
  });

  it("should not decrease below 0", () => {
    const { result } = renderHook(() => useTimer(1));

    act(() => {
      jest.advanceTimersByTime(2000);
    });
    expect(result.current.timeInSeconds).toBe(0);
  });

  it("should format time correctly for minutes format", () => {
    const { result } = renderHook(() => useTimer(120));
    expect(result.current.formatTime(120)).toBe("02:00");
  });

  it("should format time correctly for hours format", () => {
    const { result } = renderHook(() => useTimer(3661));
    expect(result.current.formatTime(3661)).toBe("1h 01:01");
  });

  it("should format time correctly for days format", () => {
    const { result } = renderHook(() => useTimer(90061));
    expect(result.current.formatTime(90061)).toBe("1d 1h 01:01");
  });

  it("should return '00:00' for non-positive time values", () => {
    const { result } = renderHook(() => useTimer(0));
    expect(result.current.formatTime(0)).toBe("00:00");
  });
});
