import { render, screen } from "@testing-library/react";

import { ProductOrderDetails } from "@/components/ProductOrderDetails";
import { PaymentOption } from "@/types/paymentOptions";

const mockCallback = jest.fn();
const mockChannel = jest.fn(() => ({
  listen: jest.fn(({ callBack }) => {
    mockCallback.mockImplementation(callBack);
  }),
}));

beforeEach(() => {
  window.Echo = {
    channel: mockChannel,
  } as any;
  jest.clearAllMocks();
});

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => {
    const translations: { [key: string]: string } = {
      product_order: "Order",
      payment_info: "Payment Information",
      payment_info_value: "Payment Value",
      create_access: "We are creating your access...",
      access_product: "Access my product",
      payment_info_success: "Payment Successful",
      payment_info_boleto: "Payment via Boleto",
    };
    return translations[key] || key;
  },
}));

jest.mock("../../components/PaymentInfoSuccessCreditCard", () => ({
  PaymentInfoSuccessCreditCard: ({ data }: { data: any }) => (
    <div data-testid="credit-card-info">{data.cardNumber}</div>
  ),
}));

jest.mock("../../components/ProductInfo", () => ({
  __esModule: true,
  default: () => <div data-testid="product-info">Product Info Mock</div>,
}));

const mockCreateSocketConnection = jest.fn();
jest.mock("@/ws/socket", () => ({
  createSocketConnection: () => mockCreateSocketConnection(),
}));

describe("ProductOrderDetails", () => {
  const mockData: any = {
    order_id: "123456",
    payment_method: PaymentOption.CreditCard,
    product: {},
    payment: {
      description: "12x R$ 100,00",
      cardNumber: "**** **** **** 1234",
    },
  };

  it("should render correctly with the provided data for Credit Card", () => {
    render(<ProductOrderDetails data={mockData} />);

    expect(screen.getByText("Order")).toBeInTheDocument();
    expect(screen.getByText("#123456")).toBeInTheDocument();

    expect(screen.getByTestId("product-info")).toBeInTheDocument();

    expect(screen.getByTestId("credit-card-info")).toBeInTheDocument();
    expect(screen.getByText("12x R$ 100,00")).toBeInTheDocument();

    expect(screen.getByText("Access my product")).toBeInTheDocument();
  });

  it("should render correctly for Pix payment", () => {
    const pixData = {
      ...mockData,
      payment_method: PaymentOption.Pix,
    };

    render(<ProductOrderDetails data={pixData} />);

    expect(screen.getByAltText("Pix")).toBeInTheDocument();
    expect(screen.getByText("Payment Successful")).toBeInTheDocument();
  });

  it("should render correctly for Boleto payment", () => {
    const boletoData = {
      ...mockData,
      payment_method: PaymentOption.Boleto,
    };

    render(<ProductOrderDetails data={boletoData} />);

    expect(screen.getByAltText("Boleto")).toBeInTheDocument();
    expect(screen.getByText("Payment via Boleto")).toBeInTheDocument();
  });

  it("should display the correct payment value", () => {
    render(<ProductOrderDetails data={mockData} />);

    expect(screen.getByText("Payment Value")).toBeInTheDocument();
    expect(screen.getByText("12x R$ 100,00")).toBeInTheDocument();
  });

  it("should render the button to access the product", () => {
    render(<ProductOrderDetails data={mockData} />);

    const button = screen.getByText("Access my product");
    expect(button).toBeInTheDocument();
  });

  it("should display R$ 0,00 for trial subscription products", () => {
    const trialData = {
      ...mockData,
      product: {
        charge_type: "subscription",
        subscription: {
          trial_days: 7,
          periodicity: "monthly",
          interval: 1,
        },
      },
      payment: {
        description: "12x R$ 100,00",
        status: "trialed",
      },
    };

    render(<ProductOrderDetails data={trialData} />);

    expect(screen.getByText("Payment Value")).toBeInTheDocument();
    expect(screen.getByText("R$ 0,00")).toBeInTheDocument();
    expect(screen.queryByText("12x R$ 100,00")).not.toBeInTheDocument();
  });

  it("should display R$ 0,00 for trial subscription products with subscription_id", () => {
    const trialDataWithSubscriptionId = {
      ...mockData,
      subscription_id: "sub-123",
      product: {
        charge_type: "subscription",
        subscription: {
          trial_days: 7,
          periodicity: "monthly",
          interval: 1,
        },
      },
      payment: {
        description: "12x R$ 100,00",
        status: "approved",
      },
    };

    render(<ProductOrderDetails data={trialDataWithSubscriptionId} />);

    expect(screen.getByText("Payment Value")).toBeInTheDocument();
    expect(screen.getByText("R$ 0,00")).toBeInTheDocument();
    expect(screen.queryByText("12x R$ 100,00")).not.toBeInTheDocument();
  });

  it("should display normal payment value for non-trial subscription products", () => {
    const nonTrialData = {
      ...mockData,
      product: {
        charge_type: "subscription",
        subscription: {
          trial_days: null,
          periodicity: "monthly",
          interval: 1,
        },
      },
      payment: {
        description: "12x R$ 100,00",
        status: "approved",
      },
    };

    render(<ProductOrderDetails data={nonTrialData} />);

    expect(screen.getByText("Payment Value")).toBeInTheDocument();
    expect(screen.getByText("12x R$ 100,00")).toBeInTheDocument();
    expect(screen.queryByText("R$ 0,00")).not.toBeInTheDocument();
  });

  it("should display normal payment value for regular order products", () => {
    const orderData = {
      ...mockData,
      product: {
        charge_type: "order",
      },
      payment: {
        description: "12x R$ 100,00",
        status: "approved",
      },
    };

    render(<ProductOrderDetails data={orderData} />);

    expect(screen.getByText("Payment Value")).toBeInTheDocument();
    expect(screen.getByText("12x R$ 100,00")).toBeInTheDocument();
    expect(screen.queryByText("R$ 0,00")).not.toBeInTheDocument();
  });
});
