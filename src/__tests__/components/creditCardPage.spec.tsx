import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";

import { CreditCardPage } from "@/components/CreditCardPage";
import { Locales } from "@/types/locales";

import { makeAwaitingPaymentOrder } from "../../factories/awaitingPayment";
import { creditCardMessages } from "../datasets/creditCardMessages";

jest.mock("@/components/ProductInfo", () => ({
  __esModule: true,
  default: ({ infoData }: any) => (
    <div data-testid="ProductInfo">{infoData?.title}</div>
  ),
}));

jest.mock("@/components/HeaderCreditCard", () => ({
  __esModule: true,
  default: () => <div data-testid="HeaderCreditCard">Header</div>,
}));

jest.mock("@/components/HelpSection", () => ({
  __esModule: true,
  default: () => <div data-testid="HelpSection">Ajuda</div>,
}));

jest.mock("@/components/ImageWithFallback", () => ({
  __esModule: true,
  ImageWithFallback: ({ alt }: any) => (
    <img data-testid="ImageWithFallback" alt={alt} />
  ),
}));

describe("CreditCardPage", () => {
  it.each(creditCardMessages)(
    "should render all main sections with correct content",
    (locale: Locales, messages: Record<string, any>) => {
      const data = makeAwaitingPaymentOrder();

      render(
        <IntlProvider locale={locale} messages={messages}>
          <CreditCardPage data={data} id="ABC123" />
        </IntlProvider>
      );

      expect(screen.getByTestId("HeaderCreditCard")).toBeInTheDocument();
      expect(
        screen.getByText(messages.credit_card_page.description)
      ).toBeInTheDocument();

      expect(screen.getByText(/#ABC123/i)).toBeInTheDocument();
      expect(screen.getByTestId("ProductInfo")).toHaveTextContent(
        "Produto Teste"
      );
      expect(screen.getByTestId("ImageWithFallback")).toBeInTheDocument();
      expect(screen.getByText(/Visa \*4242/i)).toBeInTheDocument();
      expect(screen.getByText("1x de R$14,90")).toBeInTheDocument();

      expect(screen.getByTestId("HelpSection")).toBeInTheDocument();
    }
  );
});
