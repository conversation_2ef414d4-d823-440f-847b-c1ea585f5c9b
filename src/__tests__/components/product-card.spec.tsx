import { QueryClient } from "@tanstack/react-query";
import { QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import ProductCard from "@/components/ProductCard";
import { Locales } from "@/types/locales";
import { ProductPeriodicity } from "@/types/product";

import { buyerFormMessages } from "../datasets/buyerFormMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

const baseProductProps = {
  organization_id: "org-1",
  description: "Descrição do produto",
  image: "https://example.com/image.jpg",
  warranty_days: 30,
  status: { value: "active", label: "Ativo" },
  created_at: "2024-01-01",
  updated_at: "2024-01-01",
  deleted_at: null,
  organization: {
    id: "org-1",
    name: "Organização",
    status: { value: "active", label: "Ativo" },
  },
  sales_page: "https://example.com",
  charge_type: "one_time",
  bumps: [],
  organization_status: { value: "active", label: "Ativo" },
  payment_methods: [],
  pixels: [],
};

const mockProductWithSubscription: any = {
  id: "1",
  title: "Produto com Assinatura",
  price: 20000,
  installment_details: "ou 12x de 20,00",
  platform: { id: "1", name: "Plataforma X", url: "https://platform-x.com" },
  subscription: {
    interval: 1,
    periodicity: ProductPeriodicity.MONTHLY,
    trial_days: 14,
  },
  ...baseProductProps,
};

const mockProductWithoutSubscription: any = {
  id: "2",
  title: "Produto sem Assinatura",
  price: 15000,
  installment_details: "ou 3x de 50,00",
  platform: { id: "2", name: "Plataforma Y", url: "https://platform-y.com" },
  ...baseProductProps,
};

const mockProductWithoutTrial: any = {
  id: "3",
  title: "Produto sem Trial",
  price: 30000,
  installment_details: "ou 6x de 50,00",
  platform: { id: "3", name: "Plataforma Z", url: "https://platform-z.com" },
  subscription: {
    interval: 1,
    periodicity: ProductPeriodicity.MONTHLY,
    trial_days: null,
  },
  ...baseProductProps,
};

const mockProductInstallments = {
  installments: [
    { installments: "12", total: "20000", description: "12x de R$20,00" },
  ],
  bumps: [],
};

describe("ProductCard Component", () => {
  it.each(buyerFormMessages)(
    "should render the product card with the correct title for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const titleElement = screen.getByText(mockProductWithSubscription.title);
      expect(titleElement).toBeInTheDocument();
      expect(titleElement).toHaveClass("font-semibold", "leading-5", "mb-2");
    }
  );

  it.each(buyerFormMessages)(
    "should render the free trial button if the product has trial_days for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const trialDays = mockProductWithSubscription.subscription?.trial_days;
      const expectedButtonText = messages.buyer_form.free_trial.replace(
        "{days}",
        trialDays?.toString() ?? "0"
      );
      const freeTrialButton = screen.getByRole("button", {
        name: expectedButtonText,
      });
      expect(freeTrialButton).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should not render the free trial button if the product has no trial_days for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithoutTrial}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const freeTrialButton = screen.queryByRole("button");
      expect(freeTrialButton).not.toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should not render the free trial button if the product has trial_days = 0 for the locale %s",
    (locale, messages) => {
      const productWithZeroTrialDays = {
        ...mockProductWithSubscription,
        subscription: {
          ...mockProductWithSubscription.subscription,
          trial_days: 0,
        },
      };

      renderWithIntl(
        <ProductCard
          product={productWithZeroTrialDays}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const freeTrialButton = screen.queryByRole("button");
      expect(freeTrialButton).not.toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should render the platform information correctly for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const platformText = screen.getByText(
        mockProductWithSubscription.platform.name
      );
      expect(platformText).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should render the price correctly formatted for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const priceElement = screen.getByText("12x de R$20,00");
      expect(priceElement).toBeInTheDocument();
      expect(priceElement).toHaveClass(
        "text-xl",
        "font-semibold",
        "text-blue-700"
      );
    }
  );

  it.each(buyerFormMessages)(
    "should render the subscription periodicity for products with subscription for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const periodicityText = screen.getByText(
        /a cada 1 mês|every 1 month|cada 1 mes/i
      );
      expect(periodicityText).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should render installment details for products without subscription for the locale %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductCard
          product={mockProductWithoutSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );
      const installmentDetails = screen.getByText(content =>
        content.includes("12x de R$20,00")
      );
      expect(installmentDetails).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing product data gracefully for the locale %s",
    (locale, messages) => {
      const incompleteProduct: any = {
        id: "4",
        title: "",
        price: 0,
        installment_details: "",
        platform: { id: "4", name: "", url: "" },
        ...baseProductProps,
      };
      renderWithIntl(
        <ProductCard
          product={incompleteProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const titleElement = screen.getByRole("heading", { level: 2 });
      expect(titleElement).toBeInTheDocument();
      expect(screen.getByText(/R\$ 0,00/)).toBeInTheDocument();
      expect(screen.getByText(messages.buyer_form.author)).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing subscription data for the locale %s",
    (locale, messages) => {
      const productWithoutSubscription: any = {
        ...mockProductWithSubscription,
        subscription: undefined,
      };
      renderWithIntl(
        <ProductCard
          product={productWithoutSubscription}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
      expect(
        screen.queryByText(/a cada 1 mês|every 1 month|cada 1 mes/i)
      ).not.toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing platform data for the locale %s",
    (locale, messages) => {
      const productWithoutPlatform: any = {
        ...mockProductWithSubscription,
        platform: undefined,
      };
      renderWithIntl(
        <ProductCard
          product={productWithoutPlatform}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(screen.getByText("Plataforma")).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing price data for the locale %s",
    (locale, messages) => {
      const productWithoutPrice: any = {
        ...mockProductWithSubscription,
        price: undefined,
      };
      renderWithIntl(
        <ProductCard
          product={productWithoutPrice}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(screen.getByText(/R\$ 0,00/)).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing installment details for the locale %s",
    (locale, messages) => {
      const productWithoutInstallment: any = {
        ...mockProductWithoutSubscription,
        installment_details: undefined,
      };
      renderWithIntl(
        <ProductCard
          product={productWithoutInstallment}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.queryByText("Detalhes do parcelamento")
      ).not.toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should handle missing title data for the locale %s",
    (locale, messages) => {
      const productWithoutTitle: any = {
        ...mockProductWithSubscription,
        title: undefined,
      };
      renderWithIntl(
        <ProductCard
          product={productWithoutTitle}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(screen.getByText("Título do produto")).toBeInTheDocument();
    }
  );
});
