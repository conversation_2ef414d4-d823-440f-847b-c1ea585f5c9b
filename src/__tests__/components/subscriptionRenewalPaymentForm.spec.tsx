import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import SubscriptionRenewalPaymentForm from "@/components/SubscriptionRenewalPaymentForm";
import { InstallmentsResponse } from "@/types/instalmentsList";
import { Locales } from "@/types/locales";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";
import { SubscriptionProductForComponents } from "@/types/subscription";

Object.defineProperty(HTMLFormElement.prototype, "requestSubmit", {
  value: function () {
    return Promise.resolve();
  },
  writable: true,
});

jest.mock("@/components/PaymentSelectCard", () => ({
  PaymentSelectCard: ({ title }: any) => (
    <button data-testid={`payment-card-${title}`}>{title}</button>
  ),
}));

jest.mock("@/components/CreditCardFormFields", () => ({
  CreditCardFormFields: () => (
    <div data-testid="credit-card-fields">
      <span>À vista</span>
    </div>
  ),
}));

jest.mock("@/components/BoletoCard", () => ({
  BoletoCard: () => <div data-testid="boleto-card">Boleto</div>,
}));

jest.mock("@/components/PixCard", () => ({
  PixCard: () => <div data-testid="pix-card">Pix</div>,
}));

jest.mock("@/components/LoadingOverlay", () => ({
  LoadingOverlay: () => <div data-testid="loading-overlay">Loading...</div>,
}));

const mockSetSelectedOption = jest.fn();
const mockSetSelectedInstalmentDescription = jest.fn();
const mockGetCardStyles = jest.fn();
const mockHandleRenewalSubmit = jest.fn();

jest.mock("@/hooks/usePaymentState", () => ({
  usePaymentState: jest.fn(() => ({
    selectedOption: "credit_card",
    setSelectedOption: mockSetSelectedOption,
    setSelectedInstalmentDescription: mockSetSelectedInstalmentDescription,
    getCardStyles: mockGetCardStyles,
  })),
}));

jest.mock("@/hooks/useSubscriptionRenewalFormSchema", () => ({
  useSubscriptionRenewalFormSchema: () => ({
    formMethods: {
      handleSubmit: (fn: any) => (e: any) => {
        e.preventDefault();
        return fn({});
      },
      register: jest.fn(),
      formState: { errors: {} },
      watch: jest.fn(),
      setValue: jest.fn(),
      getValues: jest.fn(),
    },
  }),
}));

jest.mock("@/hooks/useSubscriptionRenewalSubmitHandler", () => ({
  useSubscriptionRenewalSubmitHandler: jest.fn(() => ({
    onSubmit: mockHandleRenewalSubmit,
    isLoading: false,
  })),
}));

jest.mock("@/hooks/useOrchestratorData", () => ({
  useOrchestratorData: () => ({
    data: { publicKey: "test-key" },
  }),
}));

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useParams: () => ({
    locale: "pt-BR",
  }),
}));

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

const mockProduct: Product = {
  id: "prod_123",
  organization_id: "org_123",
  title: "Produto Teste",
  description: "Descrição do produto",
  image: null,
  price: 100,
  installment_details: "Detalhes do parcelamento",
  warranty_days: 30,
  sales_page: "https://sales.com",
  charge_type: "subscription" as any,
  payment_methods: ["credit_card", "pix", "boleto"],
  platform: {
    id: "platform_123",
    name: "Test Platform",
    url: "https://test.com",
  },
  organization: {
    id: "org_123",
    status: {
      value: "completed",
      message: "Completed",
    },
  },
  bumps: [],
  pixels: [],
};

const mockProductInstallments: InstallmentsResponse = {
  installments: [
    {
      installments: 1,
      total: "100",
      description: "À vista",
    },
    {
      installments: 2,
      total: "110",
      description: "2x de R$ 55,00",
    },
  ],
  bumps: [],
};

const mockMessages = {
  buyer_form: {
    submit_button_subscription: "Assinar agora",
    generate_pix: "Gerar Pix",
    generate_boleto: "Gerar Boleto",
  },
};

describe("SubscriptionRenewalPaymentForm Component", () => {
  it("should render the form with payment method options", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("Assinar agora")).toBeInTheDocument();
  });

  it("should render payment select cards for available methods", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("payment-card-credit_card")).toBeInTheDocument();
    expect(screen.getByTestId("payment-card-pix")).toBeInTheDocument();
    expect(screen.getByTestId("payment-card-boleto")).toBeInTheDocument();
  });

  it("should render credit card form fields when credit card is selected", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("credit-card-fields")).toBeInTheDocument();
    expect(screen.getByText("À vista")).toBeInTheDocument();
  });

  it("should render submit button with correct text", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    const submitButton = screen.getByRole("button", { name: /Assinar agora/i });
    expect(submitButton).toBeInTheDocument();
    expect(submitButton).toHaveAttribute("type", "submit");
  });

  it("should handle form submission", async () => {
    const mockOnSubmit = jest.fn();

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
        onSubmit={mockOnSubmit}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(mockOnSubmit).toBeDefined();

    const form = document.querySelector("form");
    expect(form).toBeInTheDocument();
    expect(form).toHaveAttribute("class", "w-full");
  });

  it("should render with signature prop", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
        signature="test-signature"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("Assinar agora")).toBeInTheDocument();
  });

  it("should render with subscription product type", () => {
    const subscriptionProduct: SubscriptionProductForComponents = {
      id: "sub_123",
      title: "Assinatura Teste",
      price: 100,
      payment_methods: ["credit_card"],
      platform: {
        name: "Test Platform",
        url: "https://test.com",
      },
      subscription: {
        interval: 1,
        periodicity: "monthly",
        trial_days: null,
      },
    };

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={subscriptionProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("Assinar agora")).toBeInTheDocument();
  });

  it("should render form with correct styling", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    const form = document.querySelector("form");
    expect(form).toHaveClass("w-full");
  });

  it("should call onSubmit prop when form is submitted with custom handler", async () => {
    const mockOnSubmit = jest.fn();
    const user = userEvent.setup();

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
        onSubmit={mockOnSubmit}
      />,
      Locales.PT_BR,
      mockMessages
    );

    const submitButton = screen.getByRole("button", { name: /Assinar agora/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        paymentMethod: "credit_card",
        installments: "",
      });
    });
  });

  it("should call handleRenewalSubmit when no onSubmit prop is provided", async () => {
    const user = userEvent.setup();

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    const submitButton = screen.getByRole("button", { name: /Assinar agora/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockHandleRenewalSubmit).toHaveBeenCalled();
    });
  });

  it("should use default payment methods when product.payment_methods is undefined", () => {
    const productWithoutPaymentMethods = {
      ...mockProduct,
      payment_methods: undefined,
    };

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={productWithoutPaymentMethods as any}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("payment-card-credit_card")).toBeInTheDocument();
    expect(screen.queryByTestId("payment-card-pix")).not.toBeInTheDocument();
    expect(screen.queryByTestId("payment-card-boleto")).not.toBeInTheDocument();
  });

  it("should use default payment methods when product is null", () => {
    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={null as any}
        productInstallments={mockProductInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("payment-card-credit_card")).toBeInTheDocument();
  });

  describe("Payment method selection", () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it("should render PIX content when PIX is selected", () => {
      const usePaymentState =
        require("@/hooks/usePaymentState").usePaymentState;
      usePaymentState.mockReturnValue({
        selectedOption: PaymentOption.Pix,
        setSelectedOption: mockSetSelectedOption,
        setSelectedInstalmentDescription: mockSetSelectedInstalmentDescription,
        getCardStyles: mockGetCardStyles,
      });

      renderWithIntl(
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
        />,
        Locales.PT_BR,
        mockMessages
      );

      expect(screen.getByTestId("pix-card")).toBeInTheDocument();
      expect(screen.getByText("Gerar Pix")).toBeInTheDocument();
    });

    it("should render Boleto content when Boleto is selected", () => {
      const usePaymentState =
        require("@/hooks/usePaymentState").usePaymentState;
      usePaymentState.mockReturnValue({
        selectedOption: PaymentOption.Boleto,
        setSelectedOption: mockSetSelectedOption,
        setSelectedInstalmentDescription: mockSetSelectedInstalmentDescription,
        getCardStyles: mockGetCardStyles,
      });

      renderWithIntl(
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
        />,
        Locales.PT_BR,
        mockMessages
      );

      expect(screen.getByTestId("boleto-card")).toBeInTheDocument();
      expect(screen.getByText("Gerar Boleto")).toBeInTheDocument();
    });

    it("should render null content for unknown payment option", () => {
      const usePaymentState =
        require("@/hooks/usePaymentState").usePaymentState;
      usePaymentState.mockReturnValue({
        selectedOption: "unknown_payment" as any,
        setSelectedOption: mockSetSelectedOption,
        setSelectedInstalmentDescription: mockSetSelectedInstalmentDescription,
        getCardStyles: mockGetCardStyles,
      });

      renderWithIntl(
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
        />,
        Locales.PT_BR,
        mockMessages
      );

      expect(
        screen.queryByTestId("credit-card-fields")
      ).not.toBeInTheDocument();
      expect(screen.queryByTestId("pix-card")).not.toBeInTheDocument();
      expect(screen.queryByTestId("boleto-card")).not.toBeInTheDocument();
    });
  });

  describe("Loading state", () => {
    it("should show loading state when isLoading is true", () => {
      const useSubmitHandler =
        require("@/hooks/useSubscriptionRenewalSubmitHandler").useSubscriptionRenewalSubmitHandler;
      useSubmitHandler.mockReturnValue({
        onSubmit: mockHandleRenewalSubmit,
        isLoading: true,
      });

      renderWithIntl(
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
        />,
        Locales.PT_BR,
        mockMessages
      );

      expect(screen.getAllByTestId("loading-overlay")).toHaveLength(2);

      const submitButtons = screen.getAllByRole("button");
      const submitButton = submitButtons.find(
        btn => btn.getAttribute("type") === "submit"
      );
      expect(submitButton).toBeDisabled();
      expect(submitButton?.querySelector(".animate-spin")).toBeInTheDocument();
    });

    it("should not show loading state when isLoading is false", () => {
      const useSubmitHandler =
        require("@/hooks/useSubscriptionRenewalSubmitHandler").useSubscriptionRenewalSubmitHandler;
      useSubmitHandler.mockReturnValue({
        onSubmit: mockHandleRenewalSubmit,
        isLoading: false,
      });

      renderWithIntl(
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
        />,
        Locales.PT_BR,
        mockMessages
      );

      expect(screen.queryByTestId("loading-overlay")).not.toBeInTheDocument();

      const submitButtons = screen.getAllByRole("button");
      const submitButton = submitButtons.find(
        btn => btn.getAttribute("type") === "submit"
      );
      expect(submitButton).not.toBeDisabled();
      expect(
        submitButton?.querySelector(".animate-spin")
      ).not.toBeInTheDocument();
    });
  });

  describe("Form submission with installments", () => {
    it("should include selectedInstallment when data.instalment is not provided", async () => {
      const mockOnSubmit = jest.fn();
      const user = userEvent.setup();

      const usePaymentState =
        require("@/hooks/usePaymentState").usePaymentState;
      usePaymentState.mockReturnValue({
        selectedOption: "credit_card",
        setSelectedOption: mockSetSelectedOption,
        setSelectedInstalmentDescription: mockSetSelectedInstalmentDescription,
        getCardStyles: mockGetCardStyles,
      });

      const mockComponent = (
        <SubscriptionRenewalPaymentForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
          subscriptionId="sub_123"
          onSubmit={mockOnSubmit}
        />
      );

      renderWithIntl(mockComponent, Locales.PT_BR, mockMessages);

      const submitButtons = screen.getAllByRole("button");
      const submitButton = submitButtons.find(
        btn => btn.getAttribute("type") === "submit"
      );
      if (submitButton) {
        await user.click(submitButton);
      }

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith({
          paymentMethod: "credit_card",
          installments: "",
        });
      });
    });
  });

  it("should handle undefined productInstallments.installments", () => {
    const productInstallmentsWithoutInstallments = {
      ...mockProductInstallments,
      installments: undefined as any,
    };

    renderWithIntl(
      <SubscriptionRenewalPaymentForm
        product={mockProduct}
        productInstallments={productInstallmentsWithoutInstallments}
        subscriptionId="sub_123"
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("credit-card-fields")).toBeInTheDocument();
  });
});
