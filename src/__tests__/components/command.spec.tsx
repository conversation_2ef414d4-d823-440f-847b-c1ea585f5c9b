import { DialogDescription, DialogTitle } from "@radix-ui/react-dialog";
import { render, screen } from "@testing-library/react";

import { CommandDialog, CommandShortcut } from "@/components/ui/Command";

describe("CommandDialog", () => {
  it("should render children correctly", () => {
    render(
      <CommandDialog open={true}>
        <DialogTitle>Command Dialog Title</DialogTitle>
        <DialogDescription>
          This is a description for the dialog.
        </DialogDescription>
        <div>Test Child</div>
      </CommandDialog>
    );

    expect(screen.getByText("Test Child")).toBeInTheDocument();
  });

  it("should render the CommandShortcut with the correct classes", () => {
    render(<CommandShortcut>Ctrl+K</CommandShortcut>);

    const shortcut = screen.getByText("Ctrl+K");
    expect(shortcut).toBeInTheDocument();
    expect(shortcut).toHaveClass(
      "ml-auto text-xs tracking-widest text-muted-foreground"
    );
  });

  it("should apply custom classes to CommandShortcut", () => {
    render(<CommandShortcut className="custom-class">Ctrl+K</CommandShortcut>);

    const shortcut = screen.getByText("Ctrl+K");
    expect(shortcut).toHaveClass(
      "ml-auto text-xs tracking-widest text-muted-foreground custom-class"
    );
  });
});
