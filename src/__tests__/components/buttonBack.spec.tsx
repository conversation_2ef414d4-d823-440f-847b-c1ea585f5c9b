import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import ButtonBack from "@/components/ButtonBack";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useParams: jest.fn(),
}));

describe("ButtonBack Component", () => {
  it.each(pixMessages)(
    "should render the ButtonBack component with translations in %s",
    (locale, messages) => {
      const router = {
        push: jest.fn(),
        back: jest.fn(),
      };
      const params = {
        locale,
      };
      const { useRouter, useParams } = require("next/navigation");
      useRouter.mockReturnValue(router);
      useParams.mockReturnValue(params);

      renderWithIntl(
        <ButtonBack text={messages.pix_page.change_payment_method} />,
        locale,
        messages
      );

      const button = screen.getByText(messages.pix_page.change_payment_method);
      expect(button).toBeInTheDocument();

      fireEvent.click(button);
      expect(router.push).toHaveBeenCalledTimes(1);
    }
  );

  it("should call router.back() when subscriptionId is provided", () => {
    const router = {
      push: jest.fn(),
      back: jest.fn(),
    };
    const params = {
      locale: Locales.PT_BR,
    };
    const { useRouter, useParams } = require("next/navigation");
    useRouter.mockReturnValue(router);
    useParams.mockReturnValue(params);

    const messages = {
      common: {
        back: "Voltar",
      },
    };

    renderWithIntl(
      <ButtonBack
        text={messages.common.back}
        subscriptionId="sub_123"
        orderId="order_456"
        productId="prod_789"
      />,
      Locales.PT_BR,
      messages
    );

    const button = screen.getByText(messages.common.back);
    fireEvent.click(button);

    expect(router.back).toHaveBeenCalledTimes(1);
    expect(router.push).not.toHaveBeenCalled();
  });

  it("should call router.push() when subscriptionId is not provided", () => {
    const router = {
      push: jest.fn(),
      back: jest.fn(),
    };
    const params = {
      locale: Locales.PT_BR,
    };
    const { useRouter, useParams } = require("next/navigation");
    useRouter.mockReturnValue(router);
    useParams.mockReturnValue(params);

    const messages = {
      common: {
        back: "Voltar",
      },
    };

    renderWithIntl(
      <ButtonBack
        text={messages.common.back}
        orderId="order_456"
        productId="prod_789"
      />,
      Locales.PT_BR,
      messages
    );

    const button = screen.getByText(messages.common.back);
    fireEvent.click(button);

    expect(router.push).toHaveBeenCalledTimes(1);
    expect(router.push).toHaveBeenCalledWith(
      "/pt-BR/order/order_456?product=prod_789"
    );
    expect(router.back).not.toHaveBeenCalled();
  });
});
