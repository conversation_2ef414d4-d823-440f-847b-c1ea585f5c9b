import { screen } from "@testing-library/react";

import Header<PERSON>oleto from "@/components/HeaderBoleto";

import { boletoMessages } from "../datasets/boletoMessages";
import { renderWithIntl } from "./copyButtonBoleto.spec";

describe("HeaderBoleto Component", () => {
  it.each(boletoMessages)(
    "renders the correct header content for locale %s",
    (locale, messages) => {
      renderWithIntl(<HeaderBoleto />, locale, messages);

      expect(
        screen.getByText(messages.boleto_page.payment_boleto)
      ).toBeInTheDocument();

      const image = screen.getByRole("img", { name: "Boleto" });
      expect(image).toHaveAttribute("src", "/images/barcode-dark.svg");
      expect(image).toHaveAttribute("alt", "Boleto");
    }
  );

  it.each(boletoMessages)(
    "renders the header with the correct structure for locale %s",
    (locale, messages) => {
      renderWithIntl(<HeaderBoleto />, locale, messages);

      const header = screen.getByRole("banner");
      expect(header).toBeInTheDocument();

      expect(header).toHaveClass(
        "flex flex-col items-start justify-between rounded-t-lg bg-amber-400 p-8 sm:flex-row sm:items-center"
      );

      const title = screen.getByText(messages.boleto_page.payment_boleto);
      expect(title).toHaveClass("text-2xl font-semibold leading-7");
    }
  );
});
