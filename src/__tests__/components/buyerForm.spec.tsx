import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";

import BuyerForm from "@/components/BuyerForm";
import { DocumentType } from "@/types/document";
import { Locales } from "@/types/locales";

import { buyerFormMessages } from "../datasets/buyerFormMessages";

jest.mock("lottie-react", () => ({
  __esModule: true,
  default: () => <div data-testid="lottie-animation">Animation</div>,
}));

const mockProduct: any = {
  id: "1",
  organization_id: "1",
  title: "A Bíblia e o Dinheiro",
  description: "Livro que aborda a relação entre fé e finanças.",
  image: null,
  price: 15990,
  installment_details: "ou 12x de 15,36 no cartão",
  warranty_days: 7,
  sales_page: "https://example.com/produto/a-biblia-e-o-dinheiro",
  charge_type: "oneoff",
  payment_methods: ["credit_card", "pix", "boleto"],
  platform: {
    id: "1",
    name: "A Bíblia Comentada",
    url: "https://example.com",
  },
};

const mockProductInstallments: any = {
  installments: [
    { installments: 1, description: "1x de R$ 159,90" },
    { installments: 2, description: "2x de R$ 79,95" },
  ],
};

jest.mock("next/navigation", () => ({
  useParams: jest.fn(() => ({ id: "123" })),
  useSearchParams: jest.fn(() => ({
    get: jest.fn(),
  })),
  useRouter: jest.fn(() => ({
    push: jest.fn(),
  })),
}));

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const formMethods = useForm({
      defaultValues: {
        name: "",
        email: "",
        confirmEmail: "",
        document: "",
        phone: "",
        cardNumber: "",
        cardHolderName: "",
        cardCvv: "",
        cardExpirationDateMonth: "",
        cardExpirationDateYear: "",
        instalment: "",
      },
    });
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          <FormProvider {...formMethods}>{component}</FormProvider>
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("BuyerForm Component", () => {
  it.each(buyerFormMessages)(
    "should render BuyerForm with translations in %s",
    (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByLabelText(messages.buyer_form.full_name)
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(messages.buyer_form.email)
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(messages.buyer_form.confirm_email)
      ).toBeInTheDocument();
      expect(
        screen.getByLabelText(messages.buyer_form.cpf_or_cnpj)
      ).toBeInTheDocument();

      expect(
        screen.getByRole("button", { name: messages.buyer_form.submit_button })
      ).toBeInTheDocument();

      expect(
        screen.getAllByPlaceholderText(messages.buyer_form.name_placeholder)
      ).toHaveLength(2);
      expect(
        screen.getAllByPlaceholderText(messages.buyer_form.email_placeholder)
      ).toHaveLength(2);
      expect(
        screen.getAllByPlaceholderText(
          messages.buyer_form.confirm_email_placeholder
        )
      ).toHaveLength(2);
      expect(
        screen.getByPlaceholderText(messages.buyer_form.document_placeholder)
      ).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText(messages.buyer_form.phone_placeholder)
      ).toBeInTheDocument();
    }
  );

  beforeEach(() => {
    global.ResizeObserver = class {
      observe() {}
      unobserve() {}
      disconnect() {}
    };

    Element.prototype.scrollIntoView = jest.fn();
  });

  it.each(buyerFormMessages)(
    "should validate the full name correctly in %s",
    async (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const nameInput = screen.getByLabelText(messages.buyer_form.full_name);
      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });

      fireEvent.change(nameInput, { target: { value: "John" } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        const errorMessage = screen.getByTestId("error-name");
        expect(errorMessage).toBeInTheDocument();
      });

      fireEvent.change(nameInput, { target: { value: "John Doe" } });
      fireEvent.click(submitButton);
    }
  );

  it.each(buyerFormMessages)(
    "should validate the document correctly for CPF and CNPJ in %s",
    async (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const documentInput = screen.getByLabelText(
        messages.buyer_form.cpf_or_cnpj
      );
      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });

      fireEvent.change(documentInput, { target: { value: "123.456.789-09" } });
      fireEvent.change(documentInput, {
        target: {
          value: JSON.stringify({
            type: DocumentType.CPF,
            number: "123.456.789-09",
          }),
        },
      });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByTestId("error-document")).not.toBeInTheDocument();
      });

      fireEvent.change(documentInput, { target: { value: "123.456.789-00" } });
      fireEvent.click(submitButton);

      fireEvent.change(documentInput, {
        target: { value: "12.345.678/0001-95" },
      });
      fireEvent.change(documentInput, {
        target: {
          value: JSON.stringify({
            type: DocumentType.CNPJ,
            number: "12.345.678/0001-95",
          }),
        },
      });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByTestId("error-document")).not.toBeInTheDocument();
      });

      fireEvent.change(documentInput, {
        target: { value: "12.345.678/0001-00" },
      });
      fireEvent.click(submitButton);
    }
  );

  it.each(buyerFormMessages)(
    "should hide the document input when switch is checked in %s",
    (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const switchElement = screen.getByRole("switch");
      const documentInput = screen.getByLabelText(
        messages.buyer_form.cpf_or_cnpj
      );

      expect(documentInput).toBeInTheDocument();

      fireEvent.click(switchElement);

      expect(
        screen.queryByLabelText(messages.buyer_form.cpf_or_cnpj)
      ).not.toBeInTheDocument();

      fireEvent.click(switchElement);

      expect(
        screen.getByLabelText(messages.buyer_form.cpf_or_cnpj)
      ).toBeInTheDocument();
    }
  );

  it.each(buyerFormMessages)(
    "should omit the document field from the submission data when switch is checked in %s",
    async (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const switchElement = screen.getByRole("switch");
      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });

      fireEvent.click(switchElement);
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(
          screen.queryByLabelText(messages.buyer_form.cpf_or_cnpj)
        ).not.toBeInTheDocument();
      });
    }
  );

  it.each(buyerFormMessages)(
    "should include the document field in the submission data when switch is unchecked in %s",
    async (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });
      const switchElement = screen.getByRole("switch");

      expect(switchElement).not.toBeChecked();

      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(
          screen.getByLabelText(messages.buyer_form.cpf_or_cnpj)
        ).toBeInTheDocument();
      });
    }
  );

  it.each(buyerFormMessages)(
    "should switch correctly between payment methods in %s",
    async (locale, messages) => {
      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.safe_buy)
      ).toBeInTheDocument();

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      await waitFor(() => {
        expect(
          screen.getByText(messages.buyer_form.immediate_approval)
        ).toBeInTheDocument();
        expect(
          screen.queryByText(messages.buyer_form.safe_buy)
        ).not.toBeInTheDocument();
      });

      const boletoLabels = screen.getAllByText(messages.buyer_form.boleto);
      const boletoOption = boletoLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(boletoOption!);

      await waitFor(() => {
        expect(screen.getByAltText("Boleto")).toBeInTheDocument();
      });

      const cardLabels = screen.getAllByText(messages.buyer_form.credit_card);
      const cardOption = cardLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(cardOption!);

      await waitFor(() => {
        expect(
          screen.getByText(messages.buyer_form.safe_buy)
        ).toBeInTheDocument();
      });
    }
  );

  it("should clear phone DDI and number when the phone input is empty", () => {
    const locale: Locales = Locales.PT_BR;
    const messages = buyerFormMessages.find(
      ([localeKey]) => localeKey === locale
    )![1];
    renderWithIntl(
      <BuyerForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      locale,
      messages
    );

    const phoneInput = screen.getByPlaceholderText(
      messages.buyer_form.phone_placeholder
    );
    fireEvent.change(phoneInput, { target: { value: "+5511999999999" } });
    fireEvent.change(phoneInput, { target: { value: "" } });

    expect(screen.queryByDisplayValue("+55")).not.toBeInTheDocument();
    expect(screen.queryByDisplayValue("11999999999")).not.toBeInTheDocument();
  });

  it("should set DDI to an empty string when extractDDI returns undefined", () => {
    const locale: Locales = Locales.PT_BR;
    const messages = buyerFormMessages.find(
      ([localeKey]) => localeKey === locale
    )![1];
    renderWithIntl(
      <BuyerForm
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      locale,
      messages
    );

    const phoneInput = screen.getByPlaceholderText(
      messages.buyer_form.phone_placeholder
    );
    fireEvent.change(phoneInput, { target: { value: "999999999" } });

    expect(screen.queryByDisplayValue("+")).not.toBeInTheDocument();
    expect(screen.getByDisplayValue("(99) 99999-99")).toBeInTheDocument();
  });

  describe("Product with subscription charge type", () => {
    const subscriptionProduct = {
      ...mockProduct,
      charge_type: "subscription",
      subscription: {
        interval: 1,
        periodicity: "monthly",
      },
    };

    it("should display subscription button text for subscription products", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={subscriptionProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByRole("button", {
          name: messages.buyer_form.submit_button_subscription,
        })
      ).toBeInTheDocument();
    });

    it("should display subscription interval information", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      const installmentsData = {
        installments: [{ installments: 1, description: "1x de R$ 159,90" }],
      };

      renderWithIntl(
        <BuyerForm
          product={subscriptionProduct}
          productInstallments={installmentsData as any}
        />,
        locale,
        messages
      );

      await waitFor(() => {
        expect(screen.getByText("Renova a cada 1 mês")).toBeInTheDocument();
      });
    });
  });

  describe("Product with bumps", () => {
    const productWithBumps = {
      ...mockProduct,
      bumps: [
        {
          id: "bump1",
          product_id: "bump_product_1",
          title: "Bump Product 1",
          description: "First bump description",
          price: 5000,
          cta: "Add this bonus",
          charge_type: "oneoff",
        },
        {
          id: "bump2",
          product_id: "bump_product_2",
          title: "Bump Product 2",
          description: "Second bump description",
          price: 7500,
          cta: "Get this extra",
          charge_type: "subscription",
          subscription: {
            interval: 1,
            periodicity: "monthly",
            trial_days: null,
          },
        },
      ],
    };

    const installmentsWithBumps = {
      ...mockProductInstallments,
      bumps: [
        {
          bump_id: "bump1",
          installments: [
            { installments: 1, description: "1x de R$ 50,00" },
            { installments: 2, description: "2x de R$ 25,00" },
          ],
        },
        {
          bump_id: "bump2",
          installments: [
            { installments: 1, description: "1x de R$ 75,00" },
            { installments: 2, description: "2x de R$ 37,50" },
          ],
        },
      ],
    };

    it("should display group buy section when product has bumps", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithBumps}
          productInstallments={installmentsWithBumps}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.group_buy)
      ).toBeInTheDocument();
      expect(screen.getByText("Bump Product 1")).toBeInTheDocument();
      expect(screen.getByText("Bump Product 2")).toBeInTheDocument();
    });

    it("should handle bump selection and update purchase summary", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithBumps}
          productInstallments={installmentsWithBumps}
        />,
        locale,
        messages
      );

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      await waitFor(() => {
        expect(
          screen.getByText(messages.buyer_form.purchase_summary)
        ).toBeInTheDocument();
      });
    });

    it("should update bump installments when installment selection changes", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      const installmentsWithOptions = {
        installments: [
          { installments: 1, description: "1x de R$ 159,90", total: "15990" },
          { installments: 2, description: "2x de R$ 79,95", total: "15990" },
        ],
        bumps: installmentsWithBumps.bumps,
      };

      renderWithIntl(
        <BuyerForm
          product={productWithBumps}
          productInstallments={installmentsWithOptions}
        />,
        locale,
        messages
      );

      const bumpCheckbox = screen.getByTestId("bump-checkbox-bump1");
      fireEvent.click(bumpCheckbox);

      await waitFor(() => {
        const bumpDescriptions = screen.getAllByText("2x de R$ 25,00");
        const purchaseSummaryDescription = bumpDescriptions.find(el =>
          el.className.includes("left-4 px-4 text-sm")
        );
        expect(purchaseSummaryDescription).toBeInTheDocument();
      });
    });
  });

  describe("Pixel tracking", () => {
    const productWithPixels = {
      ...mockProduct,
      pixels: [
        {
          provider: "meta",
          credentials: {
            events: ["purchase"],
          },
        },
        {
          provider: "tiktok",
          credentials: {
            pixels: ["pixel1"],
            events: ["purchase"],
          },
        },
        {
          provider: "google_ads",
          credentials: [
            {
              pixel_id: "AW-123456789",
              conversion_label: "abc123",
              events: ["purchase_pix_creditcard"],
            },
          ],
        },
      ],
    };

    beforeEach(() => {
      Object.defineProperty(window, "fbq", {
        value: jest.fn(),
        writable: true,
      });
      Object.defineProperty(window, "ttq", {
        value: { track: jest.fn() },
        writable: true,
      });
      Object.defineProperty(window, "gtag", {
        value: jest.fn(),
        writable: true,
      });
    });

    it("should trigger Facebook pixel events on PIX payment", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.generate_pix,
      });
      fireEvent.click(submitButton);

      expect(window.fbq).toHaveBeenCalledWith(
        "track",
        "InitiateCheckout",
        expect.any(Object)
      );
      expect(window.fbq).toHaveBeenCalledWith(
        "track",
        "Purchase",
        expect.any(Object)
      );
    });

    it("should trigger TikTok pixel events on Boleto payment", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const boletoLabels = screen.getAllByText(messages.buyer_form.boleto);
      const boletoOption = boletoLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(boletoOption!);

      await waitFor(() => {
        const submitButton = screen.getByText(
          messages.buyer_form.generate_boleto
        );
        fireEvent.click(submitButton);
      });

      expect(window.ttq?.track).toHaveBeenCalledWith(
        "InitiateCheckout",
        expect.any(Object)
      );
      expect(window.ttq?.track).toHaveBeenCalledWith(
        "CompletePayment",
        expect.any(Object)
      );
    });

    it("should trigger Google Ads events on credit card payment", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const submitButton = screen.getByRole("button", {
        name: messages.buyer_form.submit_button,
      });
      fireEvent.click(submitButton);

      expect(window.gtag).toHaveBeenCalledWith(
        "event",
        "conversion",
        expect.any(Object)
      );
      expect(window.gtag).toHaveBeenCalledWith(
        "event",
        "purchase",
        expect.any(Object)
      );
    });
  });

  describe("Saved data functionality", () => {
    beforeEach(() => {
      Object.defineProperty(window, "sessionStorage", {
        value: {
          getItem: jest.fn(),
          setItem: jest.fn(),
          removeItem: jest.fn(),
          clear: jest.fn(),
        },
        writable: true,
      });
    });

    it("should populate form with saved data from sessionStorage", async () => {
      const savedData = {
        name: "John Doe",
        email: "<EMAIL>",
        confirm_email: "<EMAIL>",
        phone: { ddi: "+55", number: "11999999999" },
        document: { type: "cpf", number: "123.456.789-09" },
      };

      (window.sessionStorage.getItem as jest.Mock).mockReturnValue(
        JSON.stringify(savedData)
      );

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      await waitFor(() => {
        expect(screen.getByDisplayValue("John Doe")).toBeInTheDocument();
        expect(screen.getAllByDisplayValue("<EMAIL>")).toHaveLength(2);
      });
    });

    it("should handle CNPJ formatting in saved data", async () => {
      const savedData = {
        name: "Company Name",
        email: "<EMAIL>",
        confirm_email: "<EMAIL>",
        phone: { ddi: "+55", number: "11999999999" },
        document: { type: "cnpj", number: "12345678000195" },
      };

      (window.sessionStorage.getItem as jest.Mock).mockReturnValue(
        JSON.stringify(savedData)
      );

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      await waitFor(() => {
        expect(screen.getByDisplayValue("Company Name")).toBeInTheDocument();
      });
    });

    it("should handle missing saved data gracefully", () => {
      (window.sessionStorage.getItem as jest.Mock).mockReturnValue(null);

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByLabelText(messages.buyer_form.full_name)
      ).toBeInTheDocument();
    });
  });

  describe("Payment method restrictions", () => {
    it("should force Brazilian document for PIX and Boleto payments", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const switchElement = screen.getByRole("switch");
      fireEvent.click(switchElement);

      expect(
        screen.queryByLabelText(messages.buyer_form.cpf_or_cnpj)
      ).not.toBeInTheDocument();

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      await waitFor(() => {
        expect(
          screen.getByLabelText(messages.buyer_form.cpf_or_cnpj)
        ).toBeInTheDocument();
      });
    });

    it("should hide switch for PIX and Boleto payments", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      expect(screen.queryByRole("switch")).not.toBeInTheDocument();
    });
  });

  describe("Purchase summary display", () => {
    it("should show purchase summary only when installment is selected for credit card", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      const installmentsData = {
        installments: [
          { installments: 1, description: "1x de R$ 159,90", total: "15990" },
          { installments: 2, description: "2x de R$ 79,95", total: "15990" },
        ],
        bumps: [],
      };

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={installmentsData}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.purchase_summary)
      ).toBeInTheDocument();
    });

    it("should show purchase summary immediately for non-credit card payments", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      expect(
        screen.getByText(messages.buyer_form.purchase_summary)
      ).toBeInTheDocument();
    });
  });

  describe("Edge cases and error handling", () => {
    it("should handle product without payment methods", () => {
      const productWithoutPaymentMethods = {
        ...mockProduct,
        payment_methods: undefined,
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithoutPaymentMethods}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.safe_buy)
      ).toBeInTheDocument();
    });

    it("should handle product without bumps", () => {
      const productWithoutBumps = {
        ...mockProduct,
        bumps: undefined,
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithoutBumps}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.queryByText(messages.buyer_form.group_buy)
      ).not.toBeInTheDocument();
    });

    it("should handle empty bumps array", () => {
      const productWithEmptyBumps = {
        ...mockProduct,
        bumps: [],
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithEmptyBumps}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.queryByText(messages.buyer_form.group_buy)
      ).not.toBeInTheDocument();
    });

    it("should handle missing installment data", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm product={mockProduct} productInstallments={null as any} />,
        locale,
        messages
      );

      expect(
        screen.getByLabelText(messages.buyer_form.full_name)
      ).toBeInTheDocument();
    });
  });

  describe("Bump selection with pixel tracking", () => {
    const productWithBumpsAndPixels = {
      ...mockProduct,
      bumps: [
        {
          id: "bump1",
          product_id: "bump_product_1",
          title: "Bump Product 1",
          description: "First bump description",
          price: 5000,
          cta: "Add this bonus",
          charge_type: "oneoff",
        },
      ],
      pixels: [
        {
          provider: "meta",
          credentials: {
            events: ["purchase"],
          },
        },
        {
          provider: "tiktok",
          credentials: {
            pixels: ["pixel1"],
            events: ["purchase"],
          },
        },
      ],
    };

    beforeEach(() => {
      Object.defineProperty(window, "fbq", {
        value: jest.fn(),
        writable: true,
      });
      Object.defineProperty(window, "ttq", {
        value: { track: jest.fn() },
        writable: true,
      });
    });

    it("should trigger Facebook AddToCart pixel when bump is selected", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithBumpsAndPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const bumpCheckbox = screen.getByTestId("bump-checkbox-bump1");
      fireEvent.click(bumpCheckbox);

      await waitFor(() => {
        expect(window.fbq).toHaveBeenCalledWith(
          "track",
          "AddToCart",
          expect.objectContaining({
            content_name: productWithBumpsAndPixels.title,
            content_category: "product",
            content_ids: [productWithBumpsAndPixels.id],
            content_type: "product",
            currency: "BRL",
          })
        );
      });
    });

    it("should trigger TikTok AddToCart pixel when bump is selected", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithBumpsAndPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const bumpCheckbox = screen.getByTestId("bump-checkbox-bump1");
      fireEvent.click(bumpCheckbox);

      await waitFor(() => {
        expect(window.ttq?.track).toHaveBeenCalledWith(
          "AddToCart",
          expect.objectContaining({
            contents: [
              {
                content_id: productWithBumpsAndPixels.id,
                content_type: "product",
                content_name: productWithBumpsAndPixels.title,
              },
            ],
            currency: "BRL",
          })
        );
      });
    });

    it("should not trigger pixels when bump is deselected", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithBumpsAndPixels}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const bumpCheckbox = screen.getByTestId("bump-checkbox-bump1");
      fireEvent.click(bumpCheckbox);

      (window.fbq as jest.Mock).mockClear();
      (window.ttq?.track as jest.Mock).mockClear();

      fireEvent.click(bumpCheckbox);

      await waitFor(() => {
        expect(window.fbq).not.toHaveBeenCalledWith(
          "track",
          "AddToCart",
          expect.any(Object)
        );
        expect(window.ttq?.track).not.toHaveBeenCalledWith(
          "AddToCart",
          expect.any(Object)
        );
      });
    });
  });

  describe("Installment description handling", () => {
    it("should update main product installment description when selection changes", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      const installmentsData = {
        installments: [
          { installments: 1, description: "1x de R$ 159,90", total: "15990" },
          { installments: 2, description: "2x de R$ 79,95", total: "15990" },
          { installments: 3, description: "3x de R$ 53,30", total: "15990" },
        ],
        bumps: [],
      };

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={installmentsData}
        />,
        locale,
        messages
      );

      await waitFor(() => {
        const installmentDescriptions = screen.getAllByText("3x de R$ 53,30");
        const purchaseSummaryDescription = installmentDescriptions.find(el =>
          el.className.includes("left-4 px-4 text-sm")
        );
        expect(purchaseSummaryDescription).toBeInTheDocument();
      });

      const installmentSelect = screen.getByTestId(
        "installment-select-trigger"
      );
      fireEvent.click(installmentSelect);

      const installmentOptions = await screen.findAllByRole("option");
      const twoInstallmentOption = installmentOptions.find(option =>
        option.textContent?.includes("2x de R$ 79,95")
      );
      if (twoInstallmentOption) {
        fireEvent.click(twoInstallmentOption);
      }

      await waitFor(() => {
        const installmentDescriptions = screen.getAllByText("2x de R$ 79,95");
        const purchaseSummaryDescription = installmentDescriptions.find(el =>
          el.className.includes("left-4 px-4 text-sm")
        );
        expect(purchaseSummaryDescription).toBeInTheDocument();
      });
    });

    it("should handle missing installment description gracefully", async () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      const installmentsData = {
        installments: [
          { installments: 1, description: "1x de R$ 159,90", total: "15990" },
          { installments: 2, description: "", total: "15990" },
        ],
        bumps: [],
      };

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={installmentsData}
        />,
        locale,
        messages
      );

      const installmentSelect = screen.getByTestId(
        "installment-select-trigger"
      );
      fireEvent.click(installmentSelect);

      await waitFor(() => {
        expect(
          screen.getByText(messages.buyer_form.purchase_summary)
        ).toBeInTheDocument();
      });
    });
  });

  describe("Modal and loading states", () => {
    it("should handle modal state changes", () => {
      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={mockProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });
  });

  describe("Product store integration", () => {
    it("should set subscription state for subscription products", () => {
      const subscriptionProduct = {
        ...mockProduct,
        charge_type: "subscription",
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={subscriptionProduct}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByLabelText(messages.buyer_form.full_name)
      ).toBeInTheDocument();
    });

    it("should set subscription state when product has subscription bumps", () => {
      const productWithSubscriptionBump = {
        ...mockProduct,
        bumps: [
          {
            id: "bump1",
            product_id: "bump_product_1",
            title: "Subscription Bump",
            description: "Subscription bump description",
            price: 5000,
            cta: "Add subscription",
            charge_type: "subscription",
            subscription: {
              interval: 1,
              periodicity: "monthly",
              trial_days: null,
            },
          },
        ],
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithSubscriptionBump}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      expect(
        screen.getByLabelText(messages.buyer_form.full_name)
      ).toBeInTheDocument();
    });

    it("should show subscription interval in purchase summary for subscription bumps", async () => {
      const productWithSubscriptionBump = {
        ...mockProduct,
        bumps: [
          {
            id: "bump1",
            product_id: "bump_product_1",
            title: "Subscription Bump",
            description: "Subscription bump description",
            price: 5000,
            cta: "Add subscription",
            charge_type: "subscription",
            subscription: {
              interval: 1,
              periodicity: "monthly",
              trial_days: null,
            },
          },
        ],
      };

      const locale: Locales = Locales.PT_BR;
      const messages = buyerFormMessages.find(
        ([localeKey]) => localeKey === locale
      )![1];

      renderWithIntl(
        <BuyerForm
          product={productWithSubscriptionBump}
          productInstallments={mockProductInstallments}
        />,
        locale,
        messages
      );

      const pixLabels = screen.getAllByText(messages.buyer_form.pix);
      const pixOption = pixLabels.find(el => el.tagName === "LABEL");
      fireEvent.click(pixOption!);

      const bumpCheckbox = screen.getByTestId("bump-checkbox-bump1");
      fireEvent.click(bumpCheckbox);

      await waitFor(() => {
        expect(screen.getAllByText("Subscription Bump")).toHaveLength(2);
        expect(screen.getAllByText("Renova a cada 1 mês")).toHaveLength(2);
      });
    });
  });
});
