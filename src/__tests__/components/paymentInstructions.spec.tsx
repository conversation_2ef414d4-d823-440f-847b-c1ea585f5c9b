import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import PaymentInstructions from "@/components/PaymentInstructions";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("PaymentInstructions Component", () => {
  it.each(pixMessages)(
    "should render the PaymentInstructions component with translations in %s",
    (locale, messages) => {
      renderWithIntl(
        <PaymentInstructions hiddenOnSmall={false} />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.pix_page.payment_instructions)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.pix_page.pix_app_instruction_1)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.pix_page.pix_app_instruction_2)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.pix_page.pix_app_instruction_3)
      ).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should hide the component when hiddenOnSmall is true in %s",
    (locale, messages) => {
      renderWithIntl(
        <PaymentInstructions hiddenOnSmall={true} />,
        locale,
        messages
      );

      const container = screen.getByText(
        messages.pix_page.payment_instructions
      ).parentElement;

      expect(container).toHaveClass("sm:hidden");
    }
  );

  it.each(pixMessages)(
    "should display the component when hiddenOnSmall is false in %s",
    (locale, messages) => {
      renderWithIntl(
        <PaymentInstructions hiddenOnSmall={false} />,
        locale,
        messages
      );

      const container = screen.getByText(
        messages.pix_page.payment_instructions
      ).parentElement;

      expect(container).toHaveClass("hidden sm:block");
    }
  );
});
