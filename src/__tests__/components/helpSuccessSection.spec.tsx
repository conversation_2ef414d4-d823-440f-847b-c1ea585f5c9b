import { render, screen } from "@testing-library/react";
import { NextIntlClientProvider } from "next-intl";

import { HelpSuccessSection } from "@/components/HelpSuccessSection";

const mockMessages = {
  success: {
    help_page:
      "Se precisar de ajuda com essa compra, entre em contato com o criador ou",
    help_page_link: "acesse nossa página de ajuda.",
    subscription_help_message:
      "Essa compra inclui produtos por assinatura. Se o pagamento foi feito com cartão, você será avisado por e-mail um dia antes da renovação. Se tiver sido via Pix ou boleto, enviaremos um e-mail 3 dias antes com o link para acessar o QR Code ou o código de barras, que será válido por 4 dias. Assim que o pagamento for confirmado, sua assinatura estará garantida para o próximo período.",
  },
};

function renderWithProviders(component: React.ReactElement) {
  return render(
    <NextIntlClientProvider locale="pt-BR" messages={mockMessages}>
      {component}
    </NextIntlClientProvider>
  );
}

describe("HelpSuccessSection Component", () => {
  it("should render regular help message by default", () => {
    renderWithProviders(<HelpSuccessSection />);

    expect(
      screen.getByText(/Se precisar de ajuda com essa compra/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/acesse nossa página de ajuda/)
    ).toBeInTheDocument();
    expect(
      screen.queryByText(/Essa compra inclui produtos por assinatura/)
    ).not.toBeInTheDocument();
  });

  it("should render subscription help message when isSubscription is true", () => {
    renderWithProviders(<HelpSuccessSection isSubscription={true} />);

    expect(
      screen.getByText(/Essa compra inclui produtos por assinatura/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Se o pagamento foi feito com cartão/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Se precisar de ajuda com essa compra/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/acesse nossa página de ajuda/)
    ).toBeInTheDocument();
  });

  it("should render regular help message when isSubscription is false", () => {
    renderWithProviders(<HelpSuccessSection isSubscription={false} />);

    expect(
      screen.getByText(/Se precisar de ajuda com essa compra/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/acesse nossa página de ajuda/)
    ).toBeInTheDocument();
    expect(
      screen.queryByText(/Essa compra inclui produtos por assinatura/)
    ).not.toBeInTheDocument();
  });

  it("should have correct link href", () => {
    renderWithProviders(<HelpSuccessSection />);

    const link = screen.getByRole("link", {
      name: /acesse nossa página de ajuda/,
    });
    expect(link).toHaveAttribute(
      "href",
      "https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
    );
  });
});
