import { render } from "@testing-library/react";
import confetti from "canvas-confetti";

import { ConfettiSideCannons } from "@/components";

jest.mock("canvas-confetti", () => jest.fn());

jest.mock("lottie-react", () => ({
  __esModule: true,
  default: () => <div data-testid="lottie-animation">Animation</div>,
}));

describe("ConfettiSideCannons", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render without crashing", () => {
    render(<ConfettiSideCannons />);
  });

  it("should call confetti function multiple times", () => {
    jest.useFakeTimers();
    render(<ConfettiSideCannons />);

    jest.advanceTimersByTime(3000);

    expect(confetti).toHaveBeenCalled();
    expect(confetti).toHaveBeenCalledWith(
      expect.objectContaining({
        particleCount: 2,
        angle: 60,
        spread: 55,
        startVelocity: 60,
        origin: { x: 0, y: 0.5 },
      })
    );

    expect(confetti).toHaveBeenCalledWith(
      expect.objectContaining({
        particleCount: 2,
        angle: 120,
        spread: 55,
        startVelocity: 60,
        origin: { x: 1, y: 0.5 },
      })
    );
  });

  it("should stop calling confetti after 3 seconds", () => {
    jest.useFakeTimers();
    render(<ConfettiSideCannons />);

    jest.advanceTimersByTime(3000);

    const callsDuring3Seconds = jest.mocked(confetti).mock.calls.length;
    jest.advanceTimersByTime(1000);

    expect(jest.mocked(confetti).mock.calls.length).toBe(callsDuring3Seconds);
  });
});
