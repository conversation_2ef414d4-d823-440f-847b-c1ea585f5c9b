import { render, screen } from "@testing-library/react";

import { PaymentInfoSuccessCreditCard } from "@/components/PaymentInfoSuccessCreditCard";

describe("PaymentInfoSuccessCreditCard", () => {
  const mockData: any = {
    payment: {
      brand_url_image: "/images/visa.png",
      brand: "Visa",
      last_digits: "1234",
    },
  };

  it("should render correctly with the provided data", () => {
    render(<PaymentInfoSuccessCreditCard data={mockData} />);

    const cardImage = screen.getByAltText("Card brand");
    expect(cardImage).toBeInTheDocument();
    expect(cardImage).toHaveAttribute(
      "src",
      expect.stringContaining("visa.png")
    );

    expect(screen.getByText("*1234")).toBeInTheDocument();
  });

  it("should maintain the correct visual structure", () => {
    const { container } = render(
      <PaymentInfoSuccessCreditCard data={mockData} />
    );

    const wrapper = container.firstChild;
    expect(wrapper).toHaveClass("flex items-center gap-1");
  });

  it("should render the image with the correct dimensions", () => {
    render(<PaymentInfoSuccessCreditCard data={mockData} />);

    const image = screen.getByAltText("Card brand");
    expect(image).toHaveAttribute("width", "30");
    expect(image).toHaveAttribute("height", "30");
  });
});
