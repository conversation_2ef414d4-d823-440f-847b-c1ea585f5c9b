import "@testing-library/jest-dom";

import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";

import { Footer } from "@/components/Footer";

import { footerMessages, LocaleMessages } from "../datasets";

jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: { src: string; alt?: string } & Record<string, unknown>) => {
    return <img {...props} />;
  },
}));

function renderWithIntl(
  component: React.ReactNode,
  locale: string,
  messages: Record<string, any>
) {
  return render(
    <IntlProvider locale={locale} messages={messages}>
      {component}
    </IntlProvider>
  );
}

describe("Footer Component", () => {
  it.each(footerMessages)(
    "should render messages in %s",
    (locale: string, messages: LocaleMessages) => {
      renderWithIntl(<Footer organization="" />, locale, messages);

      const privacyPolicyLink = screen.getByRole("link", {
        name: messages.home.privacy_policy,
      });
      const termsOfUseLink = screen.getByRole("link", {
        name: messages.home.terms_of_use,
      });
      const allRightsReservedText = screen.getByText(
        new RegExp(messages.home.all_rights_reserved, "i")
      );
      const helpLink = screen.getByText(messages.home.help_page);

      expect(privacyPolicyLink).toBeInTheDocument();
      expect(privacyPolicyLink).toHaveAttribute(
        "href",
        "https://site.themembers.com.br/thebank/infoprodutor/termos-de-uso"
      );
      expect(termsOfUseLink).toBeInTheDocument();
      expect(termsOfUseLink).toHaveAttribute(
        "href",
        "https://site.themembers.com.br/thebank/comprador/termos-de-uso"
      );
      expect(helpLink).toBeInTheDocument();
      expect(helpLink).toHaveAttribute(
        "href",
        "https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
      );
      expect(allRightsReservedText).toBeInTheDocument();
    }
  );
});
