import { fireEvent, render, screen } from "@testing-library/react";

import { ImageWithFallback } from "@/components/ImageWithFallback";

describe("ImageWithFallback Component", () => {
  const fallbackUrl = "fallback.jpg";
  const url = "main.jpg";
  const altText = "My image";

  it("should render fallback image initially", () => {
    render(
      <ImageWithFallback
        url={url}
        fallbackUrl={fallbackUrl}
        alt={altText}
        className="my-class"
      />
    );

    const images = screen.getAllByAltText(altText);
    expect(images.length).toBe(2);

    const [fallbackImage, mainImage] = images;

    expect(fallbackImage).toHaveAttribute("src", fallbackUrl);
    expect(mainImage).toHaveAttribute("src", url);

    expect(fallbackImage).not.toHaveStyle("display: none");
    expect(mainImage).toHaveStyle("display: none");
  });

  it("should show main image after load and hide fallback", () => {
    render(
      <ImageWithFallback
        url={url}
        fallbackUrl={fallbackUrl}
        alt={altText}
        className="my-class"
      />
    );

    const images = screen.getAllByAltText(altText);
    const [fallbackImage, mainImage] = images;

    fireEvent.load(mainImage);

    expect(mainImage).not.toHaveStyle("display: none");
    expect(fallbackImage).toHaveStyle("display: none");
  });
});
