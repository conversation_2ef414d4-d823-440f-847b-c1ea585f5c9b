import "@testing-library/jest-dom";

import { render, screen } from "@testing-library/react";
import React from "react";

import { LoadingIndicator } from "@/components/LoadingIndicator";

describe("LoadingIndicator", () => {
  it("should render an element with role 'status'", () => {
    render(<LoadingIndicator />);
    const statusDiv = screen.getByRole("status");
    expect(statusDiv).toBeInTheDocument();
  });

  it("should render the SVG with default classes", () => {
    render(<LoadingIndicator />);
    const svg = screen.getByRole("status").querySelector("svg");
    expect(svg).toBeInTheDocument();

    expect(svg).toHaveClass("inline");
    expect(svg).toHaveClass("h-4");
    expect(svg).toHaveClass("w-4");
    expect(svg).toHaveClass("animate-spin");
    expect(svg).toHaveClass("fill-primary");
    expect(svg).toHaveClass("text-white/20");
  });

  it("should add the custom class when provided", () => {
    render(<LoadingIndicator className="my-custom-class" />);
    const svg = screen.getByRole("status").querySelector("svg");
    expect(svg).toBeInTheDocument();

    expect(svg).toHaveClass("my-custom-class");
    expect(svg).toHaveClass("inline");
  });
});
