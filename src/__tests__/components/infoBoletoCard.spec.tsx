import { screen } from "@testing-library/react";

import { InfoBoletoCard } from "@/components/InfoBoletoCard";

import { buyerFormMessages } from "../datasets/buyerFormMessages";
import { renderWithIntl } from "./productInfo.spec";

describe("InfoCard Component", () => {
  it.each(buyerFormMessages)(
    "renders the correct number of items in %s",
    (locale, messages) => {
      const items = [
        { text: "Item 1", iconSrc: "/path/to/icon1.svg", iconAlt: "Icon 1" },
        { text: "Item 2", iconSrc: "/path/to/icon2.svg", iconAlt: "Icon 2" },
      ];

      renderWithIntl(<InfoBoletoCard items={items} />, locale, messages);

      expect(screen.getByText("Item 1")).toBeInTheDocument();
      expect(screen.getByText("Item 2")).toBeInTheDocument();

      const images = screen.getAllByRole("img");
      expect(images).toHaveLength(2);

      expect(images[0]).toHaveAttribute("src", "/path/to/icon1.svg");
      expect(images[0]).toHaveAttribute("alt", "Icon 1");
      expect(images[1]).toHaveAttribute("src", "/path/to/icon2.svg");
      expect(images[1]).toHaveAttribute("alt", "Icon 2");
    }
  );

  it.each(buyerFormMessages)(
    "renders no items if the items array is empty in %s",
    (locale, messages) => {
      const items: { text: string; iconSrc: string; iconAlt: string }[] = [];

      renderWithIntl(<InfoBoletoCard items={items} />, locale, messages);

      expect(screen.queryByText(/Item/i)).not.toBeInTheDocument();
      expect(screen.queryAllByRole("img")).toHaveLength(0);
    }
  );

  it.each(buyerFormMessages)(
    "renders correct text content for each item in %s",
    (locale, messages) => {
      const items = [
        {
          text: "Custom Text 1",
          iconSrc: "/path/to/icon.svg",
          iconAlt: "Icon",
        },
        {
          text: "Custom Text 2",
          iconSrc: "/path/to/icon.svg",
          iconAlt: "Icon",
        },
      ];

      renderWithIntl(<InfoBoletoCard items={items} />, locale, messages);

      expect(screen.getByText("Custom Text 1")).toBeInTheDocument();
      expect(screen.getByText("Custom Text 2")).toBeInTheDocument();
    }
  );
});
