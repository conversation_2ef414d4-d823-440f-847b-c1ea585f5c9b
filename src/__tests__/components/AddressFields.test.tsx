import { fireEvent, render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";

import { useAddressStore } from "@/hooks/useAddressStore";
import { useBrazilianStates } from "@/hooks/useBrazilianStates";
import { useCepSearch } from "@/hooks/useCepSearch";
import { useHandleCEP } from "@/hooks/useHandleCEP";
import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";

import { AddressFields } from "../../components/CreditCardFormFields/AddressFields";

jest.mock("@/hooks/useBrazilianStates");
jest.mock("@/hooks/useCepSearch");
jest.mock("@/hooks/useHandleCEP");
jest.mock("@/hooks/useAddressStore");
jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

const mockStates = [
  { id: 1, name: "São Paulo" },
  { id: 2, name: "Rio de Janeiro" },
];

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm<Partial<BuyerFormValuesPartial>>({
    defaultValues: {
      billing_address: {
        zipcode: "",
        street: "",
        district: "",
        complement: "",
        city: "",
        state: "",
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("AddressFields", () => {
  beforeEach(() => {
    (useBrazilianStates as jest.Mock).mockReturnValue({
      data: mockStates,
    });

    (useCepSearch as jest.Mock).mockReturnValue({
      searchCep: jest.fn(),
      isSearching: false,
    });

    (useHandleCEP as jest.Mock).mockReturnValue({
      handleCEPChange: jest.fn(),
    });

    (useAddressStore as unknown as jest.Mock).mockReturnValue({
      showFields: false,
      setShowFields: jest.fn(),
      hasNumber: true,
      streetNumber: "",
      setHasNumber: jest.fn(),
      setStreetNumber: jest.fn(),
      streetNumberError: false,
      setStreetNumberError: jest.fn(),
    });
  });

  it("should render CEP input field", () => {
    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    const cepInput = screen.getByTestId("cep-input");
    expect(cepInput).toBeInTheDocument();
  });

  it("should show address fields when CEP is valid", () => {
    (useAddressStore as unknown as jest.Mock).mockReturnValue({
      showFields: true,
      setShowFields: jest.fn(),
      hasNumber: true,
      streetNumber: "",
      setHasNumber: jest.fn(),
      setStreetNumber: jest.fn(),
      streetNumberError: false,
      setStreetNumberError: jest.fn(),
    });

    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    expect(
      screen.getByPlaceholderText("street_placeholder")
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("district_placeholder")
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("number_placeholder")
    ).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("complement_placeholder")
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText("city_placeholder")).toBeInTheDocument();
    expect(screen.getByText("state")).toBeInTheDocument();
  });

  it("should handle CEP change and trigger search", () => {
    const mockSearchCep = jest.fn();
    (useCepSearch as jest.Mock).mockReturnValue({
      searchCep: mockSearchCep,
      isSearching: false,
    });

    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    const cepInput = screen.getByTestId("cep-input");
    fireEvent.change(cepInput, { target: { value: "12345678" } });

    expect(mockSearchCep).toHaveBeenCalledWith("12345678");
  });

  it("should handle street number checkbox change", () => {
    const mockSetHasNumber = jest.fn();
    const mockSetStreetNumberError = jest.fn();
    (useAddressStore as unknown as jest.Mock).mockReturnValue({
      showFields: true,
      setShowFields: jest.fn(),
      hasNumber: true,
      streetNumber: "",
      setHasNumber: mockSetHasNumber,
      setStreetNumber: jest.fn(),
      streetNumberError: true,
      setStreetNumberError: mockSetStreetNumberError,
    });

    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    const checkbox = screen.getByRole("checkbox");
    fireEvent.click(checkbox);

    expect(mockSetHasNumber).toHaveBeenCalledWith(false);
    expect(mockSetStreetNumberError).toHaveBeenCalledWith(false);
  });

  it("should show loading indicator when searching CEP", () => {
    (useCepSearch as jest.Mock).mockReturnValue({
      searchCep: jest.fn(),
      isSearching: true,
    });

    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    expect(screen.getByRole("status")).toBeInTheDocument();
  });

  it("should show error message when street number is required but not provided", () => {
    (useAddressStore as unknown as jest.Mock).mockReturnValue({
      showFields: true,
      setShowFields: jest.fn(),
      hasNumber: true,
      streetNumber: "",
      setHasNumber: jest.fn(),
      setStreetNumber: jest.fn(),
      streetNumberError: true,
      setStreetNumberError: jest.fn(),
    });

    render(
      <TestWrapper>
        <AddressFields />
      </TestWrapper>
    );

    expect(screen.getByText("without_number_error")).toBeInTheDocument();
  });
});
