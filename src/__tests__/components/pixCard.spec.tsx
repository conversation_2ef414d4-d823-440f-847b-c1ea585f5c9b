import { screen } from "@testing-library/react";

import { PixCard } from "@/components/PixCard";

import { buyerFormMessages } from "../datasets/buyerFormMessages";
import { renderWithIntl } from "./buyerForm.spec";

describe("PixCard Component", () => {
  it.each(buyerFormMessages)(
    "renders PixCard with correct translations for locale %s",
    (locale, messages) => {
      renderWithIntl(<PixCard />, locale, messages);

      expect(
        screen.getByText(messages.buyer_form.immediate_approval)
      ).toBeInTheDocument();

      expect(
        screen.getByText(messages.buyer_form.secure_transaction_bcb)
      ).toBeInTheDocument();

      expect(
        screen.getByText(messages.buyer_form.easy_payment_via_bank_app)
      ).toBeInTheDocument();

      const images = screen.getAllByRole("img", { name: "Check" });
      expect(images).toHaveLength(3);

      images.forEach(image => {
        expect(image).toHaveAttribute("src", "/images/check-icon.svg");
        expect(image).toHaveAttribute("alt", "Check");
      });
    }
  );
});
