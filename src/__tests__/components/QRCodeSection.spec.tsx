import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { cleanup, render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { QRCodeSection } from "@/components/QRCodeSection";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("QRCodeSection Component - Text Translations", () => {
  it.each(pixMessages)(
    "should render the correct text translation based on isVisibleOnSmall prop in %s",
    (locale, messages) => {
      renderWithIntl(
        <QRCodeSection isVisibleOnSmall={true} />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.pix_page.qr_code_instruction)
      ).toBeInTheDocument();

      cleanup();

      renderWithIntl(
        <QRCodeSection isVisibleOnSmall={false} />,
        locale,
        messages
      );

      const paymentInstructions = screen.getAllByText(
        messages.pix_page.payment_instructions
      );
      expect(paymentInstructions.length).toBeGreaterThan(0);
    }
  );
});
