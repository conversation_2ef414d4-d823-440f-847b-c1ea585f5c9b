import { screen } from "@testing-library/react";

import { BoletoCard } from "@/components/BoletoCard";

import { buyerFormMessages } from "../datasets/buyerFormMessages";
import { renderWithIntl } from "./buyerForm.spec";

describe("BoletoCard Component", () => {
  it.each(buyerFormMessages)(
    "renders BoletoCard with correct translations for locale %s",
    (locale, messages) => {
      renderWithIntl(<BoletoCard />, locale, messages);

      expect(
        screen.getByText(messages.buyer_form.pay_until_due_date)
      ).toBeInTheDocument();
      expect(
        screen.getByText(messages.buyer_form.payment_processing_time)
      ).toBeInTheDocument();
    }
  );
});
