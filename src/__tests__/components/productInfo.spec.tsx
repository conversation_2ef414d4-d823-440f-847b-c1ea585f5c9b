import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import ProductInfo from "@/components/ProductInfo";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const queryClient = new QueryClient();

  return render(
    <QueryClientProvider client={queryClient}>
      <IntlProvider locale={locale} messages={messages}>
        {component}
      </IntlProvider>
    </QueryClientProvider>
  );
}

describe("ProductInfo Component", () => {
  const mockInfoData: any = {
    title: "A Bíblia e o Dinheiro",
    platform: {
      name: "A Bíblia Comentada",
    },
  };

  it.each(pixMessages)(
    "should render the ProductInfo component with translations in %s",
    (locale, messages) => {
      renderWithIntl(<ProductInfo infoData={mockInfoData} />, locale, messages);

      expect(screen.getByText(messages.pix_page.product)).toBeInTheDocument();
      expect(screen.getByText(mockInfoData.title)).toBeInTheDocument();
      expect(screen.getByText(messages.pix_page.author)).toBeInTheDocument();
      expect(screen.getByText(mockInfoData.platform.name)).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should render additional product info items with translations in %s",
    (locale, messages) => {
      const additionalProductInfo = [
        { id: "1", title: "Bônus Especial", description: "Descrição do bônus" },
        {
          id: "2",
          title: "Conteúdo Exclusivo",
          description: "Descrição do conteúdo",
        },
      ];

      renderWithIntl(
        <ProductInfo
          infoData={mockInfoData}
          additionalProductInfo={additionalProductInfo}
        />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.pix_page.additional_items)
      ).toBeInTheDocument();

      additionalProductInfo.forEach(item => {
        expect(screen.getByText(item.title)).toBeInTheDocument();
      });

      expect(screen.getByText("1")).toBeInTheDocument();
      expect(screen.getByText("2")).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should not render additional items section when no additionalProductInfo is provided in %s",
    (locale, messages) => {
      renderWithIntl(<ProductInfo infoData={mockInfoData} />, locale, messages);

      expect(
        screen.queryByText(messages.pix_page.additional_items)
      ).not.toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should handle empty additionalProductInfo array in %s",
    (locale, messages) => {
      renderWithIntl(
        <ProductInfo infoData={mockInfoData} additionalProductInfo={[]} />,
        locale,
        messages
      );

      expect(
        screen.queryByText(messages.pix_page.additional_items)
      ).not.toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should handle missing platform name in %s",
    (locale, messages) => {
      const infoDataWithoutPlatform: any = {
        title: "A Bíblia e o Dinheiro",
      };

      renderWithIntl(
        <ProductInfo infoData={infoDataWithoutPlatform} />,
        locale,
        messages
      );

      expect(screen.getByText(messages.pix_page.product)).toBeInTheDocument();
      expect(
        screen.getByText(infoDataWithoutPlatform.title)
      ).toBeInTheDocument();
      expect(screen.getByText(messages.pix_page.author)).toBeInTheDocument();
      expect(screen.queryByText("A Bíblia Comentada")).not.toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should handle missing title in %s",
    (locale, messages) => {
      const infoDataWithoutTitle: any = {
        platform: {
          name: "A Bíblia Comentada",
        },
      };

      renderWithIntl(
        <ProductInfo infoData={infoDataWithoutTitle} />,
        locale,
        messages
      );

      expect(screen.getByText(messages.pix_page.product)).toBeInTheDocument();
      expect(
        screen.queryByText("A Bíblia e o Dinheiro")
      ).not.toBeInTheDocument();
      expect(screen.getByText(messages.pix_page.author)).toBeInTheDocument();
      expect(
        screen.getByText(infoDataWithoutTitle.platform.name)
      ).toBeInTheDocument();
    }
  );

  it.each(pixMessages)(
    "should handle additionalProductInfo items without description in %s",
    (locale, messages) => {
      const additionalProductInfo: any[] = [
        { id: "1", title: "Bônus Especial" },
        { id: "2", title: "Conteúdo Exclusivo" },
      ];

      renderWithIntl(
        <ProductInfo
          infoData={mockInfoData}
          additionalProductInfo={additionalProductInfo}
        />,
        locale,
        messages
      );

      additionalProductInfo.forEach(item => {
        expect(screen.getByText(item.title)).toBeInTheDocument();
      });
    }
  );

  it.each(pixMessages)(
    "should handle additionalProductInfo items with empty description in %s",
    (locale, messages) => {
      const additionalProductInfo: any[] = [
        { id: "1", title: "Bônus Especial", description: "" },
        { id: "2", title: "Conteúdo Exclusivo", description: "" },
      ];

      renderWithIntl(
        <ProductInfo
          infoData={mockInfoData}
          additionalProductInfo={additionalProductInfo}
        />,
        locale,
        messages
      );

      additionalProductInfo.forEach(item => {
        expect(screen.getByText(item.title)).toBeInTheDocument();
      });
    }
  );
});
