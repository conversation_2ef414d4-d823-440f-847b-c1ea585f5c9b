import "@testing-library/jest-dom";

import { fireEvent, render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { LanguageSwitcher } from "@/components/LanguageSwitcher";
import { Locales } from "@/types/locales";

import { languageSwitcherMessages } from "../datasets/languageSwitcherMessages";

jest.mock("next/navigation", () => ({
  useParams: jest.fn(),
  useRouter: jest.fn(),
  useLocale: jest.fn(),
  useSearchParams: jest.fn(),
  usePathname: jest.fn(),
}));

export const useRouter = require("next/navigation").useRouter;
export const useParams = require("next/navigation").useParams;
export const useLocale = require("next/navigation").useLocale;
export const useSearchParams = require("next/navigation").useSearchParams;
export const usePathname = require("next/navigation").usePathname;

export const mockReplace = jest.fn();

function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  return render(
    <IntlProvider locale={locale} messages={messages}>
      {component}
    </IntlProvider>
  );
}

describe("LanguageSwitcher Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useRouter.mockReturnValue({
      replace: mockReplace,
    });
    useParams.mockReturnValue({ id: "123" });
    useSearchParams.mockReturnValue({
      get: jest
        .fn()
        .mockImplementation(key => (key === "product" ? "456" : null)),
      forEach: jest.fn().mockImplementation(callback => {
        callback("456", "product");
      }),
    });
    usePathname.mockReturnValue("/pt-BR/123");
  });

  it.each(languageSwitcherMessages)(
    "changes the language to %s and updates the route correctly when isRetryOrderPage is false",
    (locale, messages) => {
      const nextLocaleMap: Record<Locales, Locales> = {
        [Locales.EN_US]: Locales.PT_BR,
        [Locales.PT_BR]: Locales.ES_ES,
        [Locales.ES_ES]: Locales.EN_US,
      };

      useLocale.mockReturnValue(locale);
      usePathname.mockReturnValue(`/${locale}/123`);
      renderWithIntl(<LanguageSwitcher />, locale, messages);

      const triggerButton = screen.getByTestId("language-switcher-button");
      fireEvent.click(triggerButton);

      const nextLocale = nextLocaleMap[locale];
      const newText = messages.language_switcher[nextLocale];
      const newOption = screen.getByText(newText);
      fireEvent.click(newOption);

      expect(screen.getByText(newText)).toBeInTheDocument();

      const newRoute = `/${nextLocale}/123?product=456`;
      expect(mockReplace).toHaveBeenCalledWith(newRoute);
    }
  );

  it.each(languageSwitcherMessages)(
    "changes the language to %s and updates the route correctly when isRetryOrderPage is true and there is a product in the query",
    (locale, messages) => {
      const nextLocaleMap: Record<Locales, Locales> = {
        [Locales.EN_US]: Locales.PT_BR,
        [Locales.PT_BR]: Locales.ES_ES,
        [Locales.ES_ES]: Locales.EN_US,
      };

      useLocale.mockReturnValue(locale);
      usePathname.mockReturnValue(`/${locale}/order/123`);
      renderWithIntl(
        <LanguageSwitcher isRetryOrderPage={true} />,
        locale,
        messages
      );

      const triggerButton = screen.getByTestId("language-switcher-button");
      fireEvent.click(triggerButton);

      const nextLocale = nextLocaleMap[locale];
      const newText = messages.language_switcher[nextLocale];
      const newOption = screen.getByText(newText);
      fireEvent.click(newOption);

      expect(screen.getByText(newText)).toBeInTheDocument();

      const newRoute = `/${nextLocale}/order/123?product=456`;
      expect(mockReplace).toHaveBeenCalledWith(newRoute);
    }
  );

  it.each(languageSwitcherMessages)(
    "changes the language to %s and updates the route correctly when isRetryOrderPage is true without a product in the query",
    (locale, messages) => {
      useSearchParams.mockReturnValue({
        get: jest.fn().mockReturnValue(null),
        forEach: jest.fn(),
      });

      const nextLocaleMap: Record<Locales, Locales> = {
        [Locales.EN_US]: Locales.PT_BR,
        [Locales.PT_BR]: Locales.ES_ES,
        [Locales.ES_ES]: Locales.EN_US,
      };

      useLocale.mockReturnValue(locale);
      usePathname.mockReturnValue(`/${locale}/order/123`);
      renderWithIntl(
        <LanguageSwitcher isRetryOrderPage={true} />,
        locale,
        messages
      );

      const triggerButton = screen.getByTestId("language-switcher-button");
      fireEvent.click(triggerButton);

      const nextLocale = nextLocaleMap[locale];
      const newText = messages.language_switcher[nextLocale];
      const newOption = screen.getByText(newText);
      fireEvent.click(newOption);

      expect(screen.getByText(newText)).toBeInTheDocument();

      const newRoute = `/${nextLocale}/order/123`;
      expect(mockReplace).toHaveBeenCalledWith(newRoute);
    }
  );

  it.each(languageSwitcherMessages)(
    "changes the language to %s and updates the route correctly when on subscriptions page with signature parameter",
    (locale, messages) => {
      useSearchParams.mockReturnValue({
        get: jest
          .fn()
          .mockImplementation(key => (key === "signature" ? "abc123" : null)),
        forEach: jest.fn().mockImplementation(callback => {
          callback("abc123", "signature");
        }),
      });

      const nextLocaleMap: Record<Locales, Locales> = {
        [Locales.EN_US]: Locales.PT_BR,
        [Locales.PT_BR]: Locales.ES_ES,
        [Locales.ES_ES]: Locales.EN_US,
      };

      useLocale.mockReturnValue(locale);
      usePathname.mockReturnValue(`/${locale}/subscriptions/123`);
      renderWithIntl(<LanguageSwitcher />, locale, messages);

      const triggerButton = screen.getByTestId("language-switcher-button");
      fireEvent.click(triggerButton);

      const nextLocale = nextLocaleMap[locale];
      const newText = messages.language_switcher[nextLocale];
      const newOption = screen.getByText(newText);
      fireEvent.click(newOption);

      expect(screen.getByText(newText)).toBeInTheDocument();

      const newRoute = `/${nextLocale}/subscriptions/123?signature=abc123`;
      expect(mockReplace).toHaveBeenCalledWith(newRoute);
    }
  );
});
