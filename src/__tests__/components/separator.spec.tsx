import { render, screen } from "@testing-library/react";

import { Separator } from "@/components/ui/Separator";

describe("Separator Component", () => {
  it("renders correctly with default props", () => {
    render(<Separator data-testid="separator" />);

    const separator = screen.getByTestId("separator");
    expect(separator).toBeInTheDocument();
    expect(separator).toHaveClass("shrink-0 bg-border h-[1px] w-full");
  });

  it("applies vertical orientation correctly", () => {
    render(<Separator data-testid="separator" orientation="vertical" />);

    const separator = screen.getByTestId("separator");
    expect(separator).toHaveClass("shrink-0 bg-border h-full w-[1px]");
    expect(separator).toHaveAttribute("data-orientation", "vertical");
  });

  it("adds custom class name", () => {
    render(<Separator className="custom-class" data-testid="separator" />);

    const separator = screen.getByTestId("separator");
    expect(separator).toHaveClass("custom-class");
  });

  it("sets the decorative prop correctly", () => {
    render(<Separator data-testid="separator" decorative={false} />);

    const separator = screen.getByTestId("separator");
    expect(separator).toBeInTheDocument();
    expect(separator).toHaveAttribute("role", "separator");
  });

  it("forwards additional props to the Separator", () => {
    render(<Separator data-testid="custom-separator" />);

    const separator = screen.getByTestId("custom-separator");
    expect(separator).toBeInTheDocument();
  });
});
