import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { act, render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import Header from "@/components/HeaderPix";
import { Locales } from "@/types/locales";

import { pixMessages } from "../datasets/pixMessages";

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("Header Component", () => {
  const mockPixExpiresIn = 3600;

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it.each(pixMessages)(
    "should render the Header component with translations in %s",
    (locale, messages) => {
      renderWithIntl(
        <Header pixExpiresIn={mockPixExpiresIn} />,
        locale,
        messages
      );

      const translatedText = messages.pix_page.finalize_with_pix;
      expect(screen.getByText(translatedText)).toBeInTheDocument();

      const initialTime = screen.getByText("60:00");
      expect(initialTime).toBeInTheDocument();

      act(() => {
        jest.advanceTimersByTime(1000);
      });

      const updatedTime = screen.getByText("59:59");
      expect(updatedTime).toBeInTheDocument();
    }
  );
});
