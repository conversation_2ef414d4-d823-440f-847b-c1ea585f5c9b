import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";

import { DocumentInput } from "@/components/DocumentInput";

describe("DocumentInput", () => {
  const setup = (errors = {}, defaultValues = { document: "" }) => {
    const TestComponent = () => {
      const methods = useForm({
        defaultValues: {
          document: defaultValues.document || "",
        },
        mode: "onChange",
      });

      return (
        <FormProvider {...methods}>
          <DocumentInput
            control={methods.control}
            errors={errors}
            label="CPF ou CNPJ"
            name="document"
            placeholder="000.000.000-00 ou 00.000.000/0000-00"
            type="text"
          />
        </FormProvider>
      );
    };

    render(<TestComponent />);
  };

  it("should render input field with correct label", () => {
    setup();
    expect(screen.getByLabelText(/CPF ou CNPJ/i)).toBeInTheDocument();
  });

  it("should display error message when there is an error", () => {
    const errors = {
      document: { message: "Documento inválido", type: "validate" },
    };

    setup(errors);

    expect(screen.getByText(/Documento inválido/i)).toBeInTheDocument();
  });

  it("should apply error styles to input field when there is an error", () => {
    const errors = {
      document: { message: "Documento inválido", type: "validate" },
    };

    setup(errors);

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    expect(documentInput).toHaveClass("border-destructive");
  });

  it("should not change the value if the input is empty", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    fireEvent.change(documentInput, { target: { value: "123" } });
    fireEvent.change(documentInput, { target: { value: "" } });

    expect(documentInput).toHaveValue("");
  });

  it("should handle empty input correctly", async () => {
    const user = userEvent.setup();
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);

    await user.type(documentInput, "123456");
    expect(documentInput).toHaveValue("123456");

    await user.clear(documentInput);
    expect(documentInput).toHaveValue("");
  });

  it("should handle empty input correctly and reset form value", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);

    fireEvent.change(documentInput, { target: { value: "123456789" } });
    expect(documentInput).toHaveValue("123456789");

    fireEvent.change(documentInput, { target: { value: "" } });
    expect(documentInput).toHaveValue("");

    fireEvent.change(documentInput, { target: { value: "12345678909" } });
    expect(documentInput).toHaveValue("123.456.789-09");
  });

  it("should format and set CPF value correctly", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    fireEvent.change(documentInput, { target: { value: "12345678909" } });

    expect(documentInput).toHaveValue("123.456.789-09");
  });

  it("should format and set CNPJ value correctly", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    fireEvent.change(documentInput, { target: { value: "12345678000195" } });

    expect(documentInput).toHaveValue("12.345.678/0001-95");
  });

  it("should not format the value if it is not a valid CPF or CNPJ", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    fireEvent.change(documentInput, { target: { value: "123" } });

    expect(documentInput).toHaveValue("123");
  });

  it("should handle whitespace-only input as empty", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);
    fireEvent.change(documentInput, { target: { value: "   " } });

    expect(documentInput).toHaveValue("");
  });

  it("should handle null/undefined input values", () => {
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);

    fireEvent.change(documentInput, { target: { value: null } });
    expect(documentInput).toHaveValue("");

    fireEvent.change(documentInput, { target: { value: undefined } });
    expect(documentInput).toHaveValue("");
  });

  it("should render without label when label prop is not provided", () => {
    const TestComponent = () => {
      const methods = useForm({
        defaultValues: { document: "" },
        mode: "onChange",
      });

      return (
        <FormProvider {...methods}>
          <DocumentInput
            control={methods.control}
            errors={{}}
            name="document"
            placeholder="000.000.000-00 ou 00.000.000/0000-00"
            type="text"
          />
        </FormProvider>
      );
    };

    render(<TestComponent />);

    expect(screen.queryByText(/CPF ou CNPJ/i)).not.toBeInTheDocument();

    expect(
      screen.getByPlaceholderText(/000.000.000-00 ou 00.000.000\/0000-00/i)
    ).toBeInTheDocument();
  });

  it("should handle rapid input changes correctly", async () => {
    const user = userEvent.setup();
    setup();

    const documentInput = screen.getByLabelText(/CPF ou CNPJ/i);

    await user.type(documentInput, "1");
    await user.type(documentInput, "2");
    await user.type(documentInput, "3");

    await user.clear(documentInput);
    expect(documentInput).toHaveValue("");

    await user.type(documentInput, "12345678909");
    expect(documentInput).toHaveValue("123.456.789-09");
  });
});
