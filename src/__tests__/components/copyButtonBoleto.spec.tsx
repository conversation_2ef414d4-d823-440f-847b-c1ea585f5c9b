import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { CopyButtonBoleto } from "@/components/CopyButtonBoleto";
import { Locales } from "@/types/locales";

import { boletoMessages } from "../datasets/boletoMessages";

const BoletoDataMock: any = {
  payment_method: "boleto",
  order_id: "123456789",
  status: "success",
  boleto: {
    amount: 159.99,
    barcode_data: "583912736482910237461928374659283746102938475619",
    barcode_image: "https://example.com/barcode/image",
    expires_date: "2023-01-01",
  },
  product: {
    id: "123456789",
    title: "Boleto",
    description: "Boleto description",
    platform: {
      name: "<PERSON><PERSON><PERSON>",
    },
  },
};

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

describe("CopyButtonBoleto Component", () => {
  it.each(boletoMessages)(
    "should render the CopyButtonBoleto component with translations in %s",
    (locale, messages) => {
      renderWithIntl(
        <CopyButtonBoleto
          boletoData={BoletoDataMock}
          text={messages.boleto_page.copy_boleto_code}
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.boleto_page.copy_boleto_code);
      expect(button).toBeInTheDocument();
    }
  );

  it.each(boletoMessages)(
    "should trigger onClick event when CopyButtonBoleto is clicked in %s",
    (locale, messages) => {
      const handleClick = jest.fn();
      renderWithIntl(
        <CopyButtonBoleto
          boletoData={BoletoDataMock}
          text={messages.boleto_page.copy_boleto_code}
          onClick={handleClick}
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.boleto_page.copy_boleto_code);
      fireEvent.click(button);

      expect(handleClick).toHaveBeenCalledTimes(1);
    }
  );

  it.each(boletoMessages)(
    "should copy boleto text and change button text when clicked in %s",
    async (locale, messages) => {
      renderWithIntl(
        <CopyButtonBoleto
          boletoData={BoletoDataMock}
          text={messages.boleto_page.copy_boleto_code}
          copiedTextKey="copied_text"
        />,
        locale,
        messages
      );

      const button = screen.getByText(messages.boleto_page.copy_boleto_code);

      const boletoCode = "583912736482910237461928374659283746102938475619";
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValueOnce(undefined),
        },
      });

      fireEvent.click(button);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(boletoCode);

      await waitFor(() =>
        expect(
          screen.getByText(messages.boleto_page.copied_text)
        ).toBeInTheDocument()
      );

      await waitFor(
        () =>
          expect(
            screen.getByText(messages.boleto_page.copy_boleto_code)
          ).toBeInTheDocument(),
        {
          timeout: 7000,
        }
      );
    },
    10000
  );
});
