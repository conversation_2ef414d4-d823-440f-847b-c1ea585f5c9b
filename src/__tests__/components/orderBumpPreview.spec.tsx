import { fireEvent, render, screen } from "@testing-library/react";

import { OrderBumpPreview } from "@/components/OrderBumpPreview";
import { ProductChargeType, ProductPeriodicity } from "@/types/product";

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => {
    const translations: Record<string, string> = {
      "subscription.renews_every": "Renova a cada",
      "subscription.month": "mês",
      "subscription.months": "meses",
      add_product: "Adicionar produto",
      remove_product: "Remover produto",
      add_subscription: "Adicionar assinatura",
      remove_subscription: "Remover assinatura",
    };
    return translations[key] || key;
  },
}));

describe("OrderBumpPreview", () => {
  const defaultProps = {
    bump: {
      id: "bump-123",
      product_id: "product-123",
      title: "Additional Product",
      position: 1,
      cta: "Special Offer",
      description: "Product description",
      price: 2790,
      charge_type: "oneoff" as any,
    },
    onSelect: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render correctly with basic props", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    expect(screen.getByText("Special Offer")).toBeInTheDocument();
    expect(screen.getByText(/Additional Product/)).toBeInTheDocument();
    expect(screen.getByText(/Product description/)).toBeInTheDocument();
  });

  it("should call onSelect when the checkbox is clicked", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    const checkbox = screen.getByTestId(
      `bump-checkbox-${defaultProps.bump.id}`
    );
    fireEvent.click(checkbox);

    expect(defaultProps.onSelect).toHaveBeenCalledWith(true);
  });

  it("should render without CTA when not provided", () => {
    const propsWithoutCta = {
      ...defaultProps,
      bump: {
        ...defaultProps.bump,
        cta: "",
      },
    };

    render(<OrderBumpPreview {...propsWithoutCta} />);

    expect(screen.queryByText("Special Offer")).not.toBeInTheDocument();
  });

  it("should display installment description when provided", () => {
    const propsWithInstallments = {
      ...defaultProps,
      selectedInstallmentBump: "4",
      installmentsDescription: {
        "bump-123": "4x of $419.85",
      },
    };

    render(<OrderBumpPreview {...propsWithInstallments} />);

    expect(screen.getByText("4x of $419.85")).toBeInTheDocument();
  });

  it("should display normal price when there are no installments", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    expect(screen.getByText("R$ 27,90")).toBeInTheDocument();
  });

  it("should have the correct data-bump-id", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    const bumpContainer = screen
      .getByTestId(`bump-checkbox-${defaultProps.bump.id}`)
      .closest("[data-bump-id]");
    expect(bumpContainer).toHaveAttribute("data-bump-id", defaultProps.bump.id);
  });

  it("should uncheck the checkbox when clicked twice", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    const checkbox = screen.getByTestId(
      `bump-checkbox-${defaultProps.bump.id}`
    );

    fireEvent.click(checkbox);
    expect(defaultProps.onSelect).toHaveBeenLastCalledWith(true);

    fireEvent.click(checkbox);
    expect(defaultProps.onSelect).toHaveBeenLastCalledWith(false);
  });

  it("should apply CSS classes correctly when there is no CTA", () => {
    const propsWithoutCta = {
      ...defaultProps,
      bump: {
        ...defaultProps.bump,
        cta: "",
      },
    };

    render(<OrderBumpPreview {...propsWithoutCta} />);

    const contentDiv = screen
      .getByText(/Additional Product/)
      .closest("div.border-t");
    expect(contentDiv).toHaveClass("border-t", "border-green-500/50");
  });

  it("should display subscription interval for subscription bumps", () => {
    const subscriptionBumpProps = {
      ...defaultProps,
      bump: {
        ...defaultProps.bump,
        charge_type: ProductChargeType.SUBSCRIPTION,
        subscription: {
          interval: 1,
          periodicity: ProductPeriodicity.MONTHLY,
          trial_days: null,
        },
      },
    };

    render(<OrderBumpPreview {...subscriptionBumpProps} />);

    expect(screen.getByText("Renova a cada 1 mês")).toBeInTheDocument();
    expect(screen.getByText("R$ 27,90")).toBeInTheDocument();
  });

  it("should show installment description for subscription bumps when available", () => {
    const subscriptionBumpProps = {
      ...defaultProps,
      bump: {
        ...defaultProps.bump,
        charge_type: ProductChargeType.SUBSCRIPTION,
        subscription: {
          interval: 1,
          periodicity: ProductPeriodicity.MONTHLY,
          trial_days: null,
        },
      },
      selectedInstallmentBump: "4",
      installmentsDescription: {
        "bump-123": "4x of $419.85",
      },
    };

    render(<OrderBumpPreview {...subscriptionBumpProps} />);

    expect(screen.getByText("4x of $419.85")).toBeInTheDocument();
    expect(screen.getByText("Renova a cada 1 mês")).toBeInTheDocument();
  });

  it("should show correct button text for subscription bumps", () => {
    const subscriptionBumpProps = {
      ...defaultProps,
      bump: {
        ...defaultProps.bump,
        charge_type: ProductChargeType.SUBSCRIPTION,
        subscription: {
          interval: 1,
          periodicity: ProductPeriodicity.MONTHLY,
          trial_days: null,
        },
      },
    };

    render(<OrderBumpPreview {...subscriptionBumpProps} />);

    expect(screen.getByText("Adicionar assinatura")).toBeInTheDocument();

    const checkbox = screen.getByTestId(
      `bump-checkbox-${defaultProps.bump.id}`
    );
    fireEvent.click(checkbox);
    expect(screen.getByText("Remover assinatura")).toBeInTheDocument();
  });

  it("should show correct button text for regular product bumps", () => {
    render(<OrderBumpPreview {...defaultProps} />);

    expect(screen.getByText("Adicionar produto")).toBeInTheDocument();

    const checkbox = screen.getByTestId(
      `bump-checkbox-${defaultProps.bump.id}`
    );
    fireEvent.click(checkbox);

    expect(screen.getByText("Remover produto")).toBeInTheDocument();
  });
});
