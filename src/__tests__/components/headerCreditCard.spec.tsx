import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";

import HeaderCreditCard from "@/components/HeaderCreditCard";
import { Locales } from "@/types/locales";

import { creditCardMessages } from "../datasets/creditCardMessages";

describe("HeaderCreditCard Component", () => {
  it.each(creditCardMessages)(
    "should render the header with correct translation in %s",
    (locale: Locales, messages: Record<string, any>) => {
      render(
        <IntlProvider locale={locale} messages={messages}>
          <HeaderCreditCard />
        </IntlProvider>
      );

      const translatedText = messages.credit_card_page.header;
      expect(screen.getByText(translatedText)).toBeInTheDocument();
    }
  );
});
