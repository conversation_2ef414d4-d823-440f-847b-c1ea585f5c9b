import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { IntlProvider } from "next-intl";
import { ReactNode } from "react";

import { SubscriptionHeader } from "@/components/SubscriptionHeader";
import { Installment } from "@/types/instalmentsList";
import { Locales } from "@/types/locales";
import { Product } from "@/types/product";
import { SubscriptionApiResponse } from "@/types/subscription";

jest.mock("@/components/ProductCard", () => ({
  __esModule: true,
  default: ({ product }: any) => (
    <div data-testid="product-card">
      <span>{product.title}</span>
    </div>
  ),
}));

jest.mock("@/components/LanguageSwitcher", () => ({
  LanguageSwitcher: () => (
    <div data-testid="language-switcher">
      <span>Inglês</span>
      <span>Português</span>
      <span>Espanhol</span>
    </div>
  ),
}));

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useParams: () => ({
    locale: "pt-BR",
  }),
}));

export function renderWithIntl(
  component: ReactNode,
  locale: Locales,
  messages: Record<string, any>
) {
  const Wrapper = () => {
    const queryClient = new QueryClient();

    return (
      <QueryClientProvider client={queryClient}>
        <IntlProvider locale={locale} messages={messages}>
          {component}
        </IntlProvider>
      </QueryClientProvider>
    );
  };

  return render(<Wrapper />);
}

const mockSubscriptionApiData: SubscriptionApiResponse = {
  price: "100",
  credit_card_installments: [],
  product: {
    title: "Produto Teste",
    payment_rules: ["credit_card"],
    platform: {
      name: "Test Platform",
      url: "https://test.com",
    },
  },
  periodicity: {
    type: "monthly",
    label: "Mensal",
    interval: 1,
    cycle_count: 1,
  },
  subscriber: {
    name: "João Silva",
    email: "<EMAIL>",
    phone: "9999",
    document: "901",
  },
};

const mockProduct: Product = {
  id: "prod_123",
  organization_id: "org_123",
  title: "Produto Teste",
  description: "Descrição do produto",
  image: null,
  price: 100,
  installment_details: "Detalhes do parcelamento",
  warranty_days: 30,
  sales_page: "https://sales.com",
  charge_type: "subscription" as any,
  payment_methods: ["credit_card"],
  platform: {
    id: "platform_123",
    name: "Test Platform",
    url: "https://test.com",
  },
  organization: {
    id: "org_123",
    status: {
      value: "completed",
      message: "Completed",
    },
  },
  bumps: [],
  pixels: [],
};

const mockProductInstallments = {
  installments: [] as Installment[],
  bumps: [],
};

const mockMessages = {
  signature: {
    title: "Atualize o pagamento da sua assinatura",
    your_data: "Seus dados",
    full_name: "Nome completo",
    email: "E-mail",
    phone: "Celular",
    document: "Documento",
  },
  language_switcher: {
    english: "Inglês",
    portuguese: "Português",
    spanish: "Espanhol",
  },
  buyer_form: {
    author: "Autor:",
    at_view: "à vista",
    or: "ou",
  },
};

describe("SubscriptionHeader Component", () => {
  it("should render the subscription header with title", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(
      screen.getByText("Atualize o pagamento da sua assinatura")
    ).toBeInTheDocument();
  });

  it("should render subscriber data correctly", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("Seus dados")).toBeInTheDocument();
    expect(screen.getByText("Nome completo")).toBeInTheDocument();
    expect(screen.getByText("João Silva")).toBeInTheDocument();
    expect(screen.getByText("E-mail")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("Celular")).toBeInTheDocument();
    expect(screen.getByText("Documento")).toBeInTheDocument();
  });

  it("should render ProductCard component", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("product-card")).toBeInTheDocument();
    expect(screen.getByText("Produto Teste")).toBeInTheDocument();
  });

  it("should render LanguageSwitcher component", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByTestId("language-switcher")).toBeInTheDocument();
    expect(screen.getByText("Inglês")).toBeInTheDocument();
    expect(screen.getByText("Português")).toBeInTheDocument();
    expect(screen.getByText("Espanhol")).toBeInTheDocument();
  });

  it("should display masked phone number", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("(**) *****-9999")).toBeInTheDocument();
  });

  it("should display masked document", () => {
    renderWithIntl(
      <SubscriptionHeader
        subscriptionApiData={mockSubscriptionApiData}
        product={mockProduct}
        productInstallments={mockProductInstallments}
      />,
      Locales.PT_BR,
      mockMessages
    );

    expect(screen.getByText("***.***.**9-01")).toBeInTheDocument();
  });
});
