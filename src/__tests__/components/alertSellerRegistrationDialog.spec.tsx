import { fireEvent, screen } from "@testing-library/react";

import { AlertSellerRegistrationDialog } from "@/components/ui/AlertSellerRegistrationDialog";
import { Locales } from "@/types/locales";

import { buyerFormMessages } from "../datasets/buyerFormMessages";
import { renderWithIntl } from "./buyerForm.spec";

describe("AlertSellerRegistrationDialog Component", () => {
  const mockSetShowModal = jest.fn();
  const mockOnClose = jest.fn();

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    setShowModal: mockSetShowModal,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it.each(buyerFormMessages)(
    "renders AlertSellerRegistrationDialog with correct translations for locale %s",
    (locale, messages) => {
      renderWithIntl(
        <AlertSellerRegistrationDialog {...defaultProps} />,
        locale,
        messages
      );

      expect(
        screen.getByText(messages.buyer_form.seller_registration_error_title)
      ).toBeInTheDocument();

      expect(
        screen.getByText(
          messages.buyer_form.seller_registration_error_description
        )
      ).toBeInTheDocument();

      expect(
        screen.getByText(messages.buyer_form.understood)
      ).toBeInTheDocument();
    }
  );

  it("calls setShowModal when clicking the understood button", () => {
    renderWithIntl(
      <AlertSellerRegistrationDialog {...defaultProps} />,
      Locales.PT_BR,
      buyerFormMessages[0][1]
    );

    const button = screen.getByText(
      buyerFormMessages[0][1].buyer_form.understood
    );
    fireEvent.click(button);

    expect(mockSetShowModal).toHaveBeenCalledWith(false);
  });

  it("calls onClose when dialog is closed", () => {
    renderWithIntl(
      <AlertSellerRegistrationDialog {...defaultProps} />,
      Locales.PT_BR,
      buyerFormMessages[0][1]
    );

    const dialog = screen.getByRole("dialog");
    fireEvent.keyDown(dialog, { key: "Escape" });

    expect(mockOnClose).toHaveBeenCalled();
  });
});
