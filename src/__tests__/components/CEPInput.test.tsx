import { fireEvent, render, screen } from "@testing-library/react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { CEPInput } from "../../components/CEPInput";

const TestForm = () => {
  const schema = z.object({
    cep: z.string(),
  });

  const {
    register,
    formState: { errors },
  } = useForm<z.infer<typeof schema>>({
    defaultValues: {
      cep: "",
    },
  });

  return (
    <CEPInput
      type="text"
      label="CEP"
      name="cep"
      register={register}
      errors={errors}
      placeholder="00000-000"
    />
  );
};

const DisabledTestForm = () => {
  const schema = z.object({
    cep: z.string(),
  });

  const {
    register,
    formState: { errors },
  } = useForm<z.infer<typeof schema>>({
    defaultValues: {
      cep: "",
    },
  });

  return (
    <CEPInput
      type="text"
      label="CEP"
      name="cep"
      register={register}
      errors={errors}
      disabled={true}
    />
  );
};

const CustomClassTestForm = () => {
  const schema = z.object({
    cep: z.string(),
  });

  const {
    register,
    formState: { errors },
  } = useForm<z.infer<typeof schema>>({
    defaultValues: {
      cep: "",
    },
  });

  return (
    <CEPInput
      type="text"
      label="CEP"
      name="cep"
      register={register}
      errors={errors}
      className="custom-class"
    />
  );
};

describe("CEPInput", () => {
  it("should render the input with correct props", () => {
    render(<TestForm />);

    const input = screen.getByTestId("cep-input");
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("type", "text");
    expect(input).toHaveAttribute("placeholder", "00000-000");
    expect(input).toHaveAttribute("maxLength", "9");
  });

  it("should render with label", () => {
    render(<TestForm />);

    const label = screen.getByText("CEP");
    expect(label).toBeInTheDocument();
  });

  it("should be disabled when disabled prop is true", () => {
    render(<DisabledTestForm />);

    const input = screen.getByTestId("cep-input");
    expect(input).toBeDisabled();
  });

  it("should handle CEP change when handleCEPChange is provided", () => {
    const handleCEPChange = jest.fn();
    render(
      <CEPInput
        type="text"
        label="CEP"
        name="cep"
        register={jest.fn()}
        errors={{}}
        handleCEPChange={handleCEPChange}
      />
    );

    const input = screen.getByTestId("cep-input");
    fireEvent.change(input, { target: { value: "12345-678" } });
    expect(handleCEPChange).toHaveBeenCalled();
  });

  it("should apply custom className when provided", () => {
    render(<CustomClassTestForm />);

    const input = screen.getByTestId("cep-input");
    expect(input).toHaveClass("custom-class");
  });
});
