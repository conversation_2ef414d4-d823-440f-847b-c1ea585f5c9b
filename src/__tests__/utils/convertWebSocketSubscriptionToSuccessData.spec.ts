import {
  convertWebSocketSubscriptionToSuccessData,
  SuccessPageData,
  WebSocketSubscriptionData,
} from "@/utils/subscriptionUtils/convertWebSocketSubscriptionToSuccessData";

describe("convertWebSocketSubscriptionToSuccessData", () => {
  const mockSubscriptionId = "SB1B4H1AR";
  const mockCustomerEmail = "<EMAIL>";

  const mockWebSocketData: WebSocketSubscriptionData = {
    payment: {
      method: "credit_card",
      amount: "10000",
      status: "approved",
      description: "1x de R$ 100,00",
      brand: "visa",
      first_digits: 1234,
      last_digits: 5678,
      installments: 1,
    },
    product: {
      title: "Test Product",
      description: "Test Product Description",
      platform: {
        name: "Test Platform",
        url: "https://test-platform.com",
      },
    },
  };

  it("should convert WebSocket subscription data to success page data with all fields", () => {
    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    const expected: SuccessPageData = {
      order_id: mockSubscriptionId,
      payment_method: "credit_card",
      customer_email: mockCustomerEmail,
      product: {
        id: mockSubscriptionId,
        title: "Test Product",
        description: "Test Product Description",
        platform: {
          name: "Test Platform",
          url: "https://test-platform.com",
        },
      },
      bumps: [],
      payment: {
        status: "approved",
        description: "1x de R$ 100,00",
        brand: "visa",
        brand_image_url: null,
        first_digits: 1234,
        last_digits: 5678,
        installments: 1,
      },
    };

    expect(result).toEqual(expected);
  });

  it("should use default customer email when not provided", () => {
    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      mockSubscriptionId
    );

    expect(result.customer_email).toBe("Customer");
  });

  it("should handle WebSocket data without optional payment fields", () => {
    const webSocketDataWithoutOptionalFields: WebSocketSubscriptionData = {
      payment: {
        method: "pix",
        amount: "5000",
        status: "pending",
        description: "PIX payment",
      },
      product: {
        title: "PIX Product",
        description: "PIX Product Description",
        platform: {
          name: "PIX Platform",
          url: "https://pix-platform.com",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      webSocketDataWithoutOptionalFields,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.brand).toBeUndefined();
    expect(result.payment.first_digits).toBeUndefined();
    expect(result.payment.last_digits).toBeUndefined();
    expect(result.payment.installments).toBeUndefined();
    expect(result.payment.brand_image_url).toBeNull();
  });

  it("should handle different payment methods", () => {
    const boletoData: WebSocketSubscriptionData = {
      payment: {
        method: "boleto",
        amount: "15000",
        status: "pending",
        description: "Boleto payment",
      },
      product: {
        title: "Boleto Product",
        description: "Boleto Product Description",
        platform: {
          name: "Boleto Platform",
          url: "https://boleto-platform.com",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      boletoData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment_method).toBe("boleto");
    expect(result.payment.status).toBe("pending");
    expect(result.payment.description).toBe("Boleto payment");
  });

  it("should handle different payment statuses", () => {
    const failedPaymentData: WebSocketSubscriptionData = {
      ...mockWebSocketData,
      payment: {
        ...mockWebSocketData.payment,
        status: "failed",
        description: "Payment failed",
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      failedPaymentData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.status).toBe("failed");
    expect(result.payment.description).toBe("Payment failed");
  });

  it("should always set bumps as empty array", () => {
    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.bumps).toEqual([]);
    expect(Array.isArray(result.bumps)).toBe(true);
  });

  it("should use subscription ID as both order_id and product.id", () => {
    const customSubscriptionId = "CUSTOM_SUB_123";

    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      customSubscriptionId,
      mockCustomerEmail
    );

    expect(result.order_id).toBe(customSubscriptionId);
    expect(result.product.id).toBe(customSubscriptionId);
  });

  it("should preserve all product information", () => {
    const customProductData: WebSocketSubscriptionData = {
      payment: {
        method: "credit_card",
        amount: "20000",
        status: "approved",
        description: "2x de R$ 100,00",
      },
      product: {
        title: "Custom Product Title",
        description:
          "Custom Product Description with special characters: áéíóú",
        platform: {
          name: "Custom Platform Name",
          url: "https://custom-platform.example.com/path",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      customProductData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.product.title).toBe("Custom Product Title");
    expect(result.product.description).toBe(
      "Custom Product Description with special characters: áéíóú"
    );
    expect(result.product.platform.name).toBe("Custom Platform Name");
    expect(result.product.platform.url).toBe(
      "https://custom-platform.example.com/path"
    );
  });

  it("should handle credit card with multiple installments", () => {
    const installmentData: WebSocketSubscriptionData = {
      ...mockWebSocketData,
      payment: {
        ...mockWebSocketData.payment,
        description: "12x de R$ 50,00",
        installments: 12,
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      installmentData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.installments).toBe(12);
    expect(result.payment.description).toBe("12x de R$ 50,00");
  });

  it("should handle different credit card brands", () => {
    const mastercardData: WebSocketSubscriptionData = {
      ...mockWebSocketData,
      payment: {
        ...mockWebSocketData.payment,
        brand: "mastercard",
        first_digits: 5555,
        last_digits: 4444,
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      mastercardData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.brand).toBe("mastercard");
    expect(result.payment.first_digits).toBe(5555);
    expect(result.payment.last_digits).toBe(4444);
  });

  it("should always set brand_image_url to null", () => {
    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.brand_image_url).toBeNull();
  });

  it("should handle empty strings in payment data", () => {
    const emptyStringData: WebSocketSubscriptionData = {
      payment: {
        method: "",
        amount: "",
        status: "",
        description: "",
      },
      product: {
        title: "",
        description: "",
        platform: {
          name: "",
          url: "",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      emptyStringData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment_method).toBe("");
    expect(result.payment.status).toBe("");
    expect(result.payment.description).toBe("");
    expect(result.product.title).toBe("");
    expect(result.product.description).toBe("");
    expect(result.product.platform.name).toBe("");
    expect(result.product.platform.url).toBe("");
  });

  it("should handle zero values for numeric fields", () => {
    const zeroValueData: WebSocketSubscriptionData = {
      payment: {
        method: "credit_card",
        amount: "0",
        status: "approved",
        description: "0x de R$ 0,00",
        first_digits: 0,
        last_digits: 0,
        installments: 0,
      },
      product: {
        title: "Zero Value Product",
        description: "Product with zero values",
        platform: {
          name: "Zero Platform",
          url: "https://zero.com",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      zeroValueData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(result.payment.first_digits).toBe(0);
    expect(result.payment.last_digits).toBe(0);
    expect(result.payment.installments).toBe(0);
  });

  it("should handle special characters in all string fields", () => {
    const specialCharData: WebSocketSubscriptionData = {
      payment: {
        method: "credit_card",
        amount: "10000",
        status: "approved",
        description: "Pagamento com acentos: áéíóú ç ñ",
        brand: "visa",
      },
      product: {
        title: "Produto com Acentos: áéíóú ç ñ & símbolos @#$%",
        description: "Descrição com caracteres especiais: <>&\"'",
        platform: {
          name: "Plataforma Especial: áéíóú",
          url: "https://plataforma-especial.com.br/path?param=value&other=test",
        },
      },
    };

    const result = convertWebSocketSubscriptionToSuccessData(
      specialCharData,
      mockSubscriptionId,
      "usuá******************"
    );

    expect(result.customer_email).toBe("usuá******************");
    expect(result.payment.description).toBe("Pagamento com acentos: áéíóú ç ñ");
    expect(result.product.title).toBe(
      "Produto com Acentos: áéíóú ç ñ & símbolos @#$%"
    );
    expect(result.product.description).toBe(
      "Descrição com caracteres especiais: <>&\"'"
    );
    expect(result.product.platform.name).toBe("Plataforma Especial: áéíóú");
    expect(result.product.platform.url).toBe(
      "https://plataforma-especial.com.br/path?param=value&other=test"
    );
  });

  it("should handle very long subscription ID", () => {
    const longSubscriptionId =
      "VERY_LONG_SUBSCRIPTION_ID_WITH_MANY_CHARACTERS_AND_NUMBERS_123456789";

    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      longSubscriptionId,
      mockCustomerEmail
    );

    expect(result.order_id).toBe(longSubscriptionId);
    expect(result.product.id).toBe(longSubscriptionId);
  });

  it("should maintain data types correctly", () => {
    const result = convertWebSocketSubscriptionToSuccessData(
      mockWebSocketData,
      mockSubscriptionId,
      mockCustomerEmail
    );

    expect(typeof result.order_id).toBe("string");
    expect(typeof result.payment_method).toBe("string");
    expect(typeof result.customer_email).toBe("string");
    expect(typeof result.product.id).toBe("string");
    expect(typeof result.product.title).toBe("string");
    expect(typeof result.product.description).toBe("string");
    expect(typeof result.product.platform.name).toBe("string");
    expect(typeof result.product.platform.url).toBe("string");
    expect(typeof result.payment.status).toBe("string");
    expect(typeof result.payment.description).toBe("string");

    expect(typeof result.payment.first_digits).toBe("number");
    expect(typeof result.payment.last_digits).toBe("number");
    expect(typeof result.payment.installments).toBe("number");

    expect(Array.isArray(result.bumps)).toBe(true);

    expect(result.payment.brand_image_url).toBeNull();
  });
});
