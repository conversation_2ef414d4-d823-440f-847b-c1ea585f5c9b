import formatToBRL from "@/utils/formatting/formatToBRL";
import addPeriods from "@/utils/mathUtils/addPeriods";

jest.mock("../../utils/mathUtils/addPeriods", () => ({
  __esModule: true,
  default: jest.fn(value => {
    return value.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
  }),
}));

describe("formatToBRL", () => {
  beforeEach(() => {
    require("../../utils/mathUtils/addPeriods").default.mockClear();
  });

  it("should format a number as BRL currency with two decimal places", () => {
    expect(formatToBRL(1234)).toBe("R$ 1.234,00");
    expect(formatToBRL(56789.1)).toBe("R$ 56.789,10");
    expect(formatToBRL(0)).toBe("R$ 0,00");
  });

  it("should format a string number as BRL currency", () => {
    expect(formatToBRL("987654")).toBe("R$ 987.654,00");
    expect(formatToBRL("1234.56")).toBe("R$ 1.234,56");
  });

  it("should handle numbers with more than two decimal places correctly", () => {
    expect(formatToBRL(1234.5678)).toBe("R$ 1.234,57");
  });

  it("should handle invalid input gracefully by converting to zero", () => {
    expect(formatToBRL("invalid")).toBe("R$ 0,00");
    expect(formatToBRL("")).toBe("R$ 0,00");
    expect(formatToBRL("" as any)).toBe("R$ 0,00");
    expect(formatToBRL(undefined as unknown as string)).toBe("R$ 0,00");
  });

  it("should call addPeriods function to format thousands", () => {
    formatToBRL(1234567.89);
    expect(addPeriods).toHaveBeenCalledWith("1234567,89");
  });
});
