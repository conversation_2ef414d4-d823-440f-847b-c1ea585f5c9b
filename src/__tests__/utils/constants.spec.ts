import {
  CNPJ_PATTERN,
  CPF_PATTERN,
  NON_NUMERIC_REGEX,
  PHONE_PATTERN,
} from "@/utils/miscellaneous";

describe("Regex and Constants Tests", () => {
  it("should remove all non-numeric characters using NON_NUMERIC_REGEX", () => {
    const input = "ABC123!@#456";
    const result = input.replace(NON_NUMERIC_REGEX, "");
    expect(result).toBe("123456");
  });

  it("should validate CPF_PATTERN correctly", () => {
    expect(CPF_PATTERN.test("123.456.789-09")).toBeTruthy();
    expect(CPF_PATTERN.test("12345678909")).toBeTruthy();
    expect(CPF_PATTERN.test("123.456.789-0")).toBeFalsy();
    expect(CPF_PATTERN.test("1234567890")).toBeFalsy();
    expect(CPF_PATTERN.test("123.456.789/09")).toBeFalsy();
  });

  it("should validate CNPJ_PATTERN correctly", () => {
    expect(CNPJ_PATTERN.test("12.345.678/0001-95")).toBeTruthy();
    expect(CNPJ_PATTERN.test("12345678000195")).toBeTruthy();
    expect(CNPJ_PATTERN.test("12.345.678/0001-9")).toBeFalsy();
    expect(CNPJ_PATTERN.test("1234567800019")).toBeFalsy();
    expect(CNPJ_PATTERN.test("12.************/95")).toBeFalsy();
  });

  it("should validate PHONE_PATTERN correctly", () => {
    expect(PHONE_PATTERN.test("+55 (11) 91234-5678")).toBeTruthy();
    expect(PHONE_PATTERN.test("11 91234 5678")).toBeTruthy();
    expect(PHONE_PATTERN.test("91234-5678")).toBeTruthy();
    expect(PHONE_PATTERN.test("1234-567")).toBeFalsy();
    expect(PHONE_PATTERN.test("(11) 12345-678")).toBeFalsy();
    expect(PHONE_PATTERN.test("91234567890")).toBeFalsy();
  });
});
