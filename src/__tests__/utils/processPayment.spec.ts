import { MalgaService } from "@/services/malga";
import { OrchestratorResponse } from "@/types/fetchOrchestratorData";
import { PaymentOption } from "@/types/paymentOptions";
import { processPayment } from "@/utils/createOrderSubmitHandler/processPayment";

jest.mock("@/utils/stringUtils", () => ({
  clearString: jest.fn((value: string) => value.replace(/\D/g, "")),
}));

describe("processPayment", () => {
  const mockSetTokenizationError = jest.fn();
  const buyerData: any = {
    cardHolderName: "John Doe",
    cardNumber: "****************",
    cardCvv: "123",
    cardExpirationDateMonth: "12",
    cardExpirationDateYear: "2030",
  };
  const orchestratorData: any = {
    client_id: "client_id_123",
    public_key: "public_key_123",
  };

  it("should tokenize credit card and return tokenId", async () => {
    mockTokenizeCreditCard.mockResolvedValueOnce({ tokenId: "mock-token-id" });

    const result = await processPayment(
      buyerData,
      PaymentOption.CreditCard,
      orchestratorData,
      mockSetTokenizationError
    );

    expect(result).toBe("mock-token-id");
    expect(mockTokenizeCreditCard).toHaveBeenCalledTimes(1);
    expect(mockTokenizeCreditCard).toHaveBeenCalledWith(
      {
        cardHolderName: "JOHN DOE",
        cardNumber: "****************",
        cardCvv: "123",
        cardExpirationDate: "12/2030",
      },
      orchestratorData.client_id,
      orchestratorData.public_key
    );
  });

  let mockTokenizeCreditCard: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    mockTokenizeCreditCard = jest.fn();
    MalgaService.prototype.tokenizeCreditCard = mockTokenizeCreditCard;
  });

  it("should return undefined if selectedOption is not CreditCard", async () => {
    const result = await processPayment(
      buyerData,
      PaymentOption.Pix,
      orchestratorData,
      mockSetTokenizationError
    );

    expect(result).toBeUndefined();
    expect(MalgaService.prototype.tokenizeCreditCard).not.toHaveBeenCalled();
  });

  it("should handle tokenization failure", async () => {
    mockTokenizeCreditCard.mockRejectedValueOnce(
      new Error("Tokenization failed")
    );

    await expect(
      processPayment(
        buyerData,
        PaymentOption.CreditCard,
        orchestratorData,
        mockSetTokenizationError
      )
    ).rejects.toThrow("Tokenization failed");

    expect(mockSetTokenizationError).toHaveBeenCalledWith(true);
    expect(mockTokenizeCreditCard).toHaveBeenCalledTimes(1);
  });

  it("should return undefined if orchestratorData is not provided", async () => {
    const result = await processPayment(
      buyerData,
      PaymentOption.CreditCard,
      null as unknown as OrchestratorResponse,
      mockSetTokenizationError
    );

    expect(result).toBeUndefined();
    expect(MalgaService.prototype.tokenizeCreditCard).not.toHaveBeenCalled();
  });
});
