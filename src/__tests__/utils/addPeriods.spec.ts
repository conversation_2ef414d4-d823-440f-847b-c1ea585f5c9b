import addPeriods from "@/utils/mathUtils/addPeriods";

describe("addPeriods", () => {
  it("should add periods as thousand separators for a number string", () => {
    expect(addPeriods("1000")).toBe("1.000");
    expect(addPeriods("1000000")).toBe("1.000.000");
    expect(addPeriods("123456789")).toBe("123.456.789");
  });

  it("should return the original string if it contains less than 4 characters", () => {
    expect(addPeriods("123")).toBe("123");
    expect(addPeriods("12")).toBe("12");
    expect(addPeriods("1")).toBe("1");
  });

  it("should correctly handle string with mixed numbers and characters", () => {
    expect(addPeriods("1000abc")).toBe("1.000abc");
    expect(addPeriods("12345xyz")).toBe("12.345xyz");
  });

  it("should not modify a string that does not contain numbers", () => {
    expect(addPeriods("abcdef")).toBe("abcdef");
    expect(addPeriods("")).toBe("");
  });

  it("should handle large numbers as a string correctly", () => {
    expect(addPeriods("9876543210")).toBe("9.876.543.210");
  });
});
