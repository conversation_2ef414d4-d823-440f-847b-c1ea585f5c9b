import { SubscriptionRenewalFormValuesPartial } from "@/schemas/subscriptionRenewalSchema";
import { PaymentOption } from "@/types/paymentOptions";
import { buildSubscriptionPayload } from "@/utils/createOrderSubmitHandler/buildSubscriptionPayload";

jest.mock("@thebank/creditcard-js", () => ({
  getCreditCardNameByNumber: jest.fn(() => "visa"),
}));

jest.mock("@/utils/stringUtils", () => ({
  clearString: jest.fn((value: string) => value.replace(/\D/g, "")),
}));

describe("buildSubscriptionPayload", () => {
  const subscriptionId = "sub_123456789";
  const token = "tok_123456789";
  const signature = "sig_123456789";

  const mockSubscriptionData: SubscriptionRenewalFormValuesPartial = {
    cardNumber: "****************",
    cardHolderName: "<PERSON> Doe",
    cardCvv: "123",
    cardExpirationDateMonth: "12",
    cardExpirationDateYear: "2030",
    instalment: 3,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should build payload for credit card payment with all required data", () => {
    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
        credit_card: {
          token,
          installments: 3,
          first_digits: "4111",
          last_digits: "1111",
          brand: "visa",
        },
      },
    });
  });

  it("should build payload for credit card payment with default instalment when not provided", () => {
    const subscriptionDataWithoutInstalment = {
      ...mockSubscriptionData,
      instalment: undefined,
    };

    const result = buildSubscriptionPayload(
      subscriptionDataWithoutInstalment,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
        credit_card: {
          token,
          installments: 1,
          first_digits: "4111",
          last_digits: "1111",
          brand: "visa",
        },
      },
    });
  });

  it("should build payload for Pix payment without credit card data", () => {
    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.Pix
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.Pix,
      },
    });
  });

  it("should build payload for boleto payment without credit card data", () => {
    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.Boleto
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.Boleto,
      },
    });
  });

  it("should include signature when provided", () => {
    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.CreditCard,
      token,
      signature
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
        credit_card: {
          token,
          installments: 3,
          first_digits: "4111",
          last_digits: "1111",
          brand: "visa",
        },
      },
      signature,
    });
  });

  it("should not include credit card data when card number is missing", () => {
    const subscriptionDataWithoutCardNumber = {
      ...mockSubscriptionData,
      cardNumber: undefined,
    };

    const result = buildSubscriptionPayload(
      subscriptionDataWithoutCardNumber,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
      },
    });
  });

  it("should not include credit card data when token is missing", () => {
    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.CreditCard
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
      },
    });
  });

  it("should not include credit card data when card number and token are missing", () => {
    const subscriptionDataWithoutCardNumber = {
      ...mockSubscriptionData,
      cardNumber: undefined,
    };

    const result = buildSubscriptionPayload(
      subscriptionDataWithoutCardNumber,
      subscriptionId,
      PaymentOption.CreditCard
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
      },
    });
  });

  it("should handle empty subscription data", () => {
    const emptySubscriptionData: SubscriptionRenewalFormValuesPartial = {};

    const result = buildSubscriptionPayload(
      emptySubscriptionData,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
      },
    });
  });

  it("should handle card number with special characters", () => {
    const subscriptionDataWithSpecialChars = {
      ...mockSubscriptionData,
      cardNumber: "4111-1111-1111-1111",
    };

    const result = buildSubscriptionPayload(
      subscriptionDataWithSpecialChars,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      subscription_id: subscriptionId,
      payment: {
        method: PaymentOption.CreditCard,
        credit_card: {
          token,
          installments: 3,
          first_digits: "4111",
          last_digits: "1111",
          brand: "visa",
        },
      },
    });
  });

  it("should handle different card brands", () => {
    const { getCreditCardNameByNumber } = require("@thebank/creditcard-js");
    getCreditCardNameByNumber.mockReturnValue("MasterCard");

    const result = buildSubscriptionPayload(
      mockSubscriptionData,
      subscriptionId,
      PaymentOption.CreditCard,
      token
    );

    expect(result.payment.credit_card?.brand).toBe("mastercard");
  });
});
