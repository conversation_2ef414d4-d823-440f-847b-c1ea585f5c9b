import { isRepeatedArray } from "@/utils/validations";

describe("isRepeatedArray", () => {
  it("should return true for an array with all identical elements", () => {
    const repeatedArray = [1, 1, 1, 1];
    expect(isRepeatedArray(repeatedArray)).toBeTruthy();
  });

  it("should return false for an array with different elements", () => {
    const nonRepeatedArray = [1, 2, 1, 1];
    expect(isRepeatedArray(nonRepeatedArray)).toBeFalsy();
  });

  it("should return true for an empty array", () => {
    const emptyArray: number[] = [];
    expect(isRepeatedArray(emptyArray)).toBeTruthy();
  });

  it("should return true for an array with a single element", () => {
    const singleElementArray = [42];
    expect(isRepeatedArray(singleElementArray)).toBeTruthy();
  });

  it("should return true for an array with identical string elements", () => {
    const repeatedStringArray = ["a", "a", "a"];
    expect(isRepeatedArray(repeatedStringArray)).toBeTruthy();
  });

  it("should return false for an array with different string elements", () => {
    const nonRepeatedStringArray = ["a", "b", "a"];
    expect(isRepeatedArray(nonRepeatedStringArray)).toBeFalsy();
  });
});
