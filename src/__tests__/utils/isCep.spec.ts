import { isCEP } from "@/utils/validations/isCEP";

describe("isCEP", () => {
  describe("should return true for valid CEPs", () => {
    const valids = ["12345678", "87654321", "12.345-678", "00.000-000"];

    test.each(valids)('isCEP("%s") → true', cep => {
      expect(isCEP(cep)).toBe(true);
    });
  });

  describe("should return false for invalid CEPs", () => {
    const invalids = [
      "",
      "1234",
      "123456789",
      "12.3456-78",
      "12.345-6789",
      "abcdefghi",
      "12.345-67a",
      "1234.567-8",
    ];

    test.each(invalids)('isCEP("%s") → false', cep => {
      expect(isCEP(cep)).toBe(false);
    });
  });
});
