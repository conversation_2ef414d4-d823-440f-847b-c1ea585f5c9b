import { clearString } from "@/utils/stringUtils";

describe("clearString", () => {
  it("should remove all non-numeric characters from the string", () => {
    expect(clearString("123-456-789")).toBe("123456789");
  });

  it("should return an empty string if input is an empty string", () => {
    expect(clearString("")).toBe("");
  });

  it("should return the original string if it contains only numbers", () => {
    expect(clearString("1234567890")).toBe("1234567890");
  });

  it("should remove special characters and letters", () => {
    expect(clearString("abc123!@#456def")).toBe("123456");
  });

  it("should handle strings with spaces", () => {
    expect(clearString("123 456 789")).toBe("123456789");
  });
});
