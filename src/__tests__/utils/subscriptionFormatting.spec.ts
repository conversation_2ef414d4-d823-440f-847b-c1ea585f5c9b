import {
  formatSubscriptionInterval,
  formatSubscriptionPeriodicity,
} from "@/utils/formatting/subscriptionFormatting";

const t = (key: string) => {
  const translations: Record<string, string> = {
    "subscription.renews_every": "Renews every",
    "subscription.every": "Every",
    "subscription.month": "month",
    "subscription.months": "months",
  };
  return translations[key] || key;
};

describe("formatSubscriptionInterval", () => {
  it("should return 'Renews every 1 month' when the interval is 1", () => {
    const result = formatSubscriptionInterval(1, "monthly", t);
    expect(result).toBe("Renews every 1 month");
  });

  it("should return 'Renews every 2 months' when the interval is 2", () => {
    const result = formatSubscriptionInterval(2, "monthly", t);
    expect(result).toBe("Renews every 2 months");
  });
});

describe("formatSubscriptionPeriodicity", () => {
  it("should return 'Every 1 month' when the interval is 1", () => {
    const result = formatSubscriptionPeriodicity(1, "monthly", t);
    expect(result).toBe("/ Every 1 month");
  });

  it("should return 'Every 2 months' when the interval is 2", () => {
    const result = formatSubscriptionPeriodicity(2, "monthly", t);
    expect(result).toBe("/ Every 2 months");
  });
});
