import { delay } from "@/utils/delay";

jest.useFakeTimers();

describe("delay", () => {
  beforeAll(() => {
    jest.spyOn(global, "setTimeout");
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it("should resolve after the specified delay", async () => {
    const ms = 1000;
    const promise = delay(ms);

    jest.advanceTimersByTime(ms);

    await expect(promise).resolves.toBeUndefined();
  });
});
