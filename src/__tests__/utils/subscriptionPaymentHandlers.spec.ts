import { PaymentOption } from "@/types/paymentOptions";
import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";
import {
  handleSyncBoletoSubscriptionPayment,
  handleSyncCreditCardSubscriptionPayment,
  handleSyncPixSubscriptionPayment,
} from "@/utils/subscriptionRenewalSubmitHandler/subscriptionPaymentHandlers";

describe("subscriptionPaymentHandlers", () => {
  const mockBaseParams = {
    selectedOption: PaymentOption.Pix,
    locale: "pt",
    subscriptionId: "sub_123",
  };

  describe("handleSyncPixSubscriptionPayment", () => {
    it("should handle PIX payment with complete response", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.Pix,
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 1000,
        },
        pix: {
          qr_code: "mock_qr_code",
          copy_paste: "mock_copy_paste",
          expires: 3600,
        },
        product: { id: "product_123", name: "Test Product" },
      };

      const result = handleSyncPixSubscriptionPayment({
        response: mockResponse,
        ...mockBaseParams,
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_123",
          payment_method: PaymentOption.Pix,
          status: SubscriptionRenewalStatus.APPROVED,
          pix: {
            amount: 1000,
            qr_code: "mock_qr_code",
            copy_paste: "mock_copy_paste",
            expires_in: 3600,
          },
          product: { id: "product_123", name: "Test Product" },
          bumps: [],
        },
        path: "/pt/order/pix",
      });
    });

    it("should handle PIX payment with missing transaction data (line 23 coverage)", () => {
      const mockResponse: any = {
        pix: {
          qr_code: "mock_qr_code",
          copy_paste: "mock_copy_paste",
          expires: 3600,
        },
        product: { id: "product_123" },
      };

      const result = handleSyncPixSubscriptionPayment({
        response: mockResponse,
        ...mockBaseParams,
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_123",
          payment_method: PaymentOption.Pix,
          status: SubscriptionRenewalStatus.PENDING, // Fallback to PENDING (line 23)
          pix: {
            amount: 0,
            qr_code: "mock_qr_code",
            copy_paste: "mock_copy_paste",
            expires_in: 3600,
          },
          product: { id: "product_123" },
          bumps: [],
        },
        path: "/pt/order/pix",
      });
    });

    it("should handle PIX payment with completely empty response", () => {
      const mockResponse: any = {};

      const result = handleSyncPixSubscriptionPayment({
        response: mockResponse,
        ...mockBaseParams,
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_123",
          payment_method: PaymentOption.Pix,
          status: SubscriptionRenewalStatus.PENDING,
          pix: {
            amount: 0,
            qr_code: "",
            copy_paste: "",
            expires_in: 0,
          },
          product: undefined,
          bumps: [],
        },
        path: "/pt/order/pix",
      });
    });
  });

  describe("handleSyncBoletoSubscriptionPayment", () => {
    it("should handle Boleto payment with complete response", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 1500,
        },
        boleto: {
          barcode_image_url: "http://example.com/barcode.png",
          barcode_data: "12345678901234567890",
          expires_date: "2024-12-31T23:59:59Z",
        },
        subscriber: {
          name: "João Silva",
        },
        product: { id: "product_456", name: "Test Product" },
      };

      const result = handleSyncBoletoSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.Boleto,
        locale: "pt",
        subscriptionId: "sub_456",
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_456",
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.APPROVED,
          customer_email: "João Silva",
          boleto: {
            amount: 1500,
            barcode_image_url: "http://example.com/barcode.png",
            barcode_data: "12345678901234567890",
            expires_date: "31/12/2024",
          },
          product: { id: "product_456", name: "Test Product" },
          bumps: [],
        },
        path: "/pt/order/boleto",
      });
    });

    it("should handle Boleto payment with missing transaction data (line 58 coverage)", () => {
      const mockResponse: any = {
        boleto: {
          barcode_image_url: "http://example.com/barcode.png",
          barcode_data: "12345678901234567890",
          expires_date: "2024-12-31T23:59:59Z",
        },
        subscriber: {
          name: "João Silva",
        },
        product: { id: "product_456" },
      };

      const result = handleSyncBoletoSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.Boleto,
        locale: "en",
        subscriptionId: "sub_456",
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_456",
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.PENDING, // Fallback to PENDING (line 58)
          customer_email: "João Silva",
          boleto: {
            amount: 0,
            barcode_image_url: "http://example.com/barcode.png",
            barcode_data: "12345678901234567890",
            expires_date: "31/12/2024",
          },
          product: { id: "product_456" },
          bumps: [],
        },
        path: "/en/order/boleto",
      });
    });

    it("should handle Boleto payment with empty date", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.PENDING,
          amount: 1500,
        },
        boleto: {
          barcode_image_url: "",
          barcode_data: "",
          expires_date: undefined,
        },
        subscriber: {
          name: "Customer Test",
        },
        product: null,
      };

      const result = handleSyncBoletoSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.Boleto,
        locale: "es",
        subscriptionId: "sub_789",
      });

      expect(result).toEqual({
        response: {
          subscription_id: "sub_789",
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.PENDING,
          customer_email: "Customer Test",
          boleto: {
            amount: 1500,
            barcode_image_url: "",
            barcode_data: "",
            expires_date: "",
          },
          product: null,
          bumps: [],
        },
        path: "/es/order/boleto",
      });
    });

    it("should handle Boleto payment with missing subscriber name", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.Boleto,
          status: SubscriptionRenewalStatus.PENDING,
          amount: 1500,
        },
        boleto: {
          barcode_image_url: "test.png",
          barcode_data: "123456",
          expires_date: "2024-12-31",
        },
        subscriber: {},
        product: { id: "test" },
      };

      const result = handleSyncBoletoSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.Boleto,
        locale: "pt",
        subscriptionId: "sub_999",
      });

      expect(result?.response.customer_email).toBe("Customer");
    });
  });

  describe("handleSyncCreditCardSubscriptionPayment", () => {
    it("should handle approved credit card payment", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.CreditCard,
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 2000,
        },
        card: {
          last_four: "1234",
          brand: "Visa",
        },
        product: { id: "product_789", name: "Premium Product" },
      };

      const result = handleSyncCreditCardSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.CreditCard,
        locale: "pt",
        subscriptionId: "sub_789",
      });

      expect(result).toEqual({
        response: mockResponse,
        path: "/pt/success",
      });
    });

    it("should return null for non-approved credit card payment (lines 83-84 coverage)", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.CreditCard,
          status: SubscriptionRenewalStatus.PENDING,
          amount: 2000,
        },
        card: {
          last_four: "1234",
          brand: "Visa",
        },
        product: { id: "product_789", name: "Premium Product" },
      };

      const result = handleSyncCreditCardSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.CreditCard,
        locale: "pt",
        subscriptionId: "sub_789",
      });

      expect(result).toBeNull(); // Lines 83-84 coverage
    });

    it("should return null for failed credit card payment", () => {
      const mockResponse: any = {
        transaction: {
          payment_method: PaymentOption.CreditCard,
          status: SubscriptionRenewalStatus.FAILED,
          amount: 2000,
        },
        card: {
          last_four: "1234",
          brand: "Visa",
        },
        product: { id: "product_789", name: "Premium Product" },
      };

      const result = handleSyncCreditCardSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.CreditCard,
        locale: "en",
        subscriptionId: "sub_999",
      });

      expect(result).toBeNull();
    });

    it("should return null when transaction is missing", () => {
      const mockResponse: any = {
        card: {
          last_four: "1234",
          brand: "Visa",
        },
        product: { id: "product_789", name: "Premium Product" },
      };

      const result = handleSyncCreditCardSubscriptionPayment({
        response: mockResponse,
        selectedOption: PaymentOption.CreditCard,
        locale: "pt",
        subscriptionId: "sub_789",
      });

      expect(result).toBeNull();
    });
  });
});
