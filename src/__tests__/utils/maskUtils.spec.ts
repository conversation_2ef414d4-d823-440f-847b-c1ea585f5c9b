import { maskDocument, maskPhone } from "@/utils/masks";

describe("maskPhone", () => {
  it("should mask the suffix when it contains exactly 4 digits", () => {
    expect(maskPhone("1234")).toBe("(**) *****-1234");
    expect(maskPhone(" ab12 34 ")).toBe("(**) *****-1234");
  });

  it("should not mask when there are not exactly 4 digits", () => {
    expect(maskPhone("123")).toBe("123");
    expect(maskPhone("12345")).toBe("12345");
    expect(maskPhone("abcd")).toBe("abcd");
  });

  it("should preserve the original input when not masked", () => {
    const input = "(99) 123";
    expect(maskPhone(input)).toBe(input);
  });
});

describe("maskDocument", () => {
  it("should mask the suffix when it contains exactly 3 digits", () => {
    expect(maskDocument("123")).toBe("***.***.**1-23");
    expect(maskDocument(" a1b2c3 ")).toBe("***.***.**1-23");
  });

  it("should not mask when there are not exactly 3 digits", () => {
    expect(maskDocument("12")).toBe("12");
    expect(maskDocument("1234")).toBe("1234");
    expect(maskDocument("abc")).toBe("abc");
  });
});
