import { getRemaining } from "@/utils/mathUtils";

describe("getRemaining", () => {
  it("should return 0 when value % 11 is less than 2", () => {
    expect(getRemaining(1)).toBe(0);
    expect(getRemaining(0)).toBe(0);
    expect(getRemaining(11)).toBe(0);
  });

  it("should return 11 - (value % 11) when value % 11 is 2 or more", () => {
    expect(getRemaining(3)).toBe(8);
    expect(getRemaining(10)).toBe(1);
    expect(getRemaining(23)).toBe(0);
  });

  it("should handle negative values correctly", () => {
    expect(getRemaining(-1)).toBe(0);
    expect(getRemaining(-3)).toBe(0);
  });
});
