import { mapToNumeric } from "@/utils/stringUtils";

describe("mapToNumeric", () => {
  it("should remove all non-numeric characters", () => {
    const input = "ABC123!@#456";
    const result = mapToNumeric(input);
    expect(result).toBe("123456");
  });

  it("should return an empty string when input has no numeric characters", () => {
    const input = "ABC!@#";
    const result = mapToNumeric(input);
    expect(result).toBe("");
  });

  it("should return the same string if it contains only numeric characters", () => {
    const input = "1234567890";
    const result = mapToNumeric(input);
    expect(result).toBe("1234567890");
  });

  it("should handle an empty string input", () => {
    const input = "";
    const result = mapToNumeric(input);
    expect(result).toBe("");
  });
});
