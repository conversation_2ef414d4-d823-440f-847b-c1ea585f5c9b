import { DocumentType } from "@/types/document";
import { formatBuyerData } from "@/utils/createOrderSubmitHandler/formatBuyerData";

describe("formatBuyerData", () => {
  it("should return the same data if haveBrazilianDocument is true", () => {
    const inputData: any = {
      name: "<PERSON>",
      email: "<EMAIL>",
      confirm_email: "<EMAIL>",
      document: {
        type: DocumentType.CPF,
        number: "123.456.789-09",
      },
      phone: { ddi: "+55", number: "999999999" },
    };

    const result = formatBuyerData(inputData, true);

    expect(result).toEqual(inputData);
  });

  it("should omit the document field if haveBrazilianDocument is false", () => {
    const inputData: any = {
      name: "<PERSON>",
      email: "<EMAIL>",
      confirm_email: "<EMAIL>",
      document: {
        type: DocumentType.CPF,
        number: "123.456.789-09",
      },
      phone: { ddi: "+44", number: "777777777" },
    };

    const expectedOutput: any = {
      name: "<PERSON>e",
      email: "<EMAIL>",
      confirm_email: "<EMAIL>",
      phone: { ddi: "+44", number: "777777777" },
    };

    const result = formatBuyerData(inputData, false);

    expect(result).toEqual(expectedOutput);
  });

  it("should handle empty data gracefully when haveBrazilianDocument is true", () => {
    const inputData: any = {};

    const result = formatBuyerData(inputData, true);

    expect(result).toEqual(inputData);
  });

  it("should handle empty data gracefully when haveBrazilianDocument is false", () => {
    const inputData: any = {};

    const result = formatBuyerData(inputData, false);

    expect(result).toEqual(inputData);
  });
});
