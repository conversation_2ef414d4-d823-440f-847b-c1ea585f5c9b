import { mapToNumeric } from "@/utils/stringUtils/mapToNumeric";
import { formatToCEP } from "@/utils/validations/formatToCEP";

describe("formatToCEP", () => {
  it("should format a numeric string correctly", () => {
    expect(formatToCEP("12345678")).toBe("12345-678");
  });

  it("should remove non-numeric characters and format correctly", () => {
    expect(formatToCEP("12345-678")).toBe("12345-678");
    expect(formatToCEP("12a345b678")).toBe("12345-678");
  });

  it("should keep extra digits after the pattern (more than 8 digits)", () => {
    expect(formatToCEP("123456789")).toBe("12345-6789");
  });

  it("should not format if there are not enough digits for the pattern", () => {
    expect(formatToCEP("1234")).toBe("1234");
    expect(formatToCEP("12345")).toBe("12345");
  });

  it("should return an empty string for empty input or without digits", () => {
    expect(formatToCEP("")).toBe("");
    expect(formatToCEP("abc")).toBe("");
  });
});

describe("mapToNumeric", () => {
  it("should remove all non-numeric characters", () => {
    expect(mapToNumeric("12345-678")).toBe("12345678");
    expect(mapToNumeric("abc123def")).toBe("123");
  });

  it("should return an empty string if there are no digits", () => {
    expect(mapToNumeric("abc")).toBe("");
  });
});
