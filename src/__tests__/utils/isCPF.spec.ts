import { isCPF } from "@/utils/validations";

import {
  invalidCheckDigitsCPFs,
  invalidFormatCPFs,
  repeatedSequenceCPFs,
  validCPFs,
  validCPFsWithoutFormatting,
} from "../datasets/cpfDataset";

describe("isCPF", () => {
  it.each(validCPFs)("should return true for a valid CPF: %s", validCPF => {
    expect(isCPF(validCPF)).toBeTruthy();
  });

  it.each(invalidFormatCPFs)(
    "should return false for an invalid CPF format: %s",
    invalidCPF => {
      expect(isCPF(invalidCPF)).toBeFalsy();
    }
  );

  it.each(repeatedSequenceCPFs)(
    "should return false for a repeated sequence CPF: %s",
    repeatedCPF => {
      expect(isCPF(repeatedCPF)).toBeFalsy();
    }
  );

  it.each(invalidCheckDigitsCPFs)(
    "should return false for an invalid CPF with incorrect check digits: %s",
    invalidCheckDigitsCPF => {
      expect(isCPF(invalidCheckDigitsCPF)).toBeFalsy();
    }
  );

  it.each(validCPFsWithoutFormatting)(
    "should return true for a valid CPF without formatting: %s",
    validCPF => {
      expect(isCPF(validCPF)).toBeTruthy();
    }
  );

  it("should return false for an empty string", () => {
    expect(isCPF("")).toBeFalsy();
  });
});
