import { DocumentType } from "@/types/document";
import { OrderPayload } from "@/types/order";
import { PaymentOption } from "@/types/paymentOptions";
import { buildOrderPayload } from "@/utils/createOrderSubmitHandler/buildOrderPayload";

jest.mock("@thebank/creditcard-js", () => ({
  getCreditCardNameByNumber: jest.fn(() => "visa"),
}));

jest.mock("@/utils/stringUtils", () => ({
  clearString: jest.fn((value: string) => value.replace(/\D/g, "")),
}));

describe("buildOrderPayload", () => {
  const productId = "12345";
  const buyerData: any = {
    name: "<PERSON>",
    email: "<EMAIL>",
    confirm_email: "<EMAIL>",
    document: {
      type: DocumentType.CPF,
      number: "123.456.789-09",
    },
    phone: {
      ddi: "+55",
      number: "*********",
    },
    cardNumber: "****************",
    cardCvv: "123",
    cardExpirationDateMonth: "12",
    cardExpirationDateYear: "2030",
    cardHolderName: "John Doe",
    instalment: 3,
  };

  it("should build payload for credit card payment", () => {
    const token = "mocked-token";

    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.CreditCard,
      token
    );

    expect(result).toEqual({
      products: [{ id: productId, quantity: 1 }],
      main_product_id: productId,
      buyer: {
        name: buyerData.name,
        email: buyerData.email,
        confirm_email: buyerData.confirm_email,
        document: buyerData.document,
        phone: {
          ddi: buyerData.phone.ddi,
          number: buyerData.phone.number,
        },
      },
      payment: {
        method: PaymentOption.CreditCard,
        credit_card: {
          token,
          installments: buyerData.instalment,
          first_digits: "4111",
          last_digits: "1111",
          brand: "visa",
        },
      },
    });
  });

  it("should build payload for Pix payment", () => {
    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.Pix
    );

    expect(result).toEqual({
      products: [{ id: productId, quantity: 1 }],
      main_product_id: productId,
      buyer: {
        name: buyerData.name,
        email: buyerData.email,
        confirm_email: buyerData.confirm_email,
        document: buyerData.document,
        phone: {
          ddi: buyerData.phone.ddi,
          number: buyerData.phone.number,
        },
      },
      payment: {
        method: PaymentOption.Pix,
      },
    });
  });

  it("should build payload for boleto payment", () => {
    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.Boleto
    );

    expect(result).toEqual({
      products: [{ id: productId, quantity: 1 }],
      main_product_id: productId,
      buyer: {
        name: buyerData.name,
        email: buyerData.email,
        confirm_email: buyerData.confirm_email,
        document: buyerData.document,
        phone: {
          ddi: buyerData.phone.ddi,
          number: buyerData.phone.number,
        },
      },
      payment: {
        method: PaymentOption.Boleto,
      },
    });
  });

  it("should omit document if it is not present", () => {
    const buyerDataWithoutDocument = {
      ...buyerData,
      document: undefined,
    };

    const result: OrderPayload = buildOrderPayload(
      buyerDataWithoutDocument,
      productId,
      PaymentOption.Pix
    );

    expect(result.buyer.document).toBeUndefined();
  });

  it("should set default phone values when phone is not provided", () => {
    const buyerDataWithoutPhone = {
      ...buyerData,
      phone: undefined,
    };

    const result: OrderPayload = buildOrderPayload(
      buyerDataWithoutPhone,
      productId,
      PaymentOption.Pix
    );

    expect(result.buyer.phone).toEqual({ ddi: "", number: "" });
  });

  it("should omit document if it is not present", () => {
    const buyerDataWithoutDocument = {
      ...buyerData,
      document: undefined,
    };

    const result: OrderPayload = buildOrderPayload(
      buyerDataWithoutDocument,
      productId,
      PaymentOption.Pix
    );

    expect(result.buyer.document).toBeUndefined();
  });

  it("should include product bumps when selectedProductBumpsId is provided", () => {
    const selectedProductBumpsId = ["bump1", "bump2"];

    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.CreditCard,
      "token-123",
      selectedProductBumpsId
    );

    expect(result.products).toEqual([
      { id: productId, quantity: 1 },
      { id: "bump1", quantity: 1 },
      { id: "bump2", quantity: 1 },
    ]);
  });

  it("should handle empty selectedProductBumpsId array", () => {
    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.CreditCard,
      "token-123",
      []
    );

    expect(result.products).toEqual([{ id: productId, quantity: 1 }]);
  });

  it("should handle undefined selectedProductBumpsId", () => {
    const result: OrderPayload = buildOrderPayload(
      buyerData,
      productId,
      PaymentOption.CreditCard,
      "token-123"
    );

    expect(result.products).toEqual([{ id: productId, quantity: 1 }]);
  });
});
