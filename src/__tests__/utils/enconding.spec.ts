import { fromBase64, toBase64 } from "@/utils/stringUtils/enconding";

describe("Base64 Utilities", () => {
  it("should encode a string to Base64 correctly", () => {
    const input = "Hello, world!";
    const expectedOutput = "SGVsbG8sIHdvcmxkIQ==";

    const result = toBase64(input);

    expect(result).toBe(expectedOutput);
  });

  it("should decode a Base64 string correctly", () => {
    const input = "SGVsbG8sIHdvcmxkIQ==";
    const expectedOutput = "Hello, world!";

    const result = fromBase64(input);

    expect(result).toBe(expectedOutput);
  });

  it("should handle special characters correctly when encoding and decoding", () => {
    const input = "Olá, mundo! 🚀";
    const encoded = toBase64(input);
    const decoded = fromBase64(encoded);

    expect(decoded).toBe(input);
  });

  it("should handle empty strings correctly", () => {
    const input = "";
    const encoded = toBase64(input);
    const decoded = fromBase64(encoded);

    expect(encoded).toBe("");
    expect(decoded).toBe("");
  });

  it("should throw an error for invalid Base64 strings when decoding", () => {
    const invalidBase64 = "Invalid!!==";

    expect(() => fromBase64(invalidBase64)).toThrowError();
  });
});
