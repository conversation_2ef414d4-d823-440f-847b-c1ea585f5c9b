import { generateCheckSums } from "@/utils/mathUtils";

describe("generateCheckSums", () => {
  it("should generate correct checksums for valid input", () => {
    const numbers = [1, 2, 3, 4];
    const validators = [2, 3, 4, 5];
    const result = generateCheckSums(numbers, validators);
    expect(result).toEqual([26, 40]);
  });

  it("should return [0, 0] when given empty arrays", () => {
    const numbers: number[] = [];
    const validators: number[] = [];
    const result = generateCheckSums(numbers, validators);
    expect(result).toEqual([0, 0]);
  });

  it("should handle cases where the validators array has more elements than the numbers array", () => {
    const numbers = [1, 2];
    const validators = [2, 3, 4];
    const result = generateCheckSums(numbers, validators);
    expect(result).toEqual([11, NaN]);
  });

  it("should handle cases where the numbers array has more elements than the validators array", () => {
    const numbers = [1, 2, 3];
    const validators = [2, 3];
    const result = generateCheckSums(numbers, validators);
    expect(result).toEqual([3, 8]);
  });

  it("should generate correct checksums when all elements are zero", () => {
    const numbers = [0, 0, 0];
    const validators = [0, 0, 0];
    const result = generateCheckSums(numbers, validators);
    expect(result).toEqual([0, 0]);
  });
});
