import { mapToNumbers } from "@/utils/stringUtils";

describe("mapToNumbers", () => {
  it("should convert a string with mixed characters to an array of numbers", () => {
    const input = "ABC123!@#456";
    const result = mapToNumbers(input);
    expect(result).toEqual([1, 2, 3, 4, 5, 6]);
  });

  it("should return an empty array when the input has no numeric characters", () => {
    const input = "ABC!@#";
    const result = mapToNumbers(input);
    expect(result).toEqual([]);
  });

  it("should return an array of numbers when the input contains only numeric characters", () => {
    const input = "1234567890";
    const result = mapToNumbers(input);
    expect(result).toEqual([1, 2, 3, 4, 5, 6, 7, 8, 9, 0]);
  });

  it("should handle an empty string input", () => {
    const input = "";
    const result = mapToNumbers(input);
    expect(result).toEqual([]);
  });

  test.each([
    { input: "111", expected: [1, 1, 1] },
    { input: "222", expected: [2, 2, 2] },
    { input: "333", expected: [3, 3, 3] },
    { input: "444", expected: [4, 4, 4] },
    { input: "555", expected: [5, 5, 5] },
  ])(
    "should map '$input' to an array of repeated numbers equal to $expected",
    ({ input, expected }) => {
      const result = mapToNumbers(input);
      expect(result).toEqual(expected);
    }
  );
});
