import { isCNPJ } from "@/utils/validations";

import {
  invalidCheckDigitsCNPJs,
  invalidFormatCNPJs,
  repeatedSequenceCNPJs,
  validCNPJs,
  validCNPJsWithoutFormatting,
} from "../datasets/cnpjDataset";

describe("isCNPJ", () => {
  it.each(validCNPJs)("should return true for a valid CNPJ: %s", validCNPJ => {
    expect(isCNPJ(validCNPJ)).toBeTruthy();
  });

  it.each(invalidFormatCNPJs)(
    "should return false for an invalid CNPJ format: %s",
    invalidCNPJ => {
      expect(isCNPJ(invalidCNPJ)).toBeFalsy();
    }
  );

  it.each(repeatedSequenceCNPJs)(
    "should return false for a repeated sequence CNPJ: %s",
    repeatedCNPJ => {
      expect(isCNPJ(repeatedCNPJ)).toBeFalsy();
    }
  );

  it.each(invalidCheckDigitsCNPJs)(
    "should return false for an invalid CNPJ with incorrect check digits: %s",
    invalidCheckDigitsCNPJ => {
      expect(isCNPJ(invalidCheckDigitsCNPJ)).toBeFalsy();
    }
  );

  it.each(validCNPJsWithoutFormatting)(
    "should return true for a valid CNPJ without formatting: %s",
    validCNPJ => {
      expect(isCNPJ(validCNPJ)).toBeTruthy();
    }
  );

  it("should return false for an empty string", () => {
    expect(isCNPJ("")).toBeFalsy();
  });
});
