import { SubscriptionRenewalResponse } from "@/types/subscriptionRenewalResponse";
import { convertSubscriptionResponseToSuccessData } from "@/utils/subscriptionUtils/convertSubscriptionResponseToSuccessData";

describe("convertSubscriptionResponseToSuccessData", () => {
  const mockSubscriptionResponse: SubscriptionRenewalResponse = {
    code: "SB1B4H16G",
    price: "R$150,00",
    product: {
      title: "Alterando data de assinatura",
      platform: {
        name: "Olympus XVI",
        url: "https://plataforma-tm-journey.themembers.dev.br",
      },
    },
    periodicity: {
      type: "monthly",
      interval: 1,
    },
    subscriber: {
      name: "<PERSON><PERSON>",
    },
    transaction: {
      id: "7333372405785562503",
      status: "approved",
      amount: 20382,
      payment_method: "credit_card",
      description: "12x de R$16,98",
    },
    credit_card: {
      token: "ac7c846b-2e19-4a43-ab26-d9e814c45cc6",
      brand: "mastercard",
      first_digits: 5179,
      last_digits: 3590,
      installments: 12,
    },
  };

  it("should convert subscription response to success data format", () => {
    const result = convertSubscriptionResponseToSuccessData(
      mockSubscriptionResponse,
      "SB1B4H16G"
    );

    expect(result).toEqual({
      order_id: "SB1B4H16G",
      payment_method: "credit_card",
      customer_email: "Marcela Morais",
      product: {
        id: "SB1B4H16G",
        title: "Alterando data de assinatura",
        description: "12x de R$16,98",
        charge_type: "monthly",
        platform: {
          name: "Olympus XVI",
          url: "https://plataforma-tm-journey.themembers.dev.br",
        },
      },
      bumps: [],
      payment: {
        status: "approved",
        description: "12x de R$16,98",
        brand: "mastercard",
        brand_image_url: null,
        first_digits: 5179,
        last_digits: 3590,
        installments: 12,
      },
    });
  });

  it("should handle missing optional fields with fallbacks", () => {
    const minimalResponse: SubscriptionRenewalResponse = {
      transaction: {
        id: "123",
        status: "approved",
        amount: 10000,
        payment_method: "pix",
        description: "R$100,00",
      },
    };

    const result = convertSubscriptionResponseToSuccessData(
      minimalResponse,
      "SB123"
    );

    expect(result).toEqual({
      order_id: "SB123",
      payment_method: "pix",
      customer_email: "Customer",
      product: {
        id: "SB123",
        title: "Subscription Product",
        description: "R$100,00",
        charge_type: "subscription",
        platform: {
          name: "Platform",
          url: "",
        },
      },
      bumps: [],
      payment: {
        status: "approved",
        description: "R$100,00",
        brand: undefined,
        brand_image_url: null,
        first_digits: undefined,
        last_digits: undefined,
        installments: undefined,
      },
    });
  });
});
