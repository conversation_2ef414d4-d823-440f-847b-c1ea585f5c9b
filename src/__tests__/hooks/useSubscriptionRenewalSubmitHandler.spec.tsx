import "@testing-library/jest-dom";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import { useParams, useRouter } from "next/navigation";
import React from "react";

import { useOrchestratorData } from "@/hooks/useOrchestratorData";
import { listen } from "@/hooks/useSocket";
import { useSubscriptionRenewalSubmitHandler } from "@/hooks/useSubscriptionRenewalSubmitHandler";
import { subscriptionService } from "@/services/subscription";
import { buyerStore } from "@/store/buyerStore";
import { useCardValidationStore } from "@/store/cardValidationStore";
import { PaymentOption } from "@/types/paymentOptions";
import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";
import { buildSubscriptionPayload } from "@/utils/createOrderSubmitHandler/buildSubscriptionPayload";
import { processPayment } from "@/utils/createOrderSubmitHandler/processPayment";
import { makeQuery, toBase64 } from "@/utils/stringUtils/enconding";
import { convertWebSocketSubscriptionToSuccessData } from "@/utils/subscriptionUtils/convertWebSocketSubscriptionToSuccessData";

jest.mock("next/navigation");
jest.mock("@/services/subscription");
jest.mock("@/store/buyerStore");
jest.mock("@/store/cardValidationStore");
jest.mock("@/hooks/useOrchestratorData", () => ({
  useOrchestratorData: jest.fn(() => ({ data: null })),
}));
jest.mock("@/utils/createOrderSubmitHandler/processPayment");
jest.mock("@/utils/createOrderSubmitHandler/buildSubscriptionPayload");
jest.mock("@/hooks/useSocket", () => ({
  listen: jest.fn(),
}));
jest.mock("@/utils/stringUtils/enconding", () => ({
  toBase64: jest.fn(str => btoa(str)),
  makeQuery: jest.fn(),
}));
jest.mock(
  "@/utils/subscriptionUtils/convertWebSocketSubscriptionToSuccessData",
  () => ({
    convertWebSocketSubscriptionToSuccessData: jest.fn(() => ({
      mockData: true,
    })),
  })
);

const mockMutate = jest.fn();
let mockMutationOptions: any = {};
const mockMutation = {
  mutate: mockMutate,
  isLoading: false,
  error: null,
  data: null,
};

jest.mock("@tanstack/react-query", () => ({
  ...jest.requireActual("@tanstack/react-query"),
  useMutation: jest.fn(options => {
    mockMutationOptions = options;
    return {
      ...mockMutation,
      mutate: async (payload: any) => {
        mockMutate(payload);
        try {
          const response = await mockMutationOptions.mutationFn(payload);
          if (mockMutationOptions.onSuccess) {
            mockMutationOptions.onSuccess(response);
          }
        } catch (error) {
          if (mockMutationOptions.onError) {
            mockMutationOptions.onError(error);
          }
        }
      },
    };
  }),
  QueryClient: jest.requireActual("@tanstack/react-query").QueryClient,
  QueryClientProvider: jest.requireActual("@tanstack/react-query")
    .QueryClientProvider,
}));

const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockSubscriptionService = subscriptionService as jest.Mocked<
  typeof subscriptionService
>;
const mockBuyerStore = buyerStore as jest.MockedFunction<typeof buyerStore>;
const mockUseCardValidationStore =
  useCardValidationStore as jest.MockedFunction<typeof useCardValidationStore>;
const mockProcessPayment = processPayment as jest.MockedFunction<
  typeof processPayment
>;
const mockBuildSubscriptionPayload =
  buildSubscriptionPayload as jest.MockedFunction<
    typeof buildSubscriptionPayload
  >;
const mockUseOrchestratorData = useOrchestratorData as jest.MockedFunction<
  typeof useOrchestratorData
>;
const mockListen = listen as jest.MockedFunction<typeof listen>;
const mockToBase64 = toBase64 as jest.MockedFunction<typeof toBase64>;
const mockMakeQuery = makeQuery as jest.MockedFunction<typeof makeQuery>;
const mockConvertWebSocketSubscriptionToSuccessData =
  convertWebSocketSubscriptionToSuccessData as jest.MockedFunction<
    typeof convertWebSocketSubscriptionToSuccessData
  >;

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

const mockBuyerStoreReturn = {
  tokenizationError: false,
  setTokenizationError: jest.fn(),
  setPaymentError: jest.fn(),
  paymentError: false,
  setFailedPaymentInformation: jest.fn(),
  setPixError: jest.fn(),
  pixError: false,
  setFailedPixInformation: jest.fn(),
  setBoletoError: jest.fn(),
  boletoError: false,
  setFailedBoletoInformation: jest.fn(),
};

const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe("useSubscriptionRenewalSubmitHandler", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseParams.mockReturnValue({ locale: "pt-BR" });
    mockUseRouter.mockReturnValue(mockRouter);
    mockBuyerStore.mockReturnValue(mockBuyerStoreReturn);
    mockUseCardValidationStore.mockReturnValue({ isCardValid: true });

    mockListen.mockClear();
    mockProcessPayment.mockClear();
    mockBuildSubscriptionPayload.mockClear();
    mockUseOrchestratorData.mockClear();
    mockToBase64.mockClear();
    mockMakeQuery.mockClear();
    mockConvertWebSocketSubscriptionToSuccessData.mockClear();
    mockSubscriptionService.renewSubscription.mockClear();
    mockMutate.mockClear();
  });

  it("should initialize with correct default values", () => {
    const { result } = renderHook(
      () =>
        useSubscriptionRenewalSubmitHandler(
          PaymentOption.CreditCard,
          "SB1B4H1AR",
          "test-signature"
        ),
      {
        wrapper: Wrapper,
      }
    );

    expect(result.current.isLoading).toBe(false);
    expect(result.current.tokenizationError).toBe(false);
    expect(result.current.paymentError).toBe(false);
    expect(result.current.pixError).toBe(false);
    expect(result.current.boletoError).toBe(false);
  });

  it("should handle successful subscription renewal", async () => {
    const mockResponse = {
      transaction: {
        id: "123",
        status: SubscriptionRenewalStatus.APPROVED,
        amount: 10000,
        payment_method: "credit_card",
        description: "1x de R$ 100,00",
      },
    };

    mockSubscriptionService.renewSubscription.mockResolvedValueOnce(
      mockResponse
    );

    const { result } = renderHook(
      () =>
        useSubscriptionRenewalSubmitHandler(
          PaymentOption.CreditCard,
          "SB1B4H1AR",
          "test-signature"
        ),
      {
        wrapper: Wrapper,
      }
    );

    await waitFor(() => {
      expect(result.current.onSubmit).toBeDefined();
    });
  });

  it("should return early if card is invalid for credit card payment", async () => {
    mockUseCardValidationStore.mockReturnValue({ isCardValid: false });

    const { result } = renderHook(
      () =>
        useSubscriptionRenewalSubmitHandler(
          PaymentOption.CreditCard,
          "SB1B4H1AR",
          "test-signature"
        ),
      {
        wrapper: Wrapper,
      }
    );

    const mockData = {
      cardNumber: "****************",
      cardHolderName: "John Doe",
      cardCvv: "123",
      cardExpirationDateMonth: "12",
      cardExpirationDateYear: "2030",
      instalment: 1,
    };

    await result.current.onSubmit(mockData);

    expect(mockSubscriptionService.renewSubscription).not.toHaveBeenCalled();
  });

  describe("onSubmit function", () => {
    beforeEach(() => {
      jest.clearAllMocks();
      mockProcessPayment.mockResolvedValue("mock-token");
      mockBuildSubscriptionPayload.mockReturnValue({
        subscription_id: "SB1B4H1AR",
        payment: {
          method: PaymentOption.CreditCard,
        },
      });
    });

    it("should handle successful credit card payment submission", async () => {
      mockUseOrchestratorData.mockReturnValue({
        data: {
          client_id: "test",
          public_key: "test",
          code: "test-code",
        },
      } as any);
      mockUseCardValidationStore.mockReturnValue({ isCardValid: true });

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {
        cardNumber: "****************",
        cardHolderName: "John Doe",
        cardCvv: "123",
        cardExpirationDateMonth: "12",
        cardExpirationDateYear: "2030",
        instalment: 1,
      };

      await result.current.onSubmit(mockData);

      expect(mockBuyerStoreReturn.setTokenizationError).toHaveBeenCalledWith(
        false
      );
      expect(mockProcessPayment).toHaveBeenCalledWith(
        mockData,
        PaymentOption.CreditCard,
        { client_id: "test", public_key: "test", code: "test-code" },
        mockBuyerStoreReturn.setTokenizationError
      );
      expect(mockBuildSubscriptionPayload).toHaveBeenCalledWith(
        mockData,
        "SB1B4H1AR",
        PaymentOption.CreditCard,
        "mock-token",
        "test-signature"
      );
    });

    it("should handle PIX payment submission", async () => {
      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Pix,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};

      await result.current.onSubmit(mockData);

      expect(processPayment).not.toHaveBeenCalled();
      expect(buildSubscriptionPayload).toHaveBeenCalledWith(
        mockData,
        "SB1B4H1AR",
        PaymentOption.Pix,
        undefined,
        "test-signature"
      );
    });

    it("should handle Boleto payment submission", async () => {
      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Boleto,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};

      await result.current.onSubmit(mockData);

      expect(processPayment).not.toHaveBeenCalled();
      expect(buildSubscriptionPayload).toHaveBeenCalledWith(
        mockData,
        "SB1B4H1AR",
        PaymentOption.Boleto,
        undefined,
        "test-signature"
      );
    });
  });

  describe("Error handling", () => {
    it("should return early if orchestratorData is missing for credit card", async () => {
      mockUseOrchestratorData.mockReturnValue({ data: null } as any);
      mockUseCardValidationStore.mockReturnValue({ isCardValid: true });

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {
        cardNumber: "****************",
        cardHolderName: "John Doe",
        cardCvv: "123",
        cardExpirationDateMonth: "12",
        cardExpirationDateYear: "2030",
        instalment: 1,
      };

      await result.current.onSubmit(mockData);

      expect(mockProcessPayment).not.toHaveBeenCalled();
      expect(mockBuildSubscriptionPayload).not.toHaveBeenCalled();
    });

    it("should handle processPayment error", async () => {
      mockUseOrchestratorData.mockReturnValue({
        data: {
          client_id: "test",
          public_key: "test",
          code: "test-code",
        },
      } as any);
      mockUseCardValidationStore.mockReturnValue({ isCardValid: true });
      mockProcessPayment.mockRejectedValue(new Error("Tokenization failed"));

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {
        cardNumber: "****************",
        cardHolderName: "John Doe",
        cardCvv: "123",
        cardExpirationDateMonth: "12",
        cardExpirationDateYear: "2030",
        instalment: 1,
      };

      await result.current.onSubmit(mockData);

      expect(mockBuyerStoreReturn.setTokenizationError).toHaveBeenCalledWith(
        true
      );
    });

    it("should handle mutation error", async () => {
      mockSubscriptionService.renewSubscription.mockRejectedValue(
        new Error("API Error")
      );

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Pix,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};

      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockBuyerStoreReturn.setTokenizationError).toHaveBeenCalledWith(
          false
        );
      });
    });
  });

  describe("Mutation success scenarios", () => {
    it("should handle credit card payment with PENDING status", async () => {
      const mockResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.PENDING,
          amount: 10000,
          payment_method: "credit_card",
          description: "1x de R$ 100,00",
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(mockResponse);

      mockUseOrchestratorData.mockReturnValue({
        data: {
          client_id: "test",
          public_key: "test",
          code: "test-code",
        },
      } as any);
      mockUseCardValidationStore.mockReturnValue({ isCardValid: true });
      mockProcessPayment.mockResolvedValue("mock-token");

      mockBuildSubscriptionPayload.mockReturnValue({
        subscription_id: "SB1B4H1AR",
        payment: {
          method: PaymentOption.CreditCard,
        },
      });

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {
        cardNumber: "****************",
        cardHolderName: "John Doe",
        cardCvv: "123",
        cardExpirationDateMonth: "12",
        cardExpirationDateYear: "2030",
        instalment: 1,
      };
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockSubscriptionService.renewSubscription).toHaveBeenCalled();
      });

      expect(mockRouter.push).toHaveBeenCalledWith(
        "/pt-BR/subscriptions/credit-card/SB1B4H1AR&d=undefined"
      );
    });

    it("should handle credit card payment with APPROVED status", async () => {
      const mockResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "credit_card",
          description: "1x de R$ 100,00",
        },
      };

      mockBuildSubscriptionPayload.mockReturnValue({
        subscription_id: "SB1B4H1AR",
        payment: {
          method: PaymentOption.CreditCard,
        },
      });
      mockMakeQuery.mockReturnValue("encodedData");

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockMutate).toHaveBeenCalled();
      });

      if (mockMutationOptions.onSuccess) {
        mockMutationOptions.onSuccess(mockResponse);
      }

      expect(mockMakeQuery).toHaveBeenCalledWith(mockResponse);
      expect(mockRouter.replace).toHaveBeenCalledWith(
        "/pt-BR/success?d=encodedData"
      );
    });

    it("should handle PIX payment response", async () => {
      const mockPixResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "pix",
        },
        pix: {
          qr_code: "mock-qr-code",
          copy_paste: "mock-copy-paste",
          expires: 1800,
        },
        product: {
          title: "Test Product",
          platform: { name: "Test Platform", url: "https://test.com" },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockPixResponse
      );
      mockMakeQuery.mockReturnValue("encodedPixData");

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Pix,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockMakeQuery).toHaveBeenCalled();
        expect(mockRouter.push).toHaveBeenCalledWith(
          "/pt-BR/order/pix?d=encodedPixData&signature=test-signature&subscriptionId=SB1B4H1AR"
        );
      });
    });

    it("should handle Boleto payment response", async () => {
      const mockBoletoResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "boleto",
        },
        boleto: {
          barcode_image_url: "mock-barcode-image",
          barcode_data: "mock-barcode-data",
          expires_date: 1234567890000,
        },
        product: {
          title: "Test Product",
          platform: { name: "Test Platform", url: "https://test.com" },
        },
        subscriber: {
          name: "Test Customer",
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockBoletoResponse
      );
      mockMakeQuery.mockReturnValue("encodedBoletoData");

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Boleto,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockMakeQuery).toHaveBeenCalled();
        expect(mockRouter.push).toHaveBeenCalledWith(
          "/pt-BR/order/boleto?d=encodedBoletoData&signature=test-signature&subscriptionId=SB1B4H1AR"
        );
      });
    });
  });

  describe("Error response handling", () => {
    it("should handle PIX payment error", async () => {
      const mockPixErrorResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.FAILED,
          amount: 10000,
          payment_method: "pix",
          error: {
            title: "PIX Error",
            description: "PIX payment failed",
          },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockPixErrorResponse
      );

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Pix,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockBuyerStoreReturn.setPixError).toHaveBeenCalledWith(true);
        expect(
          mockBuyerStoreReturn.setFailedPixInformation
        ).toHaveBeenCalledWith({
          title: "PIX Error",
          description: "PIX payment failed",
        });
      });
    });

    it("should handle Boleto payment error", async () => {
      const mockBoletoErrorResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.FAILED,
          amount: 10000,
          payment_method: "boleto",
          error: {
            title: "Boleto Error",
            description: "Boleto payment failed",
          },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockBoletoErrorResponse
      );

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Boleto,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockBuyerStoreReturn.setBoletoError).toHaveBeenCalledWith(true);
        expect(
          mockBuyerStoreReturn.setFailedBoletoInformation
        ).toHaveBeenCalledWith({
          title: "Boleto Error",
          description: "Boleto payment failed",
        });
      });
    });

    it("should handle credit card payment error", async () => {
      const mockCreditCardErrorResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.FAILED,
          amount: 10000,
          payment_method: "credit_card",
          error: {
            title: "Credit Card Error",
            description: "Credit card payment failed",
          },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockCreditCardErrorResponse
      );
      mockBuildSubscriptionPayload.mockReturnValue({
        subscription_id: "SB1B4H1AR",
        payment: {
          method: PaymentOption.CreditCard,
        },
      });

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.CreditCard,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockSubscriptionService.renewSubscription).toHaveBeenCalled();
      });

      expect(mockBuyerStoreReturn.setPaymentError).toHaveBeenCalledWith(true);
      expect(
        mockBuyerStoreReturn.setFailedPaymentInformation
      ).toHaveBeenCalledWith({
        title: "Credit Card Error",
        description: "Credit card payment failed",
      });
    });
  });

  describe("Edge cases", () => {
    it("should handle payment without signature", async () => {
      const mockPixResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "pix",
        },
        pix: {
          qr_code: "mock-qr-code",
          copy_paste: "mock-copy-paste",
          expires: 1800,
        },
        product: {
          title: "Test Product",
          platform: { name: "Test Platform", url: "https://test.com" },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockPixResponse
      );
      mockMakeQuery.mockReturnValue("encodedPixData");

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(PaymentOption.Pix, "SB1B4H1AR"),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith(
          "/pt-BR/order/pix?d=encodedPixData"
        );
      });
    });

    it("should handle boleto with missing subscriber name", async () => {
      const mockBoletoResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "boleto",
        },
        boleto: {
          barcode_image_url: "mock-barcode-image",
          barcode_data: "mock-barcode-data",
          expires_date: 1234567890000,
        },
        product: {
          title: "Test Product",
          platform: { name: "Test Platform", url: "https://test.com" },
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(
        mockBoletoResponse
      );
      mockMakeQuery.mockReturnValue("encodedBoletoData");

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            PaymentOption.Boleto,
            "SB1B4H1AR",
            "test-signature"
          ),
        {
          wrapper: Wrapper,
        }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockMakeQuery).toHaveBeenCalled();
        expect(mockRouter.push).toHaveBeenCalled();
      });
    });

    it("should handle unknown payment option in sync handler", async () => {
      const mockResponse = {
        transaction: {
          id: "123",
          status: SubscriptionRenewalStatus.APPROVED,
          amount: 10000,
          payment_method: "unknown_method",
        },
      };

      mockSubscriptionService.renewSubscription.mockResolvedValue(mockResponse);

      const { result } = renderHook(
        () =>
          useSubscriptionRenewalSubmitHandler(
            "unknown_option" as PaymentOption,
            "SB1B4H1AR",
            "test-signature"
          ),
        { wrapper: Wrapper }
      );

      const mockData = {};
      await result.current.onSubmit(mockData);

      await waitFor(() => {
        expect(mockSubscriptionService.renewSubscription).toHaveBeenCalled();
      });
    });
  });
});
