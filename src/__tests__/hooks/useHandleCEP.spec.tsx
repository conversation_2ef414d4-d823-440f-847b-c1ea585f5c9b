import { act, renderHook } from "@testing-library/react";

import { useHandleCEP } from "@/hooks/useHandleCEP";
import { clearString } from "@/utils/stringUtils";
import { formatToCEP } from "@/utils/validations/formatToCEP";

jest.mock("@/utils/stringUtils", () => ({
  clearString: jest.fn(),
  formatToCEP: jest.fn(),
}));

jest.mock("@/utils/validations/formatToCEP", () => ({
  formatToCEP: jest.fn(),
}));

describe("useHandleCEP", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should format and set the CEP value correctly", () => {
    const setValue = jest.fn();
    const trigger = jest.fn();
    const { result } = renderHook(() => useHandleCEP(setValue, trigger));

    const event = {
      target: { value: "12345678" },
    } as React.ChangeEvent<HTMLInputElement>;

    (clearString as jest.Mock).mockReturnValue("12345678");
    (formatToCEP as jest.Mock).mockReturnValue("12345-678");

    act(() => {
      result.current.handleCEPChange(event);
    });

    expect(clearString).toHaveBeenCalledWith("12345678");
    expect(formatToCEP).toHaveBeenCalledWith("12345678");
  });

  it("should not set the CEP value if input is empty", () => {
    const setValue = jest.fn();
    const trigger = jest.fn();
    const { result } = renderHook(() => useHandleCEP(setValue, trigger));

    const event = {
      target: { value: "" },
    } as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleCEPChange(event);
    });

    expect(setValue).not.toHaveBeenCalled();
    expect(trigger).not.toHaveBeenCalled();
  });
});
