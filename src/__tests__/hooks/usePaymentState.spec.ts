import { act, renderHook } from "@testing-library/react";

import { usePaymentState } from "@/hooks/usePaymentState";
import { PaymentOption } from "@/types/paymentOptions";

describe("usePaymentState", () => {
  it("should initialize selectedOption to PaymentOption.CreditCard", () => {
    const { result } = renderHook(() => usePaymentState());
    expect(result.current.selectedOption).toBe(PaymentOption.CreditCard);
  });

  it("should initialize selectedInstalmentDescription to an empty string", () => {
    const { result } = renderHook(() => usePaymentState());
    expect(result.current.selectedInstalmentDescription).toBe("");
  });

  it("should update selectedOption when setSelectedOption is called", () => {
    const { result } = renderHook(() => usePaymentState());

    act(() => {
      result.current.setSelectedOption(PaymentOption.Pix);
    });

    expect(result.current.selectedOption).toBe(PaymentOption.Pix);
  });

  it("should update selectedInstalmentDescription when setSelectedInstalmentDescription is called", () => {
    const { result } = renderHook(() => usePaymentState());

    act(() => {
      result.current.setSelectedInstalmentDescription("Installment Plan A");
    });

    expect(result.current.selectedInstalmentDescription).toBe(
      "Installment Plan A"
    );
  });

  it("getCardStyles should return active styles when selectedOption matches the value", () => {
    const { result } = renderHook(() => usePaymentState());

    act(() => {
      result.current.setSelectedOption(PaymentOption.Boleto);
    });

    const styles = result.current.getCardStyles(PaymentOption.Boleto);
    expect(styles).toBe("bg-blue-50 border-2 border-blue-700");
  });

  it("getCardStyles should return default border when selectedOption does not match the value", () => {
    const { result } = renderHook(() => usePaymentState());

    const styles = result.current.getCardStyles(PaymentOption.Pix);
    expect(styles).toBe("border");
  });
});
