import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

import { useCreditCardList } from "@/hooks/useCreditCardList";
import { theBankService } from "@/services";

jest.mock("@/services", () => ({
  theBankService: {
    fetchCreditCardList: jest.fn(),
  },
}));

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useCreditCardList", () => {
  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });

  it("should fetch credit card list successfully", async () => {
    const mockData = [
      { id: 1, cardNumber: "1234 5678 9012 3456", cardType: "Visa" },
    ];
    (theBankService.fetchCreditCardList as jest.Mock).mockResolvedValueOnce(
      mockData
    );

    const { result } = renderHook(() => useCreditCardList(), {
      wrapper: Wrapper,
    });

    await waitFor(() => result.current.isSuccess);

    expect(result.current.data).toEqual(mockData);
    expect(theBankService.fetchCreditCardList).toHaveBeenCalledTimes(1);
  });
});
