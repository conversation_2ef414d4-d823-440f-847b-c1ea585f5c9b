import { renderHook } from "@testing-library/react";

import { useCreditCardList } from "@/hooks/useCreditCardList";
import { usePriorityCards } from "@/hooks/usePriorityCards";

jest.mock("@/hooks/useCreditCardList", () => ({
  useCreditCardList: jest.fn(),
}));

describe("usePriorityCards", () => {
  it("should return empty arrays and zero count when no credit cards are present", () => {
    (useCreditCardList as jest.Mock).mockReturnValue({ data: null });

    const { result } = renderHook(() => usePriorityCards());

    expect(result.current.sortedCreditCards).toEqual([]);
    expect(result.current.priorityZeroCards).toEqual([]);
    expect(result.current.priorityZeroCount).toBe(0);
    expect(result.current.restCreditCards).toEqual([]);
  });

  it("should return cards with priority zero and count", () => {
    const mockData = [
      {
        id: 1,
        priority: 0,
        brand: "Visa",
        cardNumber: "1234 5678 9012 3456",
        icon_url: "/brands/Visa.svg",
      },
      {
        id: 2,
        priority: 0,
        brand: "Mastercard",
        cardNumber: "2345 6789 0123 4567",
        icon_url: "/brands/MasterCard.svg",
      },
      {
        id: 3,
        priority: 3,
        brand: "Amex",
        cardNumber: "3456 7890 1234 5678",
        icon_url: "/brands/American Express (Amex).svg",
      },
    ];
    (useCreditCardList as jest.Mock).mockReturnValue({ data: mockData });

    const { result } = renderHook(() => usePriorityCards());

    expect(result.current.priorityZeroCards).toEqual([
      {
        id: 1,
        priority: 0,
        brand: "Visa",
        cardNumber: "1234 5678 9012 3456",
        icon_url: "/brands/Visa.svg",
      },
      {
        id: 2,
        priority: 0,
        brand: "Mastercard",
        cardNumber: "2345 6789 0123 4567",
        icon_url: "/brands/MasterCard.svg",
      },
    ]);
    expect(result.current.priorityZeroCount).toBe(2);
    expect(result.current.restCreditCards).toEqual([]);
  });

  it("should handle all cards with priority zero", () => {
    const mockData = [
      {
        id: 1,
        priority: 0,
        brand: "Visa",
        cardNumber: "1234 5678 9012 3456",
        icon_url: "/brands/Visa.svg",
      },
      {
        id: 2,
        priority: 0,
        brand: "Mastercard",
        cardNumber: "2345 6789 0123 4567",
        icon_url: "/brands/MasterCard.svg",
      },
    ];
    (useCreditCardList as jest.Mock).mockReturnValue({ data: mockData });

    const { result } = renderHook(() => usePriorityCards());

    expect(result.current.sortedCreditCards).toEqual(mockData);
    expect(result.current.priorityZeroCount).toBe(2);
  });
});
