import { act, renderHook } from "@testing-library/react";

import { useCepSearch } from "@/hooks/useCepSearch";
import { viaCEP } from "@/services/viaCEP";

jest.mock("@/services/viaCEP", () => ({
  viaCEP: jest.fn(),
}));

describe("useCepSearch", () => {
  let setValue: jest.Mock;
  let trigger: jest.Mock;
  let setShowFields: jest.Mock;

  beforeEach(() => {
    setValue = jest.fn();
    trigger = jest.fn();
    setShowFields = jest.fn();
    jest.clearAllMocks();
  });

  it("should set address fields and show fields when the address is found", async () => {
    const mockData = {
      logradouro: "Rua Exemplo",
      bairro: "Bairro Exemplo",
      localidade: "Cidade Exemplo",
      estado: "Estado Exemplo",
    };

    (viaCEP as jest.Mock).mockImplementation((cep, { onAddressFound }) => {
      onAddressFound(mockData);
    });

    const { result } = renderHook(() =>
      useCepSearch({ setValue, trigger, setShowFields, states: [] })
    );

    await act(async () => {
      result.current.searchCep("12345678");
    });

    expect(setValue).toHaveBeenCalledWith(
      "billing_address.street",
      mockData.logradouro
    );
    expect(setValue).toHaveBeenCalledWith(
      "billing_address.district",
      mockData.bairro
    );
    expect(setValue).toHaveBeenCalledWith(
      "billing_address.city",
      mockData.localidade
    );
    expect(setValue).toHaveBeenCalledWith(
      "billing_address.state",
      mockData.estado
    );
    expect(trigger).toHaveBeenCalledWith("billing_address.state");
    expect(trigger).toHaveBeenCalledWith([
      "billing_address.zipcode",
      "billing_address.street",
      "billing_address.district",
      "billing_address.city",
    ]);
    expect(setShowFields).toHaveBeenCalledWith(true);
    expect(result.current.isSearching).toBe(false);
  });

  it("deve exibir erro e definir os campos quando ocorrer um erro na busca", async () => {
    const consoleErrorSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    (viaCEP as jest.Mock).mockImplementation((cep, { onError }) => {
      onError();
    });

    const { result } = renderHook(() =>
      useCepSearch({ setValue, trigger, setShowFields, states: [] })
    );

    await act(async () => {
      result.current.searchCep("12345678");
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Erro ao comunicar com via cep"
    );
    expect(trigger).toHaveBeenCalledWith([
      "billing_address.zipcode",
      "billing_address.street",
      "billing_address.district",
      "billing_address.city",
      "billing_address.state",
    ]);
    expect(setShowFields).toHaveBeenCalledWith(true);
    expect(result.current.isSearching).toBe(false);
    consoleErrorSpy.mockRestore();
  });

  it('deve exibir "Cep não encontrado" e definir os campos quando o CEP não for localizado', async () => {
    const consoleErrorSpy = jest
      .spyOn(console, "error")
      .mockImplementation(() => {});

    (viaCEP as jest.Mock).mockImplementation((cep, { onAddressNotFound }) => {
      onAddressNotFound();
    });

    const { result } = renderHook(() =>
      useCepSearch({ setValue, trigger, setShowFields, states: [] })
    );

    await act(async () => {
      result.current.searchCep("12345678");
    });

    expect(consoleErrorSpy).toHaveBeenCalledWith("Cep não encontrado");
    expect(trigger).toHaveBeenCalledWith([
      "billing_address.zipcode",
      "billing_address.street",
      "billing_address.district",
      "billing_address.city",
      "billing_address.state",
    ]);
    expect(setShowFields).toHaveBeenCalledWith(true);
    expect(result.current.isSearching).toBe(false);
    consoleErrorSpy.mockRestore();
  });
});
