import { useAddressStore } from "@/hooks/useAddressStore";

describe("useAddressStore", () => {
  beforeEach(() => {
    useAddressStore.setState({
      showFields: false,
      hasNumber: true,
      streetNumber: "",
      streetNumberError: false,
    });
  });

  it("should initialize with default values", () => {
    const state = useAddressStore.getState();
    expect(state.showFields).toBe(false);
    expect(state.hasNumber).toBe(true);
    expect(state.streetNumber).toBe("");
    expect(state.streetNumberError).toBe(false);
  });

  it("should update showFields when setShowFields is called", () => {
    const state = useAddressStore.getState();
    state.setShowFields(true);
    expect(useAddressStore.getState().showFields).toBe(true);
  });

  it("should update hasNumber when setHasNumber is called", () => {
    const state = useAddressStore.getState();
    state.setHasNumber(false);
    expect(useAddressStore.getState().hasNumber).toBe(false);
  });

  it("should update streetNumber when setStreetNumber is called", () => {
    const state = useAddressStore.getState();
    state.setStreetNumber("123");
    expect(useAddressStore.getState().streetNumber).toBe("123");
  });

  it("should update streetNumberError when setStreetNumberError is called", () => {
    const state = useAddressStore.getState();
    state.setStreetNumberError(true);
    expect(useAddressStore.getState().streetNumberError).toBe(true);
  });
});
