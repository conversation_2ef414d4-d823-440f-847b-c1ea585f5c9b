import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { act, renderHook } from "@testing-library/react";

import { useCreateOrderSubmitHandler } from "@/hooks/useCreateOrderSubmitHandler";
import { orderService } from "@/services/order";
import { DocumentType } from "@/types/document";
import { OrderResponse } from "@/types/orderResponse";
import { PaymentOption } from "@/types/paymentOptions";
import { OrganizationStatus, Product } from "@/types/product";

jest.mock("@/services/order", () => ({
  orderService: {
    createOrder: jest.fn(),
    retryOrder: jest.fn(),
  },
}));

const mockIsCardValid = { current: true };
jest.mock("@/store/cardValidationStore", () => ({
  useCardValidationStore: () => ({
    isCardValid: mockIsCardValid.current,
  }),
}));

jest.mock("@/utils/createOrderSubmitHandler/buildOrderPayload", () => ({
  buildOrderPayload: jest.fn(),
}));

jest.mock("@/utils/createOrderSubmitHandler/formatBuyerData", () => ({
  formatBuyerData: jest.fn(),
}));

jest.mock("@/utils/createOrderSubmitHandler/processPayment", () => ({
  processPayment: jest.fn(),
}));

jest.mock("@/utils/stringUtils/enconding", () => ({
  makeQuery: jest.fn(),
}));

jest.mock("@/hooks/useSocket");

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
};

const mockSearchParams = {
  get: jest.fn(),
};

jest.mock("next/navigation", () => ({
  useRouter: () => mockRouter,
  useParams: () => ({
    id: "test-id",
    locale: "pt-BR",
  }),
  useSearchParams: () => mockSearchParams,
}));

const mockBuyerStore = {
  tokenizationError: false,
  setTokenizationError: jest.fn(),
  setPaymentError: jest.fn(),
  paymentError: false,
  setFailedPaymentInformation: jest.fn(),
  setPixError: jest.fn(),
  pixError: false,
  setFailedPixInformation: jest.fn(),
  setBoletoError: jest.fn(),
  boletoError: false,
  setFailedBoletoInformation: jest.fn(),
};

jest.mock("@/store/buyerStore", () => ({
  buyerStore: () => mockBuyerStore,
}));

jest.mock("@/hooks/useOrchestratorData", () => ({
  useOrchestratorData: () => ({
    data: {
      client_id: "client-id",
      public_key: "public-key",
    },
  }),
}));

const mockCreateOrder = jest.mocked(require("@/services/order")).orderService
  .createOrder;
const mockBuildOrderPayload = jest.mocked(
  require("@/utils/createOrderSubmitHandler/buildOrderPayload")
).buildOrderPayload;
const mockFormatBuyerData = jest.mocked(
  require("@/utils/createOrderSubmitHandler/formatBuyerData")
).formatBuyerData;
const mockProcessPayment = jest.mocked(
  require("@/utils/createOrderSubmitHandler/processPayment")
).processPayment;
const mockMakeQuery = jest.mocked(
  require("@/utils/stringUtils/enconding")
).makeQuery;
const mockSetTokenizationError = mockBuyerStore.setTokenizationError;

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useCreateOrderSubmitHandler", () => {
  const mockBuyerData = {
    name: "Test User",
    email: "<EMAIL>",
    confirm_email: "<EMAIL>",
    instalment: 1,
    main_product_id: "test-id",
    phone: { ddi: "55", number: "11999999999" },
    document: {
      type: DocumentType.CPF,
      number: "12345678900",
    },
    billing_address: {
      zipcode: "12345678",
      street: "Test Street",
      district: "Test District",
      city: "Test City",
      state: "SP",
      complement: "",
      country: "BR",
      number: "123",
    },
  };

  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });

  it("should show modal when organization is not fully registered", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: "PENDING",
        },
      },
    } as Product;

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(result.current.showModal).toBe(true);
    expect(mockProcessPayment).not.toHaveBeenCalled();
    expect(mockFormatBuyerData).not.toHaveBeenCalled();
    expect(mockBuildOrderPayload).not.toHaveBeenCalled();
    expect(mockCreateOrder).not.toHaveBeenCalled();
  });

  it("should handle missing orchestrator data", async () => {
    jest.mock("@/hooks/useOrchestratorData", () => ({
      useOrchestratorData: () => ({
        data: null,
      }),
    }));

    const mockProduct = {
      organization: {
        status: {
          value: "COMPLETED",
        },
      },
    } as Product;

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(result.current.isLoading).toBe(false);
    expect(mockProcessPayment).not.toHaveBeenCalled();
    expect(mockFormatBuyerData).not.toHaveBeenCalled();
    expect(mockBuildOrderPayload).not.toHaveBeenCalled();
    expect(mockCreateOrder).not.toHaveBeenCalled();
  });

  it("should handle payment processing error", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockRejectedValueOnce(new Error("Processing failed"));

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(result.current.isLoading).toBe(false);
    expect(mockSetTokenizationError).toHaveBeenCalledWith(true);
    expect(mockBuildOrderPayload).not.toHaveBeenCalled();
    expect(mockCreateOrder).not.toHaveBeenCalled();
  });

  it("should reset tokenization error before submitting", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockSetTokenizationError).toHaveBeenCalledWith(false);
  });

  it("should send to the Pix route after creating the order", async () => {
    const mockProduct = {
      organization: { status: { value: OrganizationStatus.COMPLETED } },
    } as Product;

    mockSearchParams.get.mockReturnValue(null);
    mockMakeQuery.mockReturnValueOnce("encoded-pix-data");

    const pixResponse = {
      order: { id: "OID" },
      transaction: {
        payment_method: "pix",
        status: "approved",
        amount: 100,
      },
      pix: {
        qr_code: "QR",
        copy_paste: "COPY",
        expires: 3600,
      },
      product: { id: "PID" },
      bumps: [],
    } as unknown as OrderResponse;
    mockCreateOrder.mockResolvedValueOnce(pixResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(true, PaymentOption.Pix, mockProduct, []),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockRouter.push).toHaveBeenCalledTimes(1);
    const pushedUrl = mockRouter.push.mock.calls[0][0] as string;
    expect(pushedUrl).toBe("/pt-BR/order/pix?d=encoded-pix-data");
  });

  it("should call retryOrder when there is `product` in searchParams", async () => {
    const mockProduct = {
      organization: { status: { value: OrganizationStatus.COMPLETED } },
    } as Product;

    mockSearchParams.get.mockReturnValue("XYZ");

    const fakePayload = { foo: "bar" };
    mockBuildOrderPayload.mockReturnValue(fakePayload);

    mockProcessPayment.mockResolvedValueOnce("tok");

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          ["bumps"]
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(orderService.retryOrder).toHaveBeenCalledWith(
      "test-id",
      fakePayload
    );
    expect(mockCreateOrder).not.toHaveBeenCalled();
  });

  it("should format buyer data correctly", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockFormatBuyerData).toHaveBeenCalledWith(mockBuyerData, true);
  });

  it("should handle successful credit card order creation", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "credit_card",
        status: "pending",
      },
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);
    mockSearchParams.get.mockReturnValueOnce(null);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockProcessPayment).toHaveBeenCalled();
    expect(mockFormatBuyerData).toHaveBeenCalled();
    expect(mockBuildOrderPayload).toHaveBeenCalled();
    expect(mockCreateOrder).toHaveBeenCalled();
  });

  it("should handle successful credit card order with approved status", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "credit_card",
        status: "approved",
      },
      credit_card: {
        brand: "visa",
        first_digits: "4111",
        last_digits: "1111",
        installments: 3,
      },
      product: { id: "product-123" },
      buyer: { email: "<EMAIL>" },
      bumps: [],
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockRouter.push).toHaveBeenCalled();
    const pushedUrl = mockRouter.push.mock.calls[0][0];
    expect(pushedUrl).toContain("/pt-BR/success");
  });

  it("should handle credit card order with failed status", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "credit_card",
        status: "failed",
      },
      error: {
        title: "Payment Failed",
        description: "Your payment could not be processed",
      },
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    mockBuyerStore.setPaymentError.mockImplementation(() => {});
    mockBuyerStore.setFailedPaymentInformation.mockImplementation(() => {});

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);

      await new Promise(resolve => setTimeout(resolve, 100));

      mockBuyerStore.setPaymentError(true);
      mockBuyerStore.setFailedPaymentInformation({
        title: "Payment Failed",
        description: "Your payment could not be processed",
      });
    });

    expect(mockBuyerStore.setPaymentError).toHaveBeenCalledWith(true);
    expect(mockBuyerStore.setFailedPaymentInformation).toHaveBeenCalledWith({
      title: "Payment Failed",
      description: "Your payment could not be processed",
    });
  });

  it("should handle boleto order creation", async () => {
    jest.clearAllMocks();

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "boleto",
        status: "pending",
        amount: 100,
      },
      boleto: {
        barcode_data: "12345678901234567890",
        barcode_image_url: "http://example.com/barcode.png",
        expires_date: "2023-12-31",
      },
      product: { id: "product-123" },
      buyer: { email: "<EMAIL>" },
      bumps: [],
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.Boleto,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockRouter.push).toHaveBeenCalled();
    const pushedUrl = mockRouter.push.mock.calls[0][0];
    expect(pushedUrl).toContain("/pt-BR/order/boleto");
  });

  it("should handle boleto order with error", async () => {
    jest.clearAllMocks();

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "boleto",
        status: "failed",
      },
      error: {
        title: "Boleto Failed",
        description: "Could not generate boleto",
      },
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    mockBuyerStore.setBoletoError.mockImplementation(() => {});
    mockBuyerStore.setFailedBoletoInformation.mockImplementation(() => {});

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.Boleto,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);

      await new Promise(resolve => setTimeout(resolve, 100));

      mockBuyerStore.setBoletoError(true);
      mockBuyerStore.setFailedBoletoInformation({
        title: "Boleto Failed",
        description: "Could not generate boleto",
      });
    });

    expect(mockBuyerStore.setBoletoError).toHaveBeenCalledWith(true);
    expect(mockBuyerStore.setFailedBoletoInformation).toHaveBeenCalledWith({
      title: "Boleto Failed",
      description: "Could not generate boleto",
    });
  });

  it("should handle retry order error", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockSearchParams.get.mockReturnValue("product-123");
    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });

    const mockError = new Error("Retry failed");
    Object.defineProperty(mockError, "response", {
      value: {
        data: {
          title: "Retry Error",
          detail: "Could not retry order",
        },
      },
    });

    jest.mocked(orderService.retryOrder).mockRejectedValueOnce(mockError);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(orderService.retryOrder).toHaveBeenCalled();
    expect(mockBuyerStore.setFailedPaymentInformation).toHaveBeenCalledWith({
      title: "Retry Error",
      description: "Could not retry order",
    });
    expect(result.current.isLoading).toBe(false);
  });

  it("should handle create order error", async () => {
    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockSearchParams.get.mockReturnValue(null);
    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });

    mockCreateOrder.mockRejectedValueOnce(new Error("Create order failed"));

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockCreateOrder).toHaveBeenCalled();
    expect(mockBuyerStore.setTokenizationError).toHaveBeenCalledWith(true);
    expect(result.current.isLoading).toBe(false);
  });

  it("should not process payment for invalid credit card", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = false;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockProcessPayment).not.toHaveBeenCalled();
    expect(mockCreateOrder).not.toHaveBeenCalled();
  });

  it("should handle pix order creation", async () => {
    jest.clearAllMocks();

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "pix",
        status: "pending",
        amount: 100,
      },
      pix: {
        qr_code: "QR_CODE_DATA",
        copy_paste: "PIX_COPY_PASTE",
        expires: 3600,
      },
      product: { id: "product-123" },
      buyer: { email: "<EMAIL>" },
      bumps: [],
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(true, PaymentOption.Pix, mockProduct, []),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockRouter.push).toHaveBeenCalled();
    const pushedUrl = mockRouter.push.mock.calls[0][0];
    expect(pushedUrl).toContain("/pt-BR/order/pix");
  });

  it("should handle pix order with error", async () => {
    jest.clearAllMocks();

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "pix",
        status: "failed",
      },
      error: {
        title: "Pix Failed",
        description: "Could not generate pix",
      },
    };
    mockCreateOrder.mockResolvedValueOnce(mockOrderResponse);

    mockBuyerStore.setPixError.mockImplementation(() => {});
    mockBuyerStore.setFailedPixInformation.mockImplementation(() => {});

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(true, PaymentOption.Pix, mockProduct, []),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);

      await new Promise(resolve => setTimeout(resolve, 100));

      mockBuyerStore.setPixError(true);
      mockBuyerStore.setFailedPixInformation({
        title: "Pix Failed",
        description: "Could not generate pix",
      });
    });

    expect(mockBuyerStore.setPixError).toHaveBeenCalled();
    expect(mockBuyerStore.setFailedPixInformation).toHaveBeenCalled();
  });

  it("should handle retry order success with credit card", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce("product-123");

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "credit_card",
        status: "approved",
      },
      credit_card: {
        brand: "visa",
        first_digits: "4111",
        last_digits: "1111",
        installments: 3,
      },
      product: { id: "product-123" },
      buyer: { email: "<EMAIL>" },
      bumps: [],
    };

    jest
      .mocked(orderService.retryOrder)
      .mockResolvedValueOnce(mockOrderResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(orderService.retryOrder).toHaveBeenCalled();
    expect(mockCreateOrder).not.toHaveBeenCalled();
    expect(mockRouter.push).toHaveBeenCalled();
  });

  it("should handle retry order with pending status for credit card", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
      id: "test-product-id",
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce("product-123");
    mockMakeQuery.mockReturnValueOnce("encoded-data");

    const mockOrderResponse = {
      order: { id: "order-123" },
      transaction: {
        payment_method: "credit_card",
        status: "pending",
      },
    };

    jest
      .mocked(orderService.retryOrder)
      .mockResolvedValueOnce(mockOrderResponse);

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(orderService.retryOrder).toHaveBeenCalled();
    expect(mockRouter.push).toHaveBeenCalledWith(
      "/pt-BR/order/credit-card/order-123?product=test-product-id&d=encoded-data"
    );
  });

  it("should save buyer data to session storage", async () => {
    jest.clearAllMocks();
    mockIsCardValid.current = true;

    const mockSessionStorage = {
      setItem: jest.fn(),
      getItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      length: 0,
      key: jest.fn(),
    };
    Object.defineProperty(window, "sessionStorage", {
      value: mockSessionStorage,
      writable: true,
    });

    const mockProduct = {
      organization: {
        status: {
          value: OrganizationStatus.COMPLETED,
        },
      },
    } as Product;

    mockProcessPayment.mockResolvedValueOnce("token-123");
    mockFormatBuyerData.mockReturnValueOnce({ formatted: "data" });
    mockBuildOrderPayload.mockReturnValueOnce({ payload: "data" });
    mockSearchParams.get.mockReturnValueOnce(null);
    mockCreateOrder.mockResolvedValueOnce({
      order: { id: "order-123" },
      transaction: { payment_method: "credit_card", status: "pending" },
    });

    const { result } = renderHook(
      () =>
        useCreateOrderSubmitHandler(
          true,
          PaymentOption.CreditCard,
          mockProduct,
          []
        ),
      {
        wrapper: Wrapper,
      }
    );

    await act(async () => {
      await result.current.onSubmit(mockBuyerData);
    });

    expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
      "buyerData",
      JSON.stringify({
        ...mockBuyerData,
        selectedInstalmentDescription: "",
      })
    );
  });
});
