import { act, renderHook } from "@testing-library/react";
import axios from "axios";

import { useSearchCep } from "@/hooks/useSearchCep";
import { ViaCepResponse } from "@/types/viaCepResponse";

jest.mock("axios");

describe("useSearchCep", () => {
  const onAddressFound = jest.fn();
  const onError = jest.fn();
  const onAddressNotFound = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call onAddressFound when address is found", async () => {
    const mockData: ViaCepResponse = {
      cep: "12345678",
      logradouro: "Rua Exemplo",
      complemento: "",
      bairro: "Bairro Exemplo",
      localidade: "Cidade Exemplo",
      uf: "Estado Exemplo",
      estado: "Estado Exemplo",
      erro: "",
    };

    (axios.get as jest.Mock).mockResolvedValue({ data: mockData });

    const { result } = renderHook(() =>
      useSearchCep({ onAddressFound, onError, onAddressNotFound })
    );

    await act(async () => {
      await result.current.execute({ cep: "12345678" });
    });

    expect(onAddressFound).toHaveBeenCalledWith(mockData);
    expect(onAddressNotFound).not.toHaveBeenCalled();
    expect(onError).not.toHaveBeenCalled();
  });

  it("should call onAddressNotFound when address is not found", async () => {
    const mockData = { erro: true };

    (axios.get as jest.Mock).mockResolvedValue({ data: mockData });

    const { result } = renderHook(() =>
      useSearchCep({ onAddressFound, onError, onAddressNotFound })
    );

    await act(async () => {
      await result.current.execute({ cep: "12345678" });
    });

    expect(onAddressNotFound).toHaveBeenCalled();
    expect(onAddressFound).not.toHaveBeenCalled();
    expect(onError).not.toHaveBeenCalled();
  });

  it("should call onError when there is an error", async () => {
    (axios.get as jest.Mock).mockRejectedValue(new Error("Network Error"));

    const { result } = renderHook(() =>
      useSearchCep({ onAddressFound, onError, onAddressNotFound })
    );

    await act(async () => {
      await result.current.execute({ cep: "12345678" });
    });

    expect(onError).toHaveBeenCalled();
    expect(onAddressFound).not.toHaveBeenCalled();
    expect(onAddressNotFound).not.toHaveBeenCalled();
  });
});
