import { renderHook } from "@testing-library/react";

import { useSocket } from "@/hooks/useSocket";
import { createSocketConnection } from "@/ws/socket";

jest.mock("@/ws/socket", () => ({
  createSocketConnection: jest.fn(),
}));

describe("useSocket Hook", () => {
  const mockListen = jest.fn();
  const mockLeaveChannel = jest.fn();

  const mockChannel = jest.fn(() => ({
    listen: mockListen,
  }));

  beforeEach(() => {
    jest.clearAllMocks();

    Object.defineProperty(global.window, "Echo", {
      value: {
        channel: mockChannel,
        leaveChannel: mockLeaveChannel,
      },
      writable: true,
      configurable: true,
    });
  });

  afterEach(() => {
    if (global.window.Echo) {
      global.window.Echo.leaveChannel = mockLeaveChannel;
    }
  });

  it("should call createSocketConnection and set up listeners", () => {
    const mockCallBack = jest.fn();
    const options: any = {
      channel: "testChannel",
      event: "testEvent",
      callBack: mockCallBack,
      type: "public",
    };

    renderHook(() => useSocket(options));

    expect(createSocketConnection).toHaveBeenCalledTimes(1);

    expect(mockChannel).toHaveBeenCalledWith("testChannel");

    expect(mockListen).toHaveBeenCalledWith(".testEvent", mockCallBack);
  });

  it("should clean up listeners on unmount", () => {
    const mockCallBack = jest.fn();
    const options: any = {
      channel: "testChannel",
      event: "testEvent",
      callBack: mockCallBack,
      type: "public",
    };

    const { unmount } = renderHook(() => useSocket(options));

    expect(global.window.Echo).toBeDefined();
    expect(global.window.Echo.leaveChannel).toBeDefined();

    unmount();

    expect(mockLeaveChannel).toHaveBeenCalledWith("public:testChannel");
  });
});
