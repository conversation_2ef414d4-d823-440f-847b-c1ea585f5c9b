import { renderHook } from "@testing-library/react";

import { useTotalPriceCalculator } from "@/hooks/useTotalPriceCalculator";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";

describe("useTotalPriceCalculator", () => {
  it("should calculate the total price for credit card payment with a valid installment and bump", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "3";
    const productInstallments: any = {
      installments: [
        { installments: 3, total: 100 },
        { installments: 2, total: 80 },
      ],
      bumps: [
        {
          bump_id: "bump1",
          installments: [{ installments: 3, total: 20 }],
        },
      ],
    };
    const product = {
      price: 500,
      bumps: [{ id: "bump1", price: 30 }],
    };
    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(120);
  });

  it("should calculate the total price for credit card payment when the desired installment is not found", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "4";
    const productInstallments: any = {
      installments: [
        { installments: 3, total: 100, description: "3x of 100.00" },
      ],
      bumps: [
        {
          bump_id: "bump1",
          installments: [{ installments: 3, total: 20 }],
        },
      ],
    };

    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: {
          price: 500,
          bumps: [
            { id: "bump1", price: 30 },
            { id: "bump2", price: 40 },
          ],
        } as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(0);
  });

  it("should calculate the total price for other payment methods by adding the product price with the selected bumps", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "";

    const selectedBumps = ["bump1", "bump2"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments: null as any,
        product: {
          price: 500,
          bumps: [
            { id: "bump1", price: 30 },
            { id: "bump2", price: 40 },
          ],
        } as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(570);
  });

  it("should calculate the total price for other payment methods without finding the selected bumps", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "";

    const selectedBumps = ["bump2"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments: null as any,
        product: {
          price: 500,
          bumps: [{ id: "bump1", price: 30 }],
        } as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(500);
  });

  it("should handle case when selectedProductInstallment is not found (line 26)", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "5";
    const productInstallments: any = {
      installments: [
        { installments: 3, total: 100 },
        { installments: 2, total: 80 },
      ],
      bumps: [],
    };
    const product = {
      price: 500,
      bumps: [],
    };
    const selectedBumps: string[] = [];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as unknown as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(0);
  });

  it("should handle case when productInstallments.bumps is undefined (lines 28-29)", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "3";
    const productInstallments: any = {
      installments: [{ installments: 3, total: 100 }],
    };
    const product = {
      price: 500,
      bumps: [],
    };
    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as unknown as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(100);
  });

  it("should handle case when bumpWithInstallments is not found (lines 31-37)", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "3";
    const productInstallments: any = {
      installments: [{ installments: 3, total: 100 }],
      bumps: [
        {
          bump_id: "bump2",
          installments: [{ installments: 3, total: 20 }],
        },
      ],
    };
    const product = {
      price: 500,
      bumps: [{ id: "bump1", price: 30 }],
    };
    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(100);
  });

  it("should handle case when selectedBumpInstallment is not found (lines 31-37)", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "3";
    const productInstallments: any = {
      installments: [{ installments: 3, total: 100 }],
      bumps: [
        {
          bump_id: "bump1",
          installments: [{ installments: 2, total: 20 }],
        },
      ],
    };
    const product = {
      price: 500,
      bumps: [{ id: "bump1", price: 30 }],
    };
    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(100);
  });

  it("should handle case when selectedBump is not found in product.bumps (lines 51-53)", () => {
    const selectedOption = PaymentOption.Boleto;
    const selectedInstallment = "";
    const productInstallments = null as any;
    const product = {
      price: 500,
      bumps: [{ id: "bump2", price: 30 }],
    };
    const selectedBumps = ["bump1"];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as Product,
        selectedBumps,
      })
    );

    expect(result.current).toBe(500);
  });

  it("should return 0 when selectedProductInstallment is falsy (coverage for line 35)", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "10";
    const productInstallments: any = {
      installments: [
        { installments: 1, total: 100 },
        { installments: 2, total: 200 },
        { installments: 3, total: 300 },
      ],
      bumps: [],
    };
    const product = {
      price: 500,
      bumps: [],
    };
    const selectedBumps: string[] = [];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as any,
        selectedBumps,
      })
    );

    expect(result.current).toBe(0);
  });

  it("should explicitly test line 35 fallback to 0 when no installment matches", () => {
    const selectedOption = PaymentOption.CreditCard;
    const selectedInstallment = "999";
    const productInstallments: any = {
      installments: [
        { installments: 1, total: 100 },
        { installments: 6, total: 600 },
        { installments: 12, total: 1200 },
      ],
      bumps: [],
    };
    const product = {
      price: 1000,
      bumps: [],
    };
    const selectedBumps: string[] = [];

    const { result } = renderHook(() =>
      useTotalPriceCalculator({
        selectedOption,
        selectedInstallment,
        productInstallments,
        product: product as any,
        selectedBumps,
      })
    );

    expect(result.current).toBe(0);
  });
});
