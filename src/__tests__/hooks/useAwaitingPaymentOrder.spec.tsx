import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import React from "react";

import { makeAwaitingPaymentData } from "@/factories/awaitingPayment";
import { useAwaitingPaymentOrder } from "@/hooks/useAwaitingPaymentOrder";
import { awaitingPaymentService } from "@/services/awaiting-payment";
import { OrderStatus } from "@/types/orderResponse";

jest.mock("@/services/awaiting-payment", () => ({
  awaitingPaymentService: {
    getOrder: jest.fn(),
  },
}));

const mockAwaitingPaymentService = awaitingPaymentService as jest.Mocked<
  typeof awaitingPaymentService
>;

describe("useAwaitingPaymentOrder", () => {
  let queryClient: QueryClient;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    jest.clearAllMocks();
  });

  afterEach(() => {
    queryClient.clear();
  });

  describe("successful data fetching", () => {
    it("should fetch order data when orderId is provided", async () => {
      const orderId = "order_123456789";
      const mockOrderData = makeAwaitingPaymentData("order", {
        order_id: orderId,
        transaction: {
          status: OrderStatus.PENDING,
          amount: 2990,
          description: "1x de R$29,90",
        },
      });

      mockAwaitingPaymentService.getOrder.mockResolvedValueOnce(mockOrderData);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.orderData).toEqual({});

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.orderData).toEqual(mockOrderData);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledWith(orderId);
      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledTimes(1);
    });

    it("should return correct query key", async () => {
      const orderId = "order_987654321";
      const mockOrderData = makeAwaitingPaymentData("order", {
        order_id: orderId,
      });

      mockAwaitingPaymentService.getOrder.mockResolvedValueOnce(mockOrderData);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      const cachedData = queryClient.getQueryData([
        "awaiting-payment/order",
        orderId,
      ]);
      expect(cachedData).toEqual(mockOrderData);
    });

    it("should handle different order statuses", async () => {
      const orderId = "order_approved";
      const mockOrderData = makeAwaitingPaymentData("order", {
        order_id: orderId,
        transaction: {
          status: OrderStatus.APPROVED,
          amount: 5000,
          description: "1x de R$50,00",
        },
      });

      mockAwaitingPaymentService.getOrder.mockResolvedValueOnce(mockOrderData);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.orderData.transaction.status).toBe(
        OrderStatus.APPROVED
      );
      expect(result.current.orderData.transaction.amount).toBe(5000);
    });
  });

  describe("query behavior", () => {
    it("should not fetch data when orderId is empty", () => {
      const { result } = renderHook(() => useAwaitingPaymentOrder(""), {
        wrapper: Wrapper,
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.orderData).toEqual({});
      expect(mockAwaitingPaymentService.getOrder).not.toHaveBeenCalled();
    });

    it("should not fetch data when orderId is null", () => {
      const { result } = renderHook(
        () => useAwaitingPaymentOrder(null as any),
        {
          wrapper: Wrapper,
        }
      );

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.orderData).toEqual({});
      expect(mockAwaitingPaymentService.getOrder).not.toHaveBeenCalled();
    });

    it("should not fetch data when orderId is undefined", () => {
      const { result } = renderHook(
        () => useAwaitingPaymentOrder(undefined as any),
        {
          wrapper: Wrapper,
        }
      );

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.orderData).toEqual({});
      expect(mockAwaitingPaymentService.getOrder).not.toHaveBeenCalled();
    });

    it("should refetch when orderId changes", async () => {
      const firstOrderId = "order_first";
      const secondOrderId = "order_second";

      const firstMockData = makeAwaitingPaymentData("order", {
        order_id: firstOrderId,
      });
      const secondMockData = makeAwaitingPaymentData("order", {
        order_id: secondOrderId,
      });

      mockAwaitingPaymentService.getOrder
        .mockResolvedValueOnce(firstMockData)
        .mockResolvedValueOnce(secondMockData);

      const { result, rerender } = renderHook(
        ({ orderId }) => useAwaitingPaymentOrder(orderId),
        {
          wrapper: Wrapper,
          initialProps: { orderId: firstOrderId },
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.orderData).toEqual(firstMockData);
      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledWith(
        firstOrderId
      );

      rerender({ orderId: secondOrderId });

      await waitFor(() => {
        expect(result.current.orderData).toEqual(secondMockData);
      });

      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledWith(
        secondOrderId
      );
      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledTimes(2);
    });
  });

  describe("error handling", () => {
    it("should handle API errors", async () => {
      const orderId = "order_error";
      const mockError = new Error("Network error");

      mockAwaitingPaymentService.getOrder.mockRejectedValueOnce(mockError);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.orderData).toEqual({});
      expect(result.current.isLoading).toBe(false);
      expect(mockAwaitingPaymentService.getOrder).toHaveBeenCalledWith(orderId);
    });

    it("should handle 404 errors", async () => {
      const orderId = "order_not_found";
      const mockError = {
        response: {
          status: 404,
          data: { message: "Order not found" },
        },
      };

      mockAwaitingPaymentService.getOrder.mockRejectedValueOnce(mockError);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.orderData).toEqual({});
    });

    it("should return empty object as default when no data", () => {
      const { result } = renderHook(() => useAwaitingPaymentOrder(""), {
        wrapper: Wrapper,
      });

      expect(result.current.orderData).toEqual({});
      expect(typeof result.current.orderData).toBe("object");
    });
  });

  describe("return values", () => {
    it("should return all react-query properties", async () => {
      const orderId = "order_props_test";
      const mockOrderData = makeAwaitingPaymentData("order", {
        order_id: orderId,
      });

      mockAwaitingPaymentService.getOrder.mockResolvedValueOnce(mockOrderData);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current).toHaveProperty("orderData");
      expect(result.current).toHaveProperty("isLoading");
      expect(result.current).toHaveProperty("isError");
      expect(result.current).toHaveProperty("isSuccess");
      expect(result.current).toHaveProperty("error");
      expect(result.current).toHaveProperty("refetch");
      expect(result.current).toHaveProperty("isFetching");
    });

    it("should maintain orderData structure", async () => {
      const orderId = "order_structure_test";
      const mockOrderData = makeAwaitingPaymentData("order", {
        order_id: orderId,
        transaction: {
          status: OrderStatus.PENDING,
          amount: 1500,
          payment_method: "credit_card",
          description: "1x de R$15,00",
        },
        credit_card: {
          brand: "visa",
          first_digits: 411111,
          last_digits: 1111,
          installments: 1,
        },
      });

      mockAwaitingPaymentService.getOrder.mockResolvedValueOnce(mockOrderData);

      const { result } = renderHook(() => useAwaitingPaymentOrder(orderId), {
        wrapper: Wrapper,
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.orderData).toHaveProperty("order_id", orderId);
      expect(result.current.orderData).toHaveProperty("transaction");
      expect(result.current.orderData).toHaveProperty("credit_card");
      expect(result.current.orderData).toHaveProperty("product");
      expect(result.current.orderData).toHaveProperty("bumps");
    });
  });
});
