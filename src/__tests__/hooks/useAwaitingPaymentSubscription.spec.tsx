import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";
import React from "react";

import { makeAwaitingPaymentData } from "@/factories/awaitingPayment";
import { useAwaitingPaymentSubscription } from "@/hooks/useAwaitingPaymentSubscription";
import { awaitingPaymentService } from "@/services/awaiting-payment";
import { OrderStatus } from "@/types/orderResponse";

jest.mock("@/services/awaiting-payment", () => ({
  awaitingPaymentService: {
    getSubscription: jest.fn(),
  },
}));

const mockAwaitingPaymentService = awaitingPaymentService as jest.Mocked<
  typeof awaitingPaymentService
>;

describe("useAwaitingPaymentSubscription", () => {
  let queryClient: QueryClient;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    jest.clearAllMocks();
  });

  afterEach(() => {
    queryClient.clear();
  });

  describe("successful data fetching", () => {
    it("should fetch subscription data when subscriptionCode is provided", async () => {
      const subscriptionCode = "SB1B4H16G";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
        transaction: {
          status: OrderStatus.PENDING,
          amount: 3560,
          description: "1x de R$35,60",
        },
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.subscriptionData).toEqual({});

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.subscriptionData).toEqual(mockSubscriptionData);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledWith(
        subscriptionCode
      );
      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledTimes(
        1
      );
    });

    it("should return correct query key", async () => {
      const subscriptionCode = "SB9Z8Y7X6W";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      const cachedData = queryClient.getQueryData([
        "awaiting-payment/subscription",
        subscriptionCode,
      ]);
      expect(cachedData).toEqual(mockSubscriptionData);
    });

    it("should handle different subscription statuses", async () => {
      const subscriptionCode = "SB_APPROVED";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
        transaction: {
          status: OrderStatus.APPROVED,
          amount: 5000,
          description: "1x de R$50,00",
        },
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.subscriptionData.transaction.status).toBe(
        OrderStatus.APPROVED
      );
      expect(result.current.subscriptionData.transaction.amount).toBe(5000);
    });

    it("should handle different payment methods", async () => {
      const subscriptionCode = "SB_PIX";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
        transaction: {
          status: OrderStatus.PENDING,
          payment_method: "pix",
          description: "1x de R$25,90",
        },
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.subscriptionData.transaction.payment_method).toBe(
        "pix"
      );
    });
  });

  describe("query behavior", () => {
    it("should not fetch data when subscriptionCode is empty", () => {
      const { result } = renderHook(() => useAwaitingPaymentSubscription(""), {
        wrapper: Wrapper,
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.subscriptionData).toEqual({});
      expect(mockAwaitingPaymentService.getSubscription).not.toHaveBeenCalled();
    });

    it("should not fetch data when subscriptionCode is null", () => {
      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(null as any),
        {
          wrapper: Wrapper,
        }
      );

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.subscriptionData).toEqual({});
      expect(mockAwaitingPaymentService.getSubscription).not.toHaveBeenCalled();
    });

    it("should not fetch data when subscriptionCode is undefined", () => {
      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(undefined as any),
        {
          wrapper: Wrapper,
        }
      );

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.subscriptionData).toEqual({});
      expect(mockAwaitingPaymentService.getSubscription).not.toHaveBeenCalled();
    });

    it("should refetch when subscriptionCode changes", async () => {
      const firstSubscriptionCode = "SB_FIRST";
      const secondSubscriptionCode = "SB_SECOND";

      const firstMockData = makeAwaitingPaymentData("subscription", {
        subscription_code: firstSubscriptionCode,
      });
      const secondMockData = makeAwaitingPaymentData("subscription", {
        subscription_code: secondSubscriptionCode,
      });

      mockAwaitingPaymentService.getSubscription
        .mockResolvedValueOnce(firstMockData)
        .mockResolvedValueOnce(secondMockData);

      const { result, rerender } = renderHook(
        ({ subscriptionCode }) =>
          useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
          initialProps: { subscriptionCode: firstSubscriptionCode },
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.subscriptionData).toEqual(firstMockData);
      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledWith(
        firstSubscriptionCode
      );

      rerender({ subscriptionCode: secondSubscriptionCode });

      await waitFor(() => {
        expect(result.current.subscriptionData).toEqual(secondMockData);
      });

      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledWith(
        secondSubscriptionCode
      );
      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledTimes(
        2
      );
    });
  });

  describe("error handling", () => {
    it("should handle API errors", async () => {
      const subscriptionCode = "SB_ERROR";
      const mockError = new Error("Network error");

      mockAwaitingPaymentService.getSubscription.mockRejectedValueOnce(
        mockError
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.subscriptionData).toEqual({});
      expect(result.current.isLoading).toBe(false);
      expect(mockAwaitingPaymentService.getSubscription).toHaveBeenCalledWith(
        subscriptionCode
      );
    });

    it("should handle 404 errors", async () => {
      const subscriptionCode = "SB_NOT_FOUND";
      const mockError = {
        response: {
          status: 404,
          data: { message: "Subscription not found" },
        },
      };

      mockAwaitingPaymentService.getSubscription.mockRejectedValueOnce(
        mockError
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.subscriptionData).toEqual({});
    });

    it("should handle unauthorized errors", async () => {
      const subscriptionCode = "SB_UNAUTHORIZED";
      const mockError = {
        response: {
          status: 401,
          data: { message: "Unauthorized access" },
        },
      };

      mockAwaitingPaymentService.getSubscription.mockRejectedValueOnce(
        mockError
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(mockError);
      expect(result.current.subscriptionData).toEqual({});
    });

    it("should return empty object as default when no data", () => {
      const { result } = renderHook(() => useAwaitingPaymentSubscription(""), {
        wrapper: Wrapper,
      });

      expect(result.current.subscriptionData).toEqual({});
      expect(typeof result.current.subscriptionData).toBe("object");
    });
  });

  describe("return values", () => {
    it("should return all react-query properties", async () => {
      const subscriptionCode = "SB_PROPS_TEST";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current).toHaveProperty("subscriptionData");
      expect(result.current).toHaveProperty("isLoading");
      expect(result.current).toHaveProperty("isError");
      expect(result.current).toHaveProperty("isSuccess");
      expect(result.current).toHaveProperty("error");
      expect(result.current).toHaveProperty("refetch");
      expect(result.current).toHaveProperty("isFetching");
    });

    it("should maintain subscriptionData structure", async () => {
      const subscriptionCode = "SB_STRUCTURE_TEST";
      const mockSubscriptionData = makeAwaitingPaymentData("subscription", {
        subscription_code: subscriptionCode,
        transaction: {
          status: OrderStatus.PENDING,
          amount: 1500,
          payment_method: "credit_card",
          description: "1x de R$15,00",
        },
        credit_card: {
          brand: "mastercard",
          first_digits: 517912,
          last_digits: 3590,
          installments: 1,
        },
      });

      mockAwaitingPaymentService.getSubscription.mockResolvedValueOnce(
        mockSubscriptionData
      );

      const { result } = renderHook(
        () => useAwaitingPaymentSubscription(subscriptionCode),
        {
          wrapper: Wrapper,
        }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.subscriptionData).toHaveProperty(
        "subscription_code",
        subscriptionCode
      );
      expect(result.current.subscriptionData).toHaveProperty("transaction");
      expect(result.current.subscriptionData).toHaveProperty("credit_card");
      expect(result.current.subscriptionData).toHaveProperty("product");
      expect(result.current.subscriptionData).toHaveProperty("bumps");
    });
  });
});
