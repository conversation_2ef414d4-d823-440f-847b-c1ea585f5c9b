import * as Sentry from "@sentry/nextjs";
import { act, renderHook, waitFor } from "@testing-library/react";

import { makeAwaitingPaymentOrder } from "@/factories/awaitingPayment";
import { useAwaitingPaymentData } from "@/hooks/useAwaitingPaymentData";
import { OrderStatus } from "@/types/orderResponse";
import { fromBase64, makeQuery } from "@/utils/stringUtils/enconding";

jest.mock("next/navigation", () => ({
  useParams: () => ({
    locale: "pt-BR",
  }),
  useSearchParams: () => {
    return new URLSearchParamsMock();
  },
  useRouter: () => ({
    replace: jest.fn().mockResolvedValue(undefined),
  }),
}));

jest.mock("@/store/buyerStore", () => {
  const setPaymentError = jest.fn();
  const setFailedPaymentInformation = jest.fn();
  return {
    buyerStore: () => ({
      setPaymentError,
      setFailedPaymentInformation,
    }),
  };
});

jest.mock("@sentry/nextjs", () => ({
  captureException: jest.fn(),
}));

jest.mock("@/utils/stringUtils/enconding", () => ({
  fromBase64: jest.fn(),
  makeQuery: jest.fn(),
}));

class URLSearchParamsMock {
  private params = new Map<string, string>();

  constructor(init?: Record<string, string>) {
    if (init) {
      for (const key in init) {
        this.params.set(key, init[key]);
      }
    }
  }

  get = (key: string) => this.params.get(key);
  toString = () => JSON.stringify(Object.fromEntries(this.params));
}

describe("useAwaitingPaymentData", () => {
  const data = makeAwaitingPaymentOrder();

  const setErrorMock =
    require("@/store/buyerStore").buyerStore().setPaymentError;
  const setInfoMock =
    require("@/store/buyerStore").buyerStore().setFailedPaymentInformation;
  const captureException = Sentry.captureException;
  const mockFromBase64 = fromBase64 as jest.MockedFunction<typeof fromBase64>;
  const mockMakeQuery = makeQuery as jest.MockedFunction<typeof makeQuery>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockOrderResponse = {
    order: { id: "123" },
    payment_method: "credit_card",
    customer_email: "<EMAIL>",
    product: {
      id: "prod-123",
      title: "Test Product",
      description: "Test Product Description",
      platform: {
        name: "Test Platform",
        url: "https://test-platform.com",
      },
    },
    payment: {
      status: OrderStatus.PENDING,
      description: "Pagamento em processamento",
      brand: "visa",
      first_digits: "4111",
      last_digits: "1111",
      installments: 1,
    },
    bumps: [],
  };

  const mockSubscriptionResponse = {
    code: "456",
    payment_method: "credit_card",
    customer_email: "Test Customer",
    product: {
      id: "prod-456",
      title: "Test Subscription",
      description: "Test Subscription Description",
      platform: {
        name: "Test Platform",
        url: "https://test-platform.com",
      },
    },
    payment: {
      status: OrderStatus.PENDING,
      description: "Pagamento em processamento",
      brand: "mastercard",
      first_digits: "5555",
      last_digits: "4444",
      installments: 1,
    },
  };

  it("should decode and set initial data from search param for order", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockOrderResponse));

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    expect(result.current.data?.payment?.description).toBe(
      "Pagamento em processamento"
    );
    expect(result.current.data?.order_id).toBe("123");
    expect(result.current.data?.customer_email).toBe("<EMAIL>");
  });

  it("should decode and set initial data from search param for subscription", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockSubscriptionResponse));

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    expect(result.current.data?.payment?.description).toBe(
      "Pagamento em processamento"
    );
    expect(result.current.data?.subscription_id).toBe("123");
    expect(result.current.data?.customer_email).toBe("Test Customer");
  });

  it("should capture exception on invalid base64", () => {
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: "INVALID!" }));

    mockFromBase64.mockImplementation(() => {
      throw new Error("Invalid base64");
    });

    renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    expect(captureException).toHaveBeenCalled();
  });

  it("should set modal and update data when status is FAILED", async () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockOrderResponse));

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.FAILED },
        },

        "123"
      )
    );

    await waitFor(() => {
      expect(result.current.isOpenModal).toBe(true);
    });
  });

  it("should call replace to success when status is APPROVED", async () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockOrderResponse));
    mockMakeQuery.mockReturnValue("encoded-query-data");

    const replaceMock = jest.fn();
    jest.spyOn(require("next/navigation"), "useRouter").mockReturnValue({
      replace: replaceMock,
    });

    renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.APPROVED },
        },

        "123"
      )
    );

    await waitFor(() => {
      expect(replaceMock).toHaveBeenCalledWith(
        "/pt-BR/success?d=encoded-query-data"
      );
    });
  });

  it("should call store setters on setError", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    const mockResponseWithError = {
      ...mockOrderResponse,
      payment: {
        ...mockOrderResponse.payment,
        title: "Payment Failed",
        description: "Your payment could not be processed",
      },
    };

    mockFromBase64.mockReturnValue(JSON.stringify(mockResponseWithError));

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    act(() => {
      result.current.setError();
    });

    expect(setErrorMock).toHaveBeenCalledWith(true);
    expect(setInfoMock).toHaveBeenCalledWith({
      title: "Payment Failed",
      description: "Your payment could not be processed",
    });
  });

  it("should handle socket callback for APPROVED status", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockOrderResponse));
    mockMakeQuery.mockReturnValue("encoded-query-data");

    const replaceMock = jest.fn();
    jest.spyOn(require("next/navigation"), "useRouter").mockReturnValue({
      replace: replaceMock,
    });

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    act(() => {
      result.current.socketCallback({
        ...mockOrderResponse,
        payment: {
          ...mockOrderResponse.payment,
          status: OrderStatus.APPROVED,
        },
      });
    });

    expect(replaceMock).toHaveBeenCalledWith(
      "/pt-BR/success?d=encoded-query-data"
    );
  });

  it("should handle socket callback for FAILED status", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    mockFromBase64.mockReturnValue(JSON.stringify(mockOrderResponse));

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    act(() => {
      result.current.socketCallback({
        ...mockOrderResponse,
        payment: {
          ...mockOrderResponse.payment,
          status: OrderStatus.FAILED,
        },
      });
    });

    expect(result.current.isOpenModal).toBe(true);
  });

  it("should handle thank_you_page_url redirect", () => {
    const validBase64 = "valid-base64-string";
    jest
      .spyOn(require("next/navigation"), "useSearchParams")
      .mockReturnValue(new URLSearchParamsMock({ d: validBase64 }));

    const mockResponseWithThankYouUrl = {
      ...mockOrderResponse,
      thank_you_page_url: "https://example.com/thank-you",
    };

    mockFromBase64.mockReturnValue(JSON.stringify(mockResponseWithThankYouUrl));

    Object.defineProperty(window, "location", {
      value: {
        href: "",
      },
      writable: true,
    });

    const { result } = renderHook(() =>
      useAwaitingPaymentData(
        {
          ...data,
          transaction: { ...data.transaction, status: OrderStatus.PENDING },
        },

        "123"
      )
    );

    act(() => {
      result.current.socketCallback({
        ...mockOrderResponse,
        payment: {
          ...mockOrderResponse.payment,
          status: OrderStatus.APPROVED,
        },
        thank_you_page_url: "https://example.com/thank-you",
      });
    });

    expect(window.location.href).toBe("https://example.com/thank-you");
  });
});
