import { renderHook } from "@testing-library/react";
import { z } from "zod";

import { useSubscriptionRenewalFormSchema } from "@/hooks/useSubscriptionRenewalFormSchema";
import { createSubscriptionRenewalSchema } from "@/schemas/subscriptionRenewalSchema";
import type { PaymentOption } from "@/types/paymentOptions";

jest.mock("next-intl", () => ({
  useTranslations: () => (key: string) => key,
}));

jest.mock("@/schemas/subscriptionRenewalSchema", () => ({
  createSubscriptionRenewalSchema: jest.fn(() =>
    z.object({
      cardNumber: z.string(),
      cardHolderName: z.string(),
      cardCvv: z.string(),
      cardExpirationDateMonth: z.string(),
      cardExpirationDateYear: z.string(),
      instalment: z.string().optional(),
    })
  ),
}));

describe("useSubscriptionRenewalFormSchema", () => {
  it("returns formMethods with default values and a translation function", () => {
    const { result } = renderHook(() =>
      useSubscriptionRenewalFormSchema("CREDIT_CARD" as PaymentOption)
    );
    const { formMethods, translations } = result.current;
    expect(typeof translations).toBe("function");
    expect(formMethods.getValues()).toEqual({
      cardNumber: "",
      cardHolderName: "",
      cardCvv: "",
      cardExpirationDateMonth: "",
      cardExpirationDateYear: "",
      instalment: undefined,
    });
  });

  it("calls createSubscriptionRenewalSchema with translations and selected option", () => {
    const option = "PIX" as PaymentOption;
    renderHook(() => useSubscriptionRenewalFormSchema(option));
    expect(createSubscriptionRenewalSchema).toHaveBeenCalledWith(
      expect.any(Function),
      option
    );
  });
});
