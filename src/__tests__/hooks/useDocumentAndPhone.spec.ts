import { renderHook } from "@testing-library/react";
import { useFormContext } from "react-hook-form";
import { parsePhoneNumber } from "react-phone-number-input";

import { useDocumentAndPhone } from "@/hooks/useDocumentAndPhone";

jest.mock("react-hook-form", () => ({
  useFormContext: jest.fn(),
}));

jest.mock("react-phone-number-input", () => ({
  parsePhoneNumber: jest.fn(),
}));

describe("useDocumentAndPhone", () => {
  const resetFieldMock = jest.fn();
  const setValueMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useFormContext as jest.Mock).mockReturnValue({
      resetField: resetFieldMock,
      setValue: setValueMock,
    });
  });

  it("should initialize haveBrazilianDocument to true", () => {
    const { result } = renderHook(() => useDocumentAndPhone());
    expect(result.current.haveBrazilianDocument).toBe(true);
  });

  describe("extractDDI", () => {
    it('should return the country calling code with "+" when phone number is valid', () => {
      const { result } = renderHook(() => useDocumentAndPhone());

      (parsePhoneNumber as jest.Mock).mockReturnValue({
        countryCallingCode: "55",
      });

      const ddi = result.current.extractDDI("+55 11 91234-5678");
      expect(ddi).toBe("+55");
      expect(parsePhoneNumber).toHaveBeenCalledWith("+55 11 91234-5678");
    });

    it("should return empty string when phone number is invalid", () => {
      const { result } = renderHook(() => useDocumentAndPhone());

      (parsePhoneNumber as jest.Mock).mockReturnValue(undefined);

      const ddi = result.current.extractDDI("invalid-number");
      expect(ddi).toBe("");
      expect(parsePhoneNumber).toHaveBeenCalledWith("invalid-number");
    });

    it("should return empty string when countryCallingCode is undefined", () => {
      const { result } = renderHook(() => useDocumentAndPhone());

      (parsePhoneNumber as jest.Mock).mockReturnValue({});

      const ddi = result.current.extractDDI("******-456-7890");
      expect(ddi).toBe("");
      expect(parsePhoneNumber).toHaveBeenCalledWith("******-456-7890");
    });
  });
});
