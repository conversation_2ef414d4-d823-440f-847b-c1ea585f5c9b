import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { act, renderHook } from "@testing-library/react";
import { getCreditCardNameByNumber } from "@thebank/creditcard-js";

import { useCreditCardFormatter } from "@/hooks/useCreditCardFormatter";

jest.mock("@thebank/creditcard-js", () => ({
  getCreditCardNameByNumber: jest.fn(),
}));

jest.mock("@/hooks/useCreditCardList", () => ({
  useCreditCardList: jest.fn(),
}));

import { useCreditCardList } from "@/hooks/useCreditCardList";

const mockedGetCreditCardNameByNumber =
  getCreditCardNameByNumber as jest.MockedFunction<
    typeof getCreditCardNameByNumber
  >;

const mockedUseCreditCardList = useCreditCardList as jest.MockedFunction<
  typeof useCreditCardList
>;

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useCreditCardFormatter", () => {
  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
    mockedUseCreditCardList.mockReturnValue({
      data: [
        { brand: "Visa", icon_url: "", priority: 0 },
        { brand: "MasterCard", icon_url: "", priority: 1 },
        { brand: "Elo", icon_url: "", priority: 0 },
        { brand: "American Express", icon_url: "", priority: 0 },
        { brand: "Diners Club", icon_url: "", priority: 0 },
        { brand: "Discover", icon_url: "", priority: 0 },
        { brand: "JCB", icon_url: "", priority: 0 },
      ],
      isLoading: false,
      isError: false,
      error: null,
      isSuccess: true,
      refetch: jest.fn(),
      isPending: false,
      isLoadingError: false,
      isRefetchError: false,
      status: "success",
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      errorUpdateCount: 0,
      isFetched: false,
      isFetchedAfterMount: false,
      isFetching: false,
      isInitialLoading: false,
      isPaused: false,
      isPlaceholderData: false,
      isRefetching: false,
      isStale: false,
      fetchStatus: "fetching",
      promise: Promise.resolve([
        { brand: "Union Pay", icon_url: "unionpay_icon_url", priority: 0 },
      ]),
    });
  });

  describe("formatCreditCard", () => {
    test.each([
      ["1234567890123456", "1234 5678 9012 3456"],
      ["1234 5678 9012 3456", "1234 5678 9012 3456"],
      ["1234-5678-9012-3456", "1234 5678 9012 3456"],
      ["12345678", "1234 5678"],
      ["", ""],
    ])('should format "%s" to "%s"', (input, expected) => {
      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { formatCreditCard } = result.current;
      expect(formatCreditCard(input)).toBe(expected);
    });
  });

  describe("mappedCreditCards", () => {
    it("should return filtered and mapped credit cards with correct icon_url", () => {
      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });

      expect(result.current.creditCardList).toEqual([
        { brand: "Visa", icon_url: "/brands/Visa.svg", priority: 0 },
        {
          brand: "MasterCard",
          icon_url: "/brands/MasterCard.svg",
          priority: 1,
        },
        { brand: "Elo", icon_url: "/brands/Elo.svg", priority: 0 },
        { brand: "Discover", icon_url: "/brands/Discover.svg", priority: 0 },
        { brand: "JCB", icon_url: "/brands/JCB.svg", priority: 0 },
      ]);
    });

    it("should use existing icon_url if provided", () => {
      mockedUseCreditCardList.mockReturnValue({
        data: [
          { brand: "Visa", icon_url: "custom_visa_url", priority: 0 },
          { brand: "MasterCard", icon_url: "", priority: 1 },
        ],
        isLoading: false,
        isError: false,
        error: null,
        isSuccess: true,
        refetch: jest.fn(),
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        status: "success",
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        fetchStatus: "fetching",
        promise: Promise.resolve([
          { brand: "Union Pay", icon_url: "unionpay_icon_url", priority: 0 },
        ]),
      });

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });

      expect(result.current.creditCardList).toEqual([
        { brand: "Visa", icon_url: "custom_visa_url", priority: 0 },
        {
          brand: "MasterCard",
          icon_url: "/brands/MasterCard.svg",
          priority: 1,
        },
      ]);
    });

    it("should return empty array when creditCardList is empty", () => {
      mockedUseCreditCardList.mockReturnValue({
        data: [] as { brand: string; icon_url: string; priority: number }[],
        isLoading: false,
        isError: false,
        error: null,
        isSuccess: true,
        refetch: jest.fn(),
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        status: "success",
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        fetchStatus: "fetching",
        promise: Promise.resolve([
          { brand: "Union Pay", icon_url: "unionpay_icon_url", priority: 0 },
        ]),
      });

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });

      expect(result.current.creditCardList).toEqual([]);
    });

    it("should return empty array when creditCardList is null/undefined (lines 31-33)", () => {
      mockedUseCreditCardList.mockReturnValue({
        data: null as any,
        isLoading: false,
        isError: false,
        error: null,
        isSuccess: true,
        refetch: jest.fn(),
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        status: "success",
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        fetchStatus: "fetching",
        promise: Promise.resolve([]),
      });

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });

      expect(result.current.creditCardList).toEqual([]);
    });

    it("should filter out cards with brands not in brandMapping (lines 32-36)", () => {
      mockedUseCreditCardList.mockReturnValue({
        data: [
          { brand: "Visa", icon_url: "", priority: 0 },
          { brand: "UnknownBrand", icon_url: "", priority: 1 },
          { brand: "MasterCard", icon_url: "", priority: 2 },
        ],
        isLoading: false,
        isError: false,
        error: null,
        isSuccess: true,
        refetch: jest.fn(),
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        status: "success",
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        fetchStatus: "fetching",
        promise: Promise.resolve([]),
      });

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });

      expect(result.current.creditCardList).toEqual([
        { brand: "Visa", icon_url: "/brands/Visa.svg", priority: 0 },
        {
          brand: "MasterCard",
          icon_url: "/brands/MasterCard.svg",
          priority: 2,
        },
      ]);
    });
  });

  describe("card detection functions", () => {
    it("should test isUnionPay function with various card numbers", () => {
      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      act(() => {
        updateCardBrandIcon("6200000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Union Pay.svg");

      act(() => {
        updateCardBrandIcon("6200000000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Union Pay.svg");

      act(() => {
        updateCardBrandIcon("6100000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");

      act(() => {
        updateCardBrandIcon("620000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");

      act(() => {
        updateCardBrandIcon("62000000000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");
    });

    it("should test isMirCard function with various card numbers", () => {
      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      act(() => {
        updateCardBrandIcon("2200000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Mir.svg");

      act(() => {
        updateCardBrandIcon("2204000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Mir.svg");

      act(() => {
        updateCardBrandIcon("2199000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");

      act(() => {
        updateCardBrandIcon("2205000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");

      act(() => {
        updateCardBrandIcon("220000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");
    });

    it("should test isHiper function with various card numbers", () => {
      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      act(() => {
        updateCardBrandIcon("6370000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Hiper.svg");

      act(() => {
        updateCardBrandIcon("6370950000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Hiper.svg");

      act(() => {
        updateCardBrandIcon("6373742300000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/Hiper.svg");

      act(() => {
        updateCardBrandIcon("6371000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");

      act(() => {
        updateCardBrandIcon("637000000000000");
      });
      expect(result.current.cardBrandIcon).toBe("/brands/defaultcard.svg");
    });
  });

  describe("updateCardBrandIcon", () => {
    test.each([
      ["4111 1111 1111 1111", "Visa", "/brands/Visa.svg"],
      ["5500 0000 0000 0004", "MasterCard", "/brands/MasterCard.svg"],
      ["3400 0000 0000 009", "amex", "/brands/American Express (Amex).svg"],
      ["0000 0000 0000 0000", null, "/brands/defaultcard.svg"],
    ])(
      "should update cardBrandIcon to %j for brand %j",
      (input, brand, expectedIcon) => {
        mockedGetCreditCardNameByNumber.mockReturnValue(brand || "");

        const { result } = renderHook(() => useCreditCardFormatter(), {
          wrapper: Wrapper,
        });
        const { updateCardBrandIcon } = result.current;

        act(() => {
          updateCardBrandIcon(input);
        });

        expect(result.current.cardBrandIcon).toBe(expectedIcon);
      }
    );

    it("should detect UnionPay cards when getCreditCardNameByNumber returns invalid", () => {
      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      act(() => {
        updateCardBrandIcon("6200000000000000");
      });

      expect(result.current.cardBrandIcon).toBe("/brands/Union Pay.svg");
    });

    it("should detect Mir cards when getCreditCardNameByNumber returns invalid", () => {
      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      act(() => {
        updateCardBrandIcon("2200000000000000");
      });

      expect(result.current.cardBrandIcon).toBe("/brands/Mir.svg");
    });

    it("should detect Hiper cards when getCreditCardNameByNumber returns invalid", () => {
      mockedGetCreditCardNameByNumber.mockReturnValue(
        "Credit card is invalid!"
      );

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      act(() => {
        updateCardBrandIcon("6370000000000000");
      });

      expect(result.current.cardBrandIcon).toBe("/brands/Hiper.svg");
    });

    it("should use custom icon_url from creditCardList when available", () => {
      mockedUseCreditCardList.mockReturnValue({
        data: [
          {
            brand: "Visa",
            icon_url: "https://custom-visa-icon.com/visa.png",
            priority: 0,
          },
        ],
        isLoading: false,
        isError: false,
        error: null,
        isSuccess: true,
        refetch: jest.fn(),
        isPending: false,
        isLoadingError: false,
        isRefetchError: false,
        status: "success",
        dataUpdatedAt: 0,
        errorUpdatedAt: 0,
        failureCount: 0,
        failureReason: null,
        errorUpdateCount: 0,
        isFetched: false,
        isFetchedAfterMount: false,
        isFetching: false,
        isInitialLoading: false,
        isPaused: false,
        isPlaceholderData: false,
        isRefetching: false,
        isStale: false,
        fetchStatus: "fetching",
        promise: Promise.resolve([]),
      });

      mockedGetCreditCardNameByNumber.mockReturnValue("Visa");

      const { result } = renderHook(() => useCreditCardFormatter(), {
        wrapper: Wrapper,
      });
      const { updateCardBrandIcon } = result.current;

      act(() => {
        updateCardBrandIcon("****************");
      });

      expect(result.current.cardBrandIcon).toBe(
        "https://custom-visa-icon.com/visa.png"
      );
    });
  });
});
