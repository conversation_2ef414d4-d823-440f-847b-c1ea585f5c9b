import { zodResolver } from "@hookform/resolvers/zod";
import { renderHook } from "@testing-library/react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { ZodSchema } from "zod";

import { useBuyerFormSchema } from "@/hooks/useBuyerFormSchema";
import { createBuyerSchema } from "@/schemas/createBuyerSchema";
import { PaymentOption } from "@/types/paymentOptions";

jest.mock("next-intl", () => ({
  useTranslations: jest.fn(),
}));

jest.mock("react-hook-form", () => ({
  useForm: jest.fn(),
}));

jest.mock("@hookform/resolvers/zod", () => ({
  zodResolver: jest.fn(),
}));

jest.mock("@/schemas/createBuyerSchema", () => ({
  createBuyerSchema: jest.fn(),
}));

describe("useBuyerFormSchema", () => {
  const tMock = jest.fn();
  const buyerSchemaMock = {} as ZodSchema;
  const formMethodsMock = {
    register: jest.fn(),
    handleSubmit: jest.fn(),
    formState: { errors: {} },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (useTranslations as jest.Mock).mockReturnValue(tMock);

    (createBuyerSchema as jest.Mock).mockReturnValue(buyerSchemaMock);

    (zodResolver as jest.Mock).mockReturnValue("zodResolverMock");

    (useForm as jest.Mock).mockReturnValue(formMethodsMock);
  });

  it("should call useTranslations with correct namespace", () => {
    renderHook(() => useBuyerFormSchema(true, PaymentOption.CreditCard));
    expect(useTranslations).toHaveBeenCalledWith("buyer_form");
  });

  it("should call createBuyerSchema with correct arguments", () => {
    renderHook(() => useBuyerFormSchema(false, PaymentOption.Pix));
    expect(createBuyerSchema).toHaveBeenCalledWith(
      tMock,
      false,
      PaymentOption.Pix
    );
  });

  it("should call zodResolver with the buyerSchema", () => {
    renderHook(() => useBuyerFormSchema(true, PaymentOption.CreditCard));
    expect(zodResolver).toHaveBeenCalledWith(buyerSchemaMock);
  });

  it("should call useForm with correct arguments", () => {
    renderHook(() => useBuyerFormSchema(true, PaymentOption.CreditCard));
    expect(useForm).toHaveBeenCalledWith({
      resolver: "zodResolverMock",
      mode: "onSubmit",
    });
  });

  it("should return formMethods and t", () => {
    const { result } = renderHook(() =>
      useBuyerFormSchema(true, PaymentOption.CreditCard)
    );
    expect(result.current.formMethods).toBe(formMethodsMock);
    expect(result.current.translations).toBe(tMock);
  });
});
