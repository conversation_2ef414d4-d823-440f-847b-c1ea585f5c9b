import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

import { useBrazilianStates } from "@/hooks/useBrazilianStates";
import { fetchStatesList } from "@/services/statesList";

jest.mock("@/services/statesList", () => ({
  fetchStatesList: jest.fn(),
}));

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useBrazilianStates", () => {
  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });

  it("should fetch states list successfully when a valid country is provided", async () => {
    const country = "br";
    const fakeStates = [
      { id: 1, name: "Estado 1", code: "E1" },
      { id: 2, name: "Estado 2", code: "E2" },
    ];
    (fetchStatesList as jest.Mock).mockResolvedValueOnce(fakeStates);

    const { result } = renderHook(() => useBrazilianStates(country), {
      wrapper: Wrapper,
    });

    await waitFor(() => result.current.isSuccess);

    expect(result.current.data).toEqual(fakeStates);
    expect(fetchStatesList).toHaveBeenCalledWith(country);
  });

  it("should handle error when fetching states list", async () => {
    const country = "us";
    const error = new Error("Network Error");
    (fetchStatesList as jest.Mock).mockRejectedValueOnce(error);

    const { result } = renderHook(() => useBrazilianStates(country), {
      wrapper: Wrapper,
    });

    await waitFor(() => result.current.isError);

    expect(fetchStatesList).toHaveBeenCalledWith(country);
  });

  it("should not fetch states list when country is falsy", async () => {
    const country = "";
    const { result } = renderHook(() => useBrazilianStates(country), {
      wrapper: Wrapper,
    });

    await waitFor(() => expect(result.current.status).toBe("pending"));
  });
});
