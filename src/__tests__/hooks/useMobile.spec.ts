import { act, renderHook } from "@testing-library/react";

import { useIsMobile } from "@/hooks/useIsMobile";

describe("useIsMobile", () => {
  let originalMatchMedia: ((query: string) => MediaQueryList) &
    ((query: string) => MediaQueryList);

  beforeEach(() => {
    jest.clearAllMocks();
    originalMatchMedia = window.matchMedia;
  });

  afterEach(() => {
    window.matchMedia = originalMatchMedia;
  });

  it("should return true if screen width is less than or equal to 767px", () => {
    window.matchMedia = (): MediaQueryList => ({
      matches: true,
      media: "(max-width: 767px)",
      onchange: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBeTruthy();
  });

  it("should return false if screen width is greater than 767px", () => {
    window.matchMedia = (query: string): MediaQueryList => ({
      matches: false,
      media: query,
      onchange: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBeFalsy();
  });

  it("should return true if screen width is less than or equal to 767px", () => {
    window.matchMedia = (): MediaQueryList => ({
      matches: true,
      media: "(max-width: 767px)",
      onchange: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBeTruthy();
  });

  it("should return false if screen width is greater than 767px", () => {
    window.matchMedia = (query: string): MediaQueryList => ({
      matches: false,
      media: query,
      onchange: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });

    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBeFalsy();
  });

  it("should update isMobile when media query matches change", () => {
    let listeners: Array<(event: Event) => void> = [];
    const mediaQueryList = {
      matches: false,
      media: "(max-width: 767px)",
      onchange: null,
      addEventListener: (event: string, callback: (event: Event) => void) => {
        if (event === "change") {
          listeners.push(callback);
        }
      },
      removeEventListener: (
        event: string,
        callback: (event: Event) => void
      ) => {
        if (event === "change") {
          listeners = listeners.filter(listener => listener !== callback);
        }
      },
      addListener: (callback: (event: Event) => void) => {
        listeners.push(callback);
      },
      removeListener: (callback: (event: Event) => void) => {
        listeners = listeners.filter(listener => listener !== callback);
      },
      dispatchEvent: (event: Event) => {
        listeners.forEach(callback => {
          if (typeof callback === "function") {
            callback(event);
          }
        });
        return true;
      },
    };

    window.matchMedia = () => mediaQueryList as unknown as MediaQueryList;

    const { result } = renderHook(() => useIsMobile());

    expect(result.current).toBeFalsy();

    act(() => {
      mediaQueryList.matches = true;
      mediaQueryList.dispatchEvent(new Event("change"));
    });

    expect(result.current).toBeTruthy();

    act(() => {
      mediaQueryList.matches = false;
      mediaQueryList.dispatchEvent(new Event("change"));
    });

    expect(result.current).toBeFalsy();
  });

  it("should add and remove event listeners correctly", () => {
    const addEventListener = jest.fn();
    const removeEventListener = jest.fn();

    window.matchMedia = () => ({
      matches: false,
      media: "(max-width: 767px)",
      onchange: null,
      addEventListener,
      removeEventListener,
      dispatchEvent: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
    });

    const { unmount } = renderHook(() => useIsMobile());

    expect(addEventListener).toHaveBeenCalledTimes(1);
    expect(addEventListener).toHaveBeenCalledWith(
      "change",
      expect.any(Function)
    );

    unmount();

    expect(removeEventListener).toHaveBeenCalledTimes(1);
    expect(removeEventListener).toHaveBeenCalledWith(
      "change",
      expect.any(Function)
    );
  });
});
