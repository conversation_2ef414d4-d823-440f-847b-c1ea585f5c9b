import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

import { useProduct } from "@/hooks/useGetProduct";
import { theBankService } from "@/services";

jest.mock("@/services", () => ({
  theBankService: {
    fetchProduct: jest.fn(),
  },
}));

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useProduct", () => {
  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });

  it("should fetch product data successfully", async () => {
    const productId = "123";
    const mockData = { id: productId, name: "Test Product" };
    (theBankService.fetchProduct as jest.Mock).mockResolvedValueOnce(mockData);

    const { result } = renderHook(() => useProduct(productId), {
      wrapper: Wrapper,
    });

    await waitFor(() => result.current.isSuccess);

    expect(result.current.data).toEqual(mockData);
    expect(theBankService.fetchProduct).toHaveBeenCalledWith(productId);
  });

  it("should handle error when fetching product data", async () => {
    const productId = "123";
    const error = new Error("Product not found");
    (theBankService.fetchProduct as jest.Mock).mockRejectedValueOnce(error);

    const { result } = renderHook(() => useProduct(productId), {
      wrapper: Wrapper,
    });

    await waitFor(() => result.current.isError);

    expect(theBankService.fetchProduct).toHaveBeenCalledWith(productId);
  });
});
