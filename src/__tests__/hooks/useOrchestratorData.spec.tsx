import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { renderHook, waitFor } from "@testing-library/react";

import { useOrchestratorData } from "@/hooks/useOrchestratorData";
import { theBankService } from "@/services";
import { PaymentOption } from "@/types/paymentOptions";

jest.mock("@/services", () => ({
  theBankService: {
    fetchOrchestratorData: jest.fn(),
  },
}));

const queryClient = new QueryClient();

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

describe("useOrchestratorData", () => {
  beforeEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });

  it("should fetch data when malga is provided", async () => {
    const mockData = { data: "test data" };
    (theBankService.fetchOrchestratorData as jest.Mock).mockResolvedValueOnce(
      mockData
    );

    const { result } = renderHook(
      () => useOrchestratorData("testMalga", PaymentOption.CreditCard),
      {
        wrapper: Wrapper,
      }
    );

    await waitFor(() => result.current.isSuccess);

    expect(result.current.data).toEqual(mockData);
    expect(theBankService.fetchOrchestratorData).toHaveBeenCalledWith(
      "testMalga"
    );
  });

  it("should not fetch data when malga is not provided", () => {
    const { result } = renderHook(
      () => useOrchestratorData("", PaymentOption.Pix),
      {
        wrapper: Wrapper,
      }
    );

    expect(result.current.isFetching).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(theBankService.fetchOrchestratorData).not.toHaveBeenCalled();
  });
});
