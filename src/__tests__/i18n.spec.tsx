import { footerMessages } from "./datasets";

jest.mock("next/navigation", () => ({
  notFound: jest.fn(),
}));

jest.mock("next-intl/server", () => ({
  getRequestConfig: jest.fn(config => config),
}));

describe("i18n config", () => {
  it("should return messages for a valid locale", async () => {
    const locale = "en-US";
    const config = (await import("@/i18n")).default;
    const result = await config({
      locale,
      requestLocale: Promise.resolve(locale),
    });

    if (
      typeof result?.messages?.home === "object" &&
      result.messages.home !== null
    ) {
      expect(result.messages.home.purchase_disclaimer).toBe(
        footerMessages[1][1].home.purchase_disclaimer
      );
    } else {
      throw new Error("Expected result.messages.home to be an object");
    }
  });

  it("should import the correct messages file", async () => {
    const locale = "pt-BR";
    const config = (await import("@/i18n")).default;
    const result = await config({
      locale,
      requestLocale: Promise.resolve(locale),
    });

    expect(result.messages).toEqual(expect.any(Object));
  });
});
