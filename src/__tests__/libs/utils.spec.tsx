import { cn } from "@/lib/utils";

describe("cn", () => {
  it("should merge class names correctly", () => {
    const result = cn("class1", "class2");
    expect(result).toBe("class1 class2");
  });

  it("should handle arrays of class names", () => {
    const result = cn(["class1", "class2"], "class3");
    expect(result).toBe("class1 class2 class3");
  });

  it("should handle objects with conditional class names", () => {
    const result = cn({ class1: true, class2: false }, "class3");
    expect(result).toBe("class1 class3");
  });
});
