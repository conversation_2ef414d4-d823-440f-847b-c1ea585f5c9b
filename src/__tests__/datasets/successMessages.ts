import { Locales } from "@/types/locales";

export interface SuccessMessages {
  success: {
    title: string;
    description: string;
    product_order: string;
    payment_info: string;
    payment_info_value: string;
    access_product: string;
    help_page: string;
    help_page_link: string;
    payment_info_success: string;
  };
}

export type SuccessMessagesDataset = [Locales, SuccessMessages][];

export const successMessages: SuccessMessagesDataset = [
  [
    Locales.EN_US,
    {
      success: {
        title: "Congratulations on your purchase!",
        description:
          "The transaction was successfully completed and you can now access your product with the information we sent to {email}. Happy studying!",
        product_order: "Order:",
        payment_info: "Payment Method",
        payment_info_value: "Amount",
        access_product: "Access my product",
        help_page:
          "If you need help with this purchase, please contact the creator or",
        help_page_link: "visit our help page.",
        payment_info_success: "Pix to view",
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      success: {
        title: "Parabéns por sua compra!",
        description:
          "A transação foi concluída com sucesso e você já pode acessar o seu produto com os dados que enviamos para <bold>{email}</bold>. Bons estudos!",
        product_order: "Pedido:",
        payment_info: "Forma de Pagamento",
        payment_info_value: "Valor",
        access_product: "Acessar meu produto",
        help_page:
          "Se precisar de ajuda com essa compra, entre em contato com o criador ou",
        help_page_link: "acesse nossa página de ajuda.",
        payment_info_success: "Pix á vista",
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      success: {
        title: "¡Felicitaciones por tu compra!",
        description:
          "La transacción se ha completado con éxito y ahora puedes acceder a tu producto con la información que enviamos a {email}. ¡Feliz estudio!",
        product_order: "Pedido:",
        payment_info: "Método de Pago",
        payment_info_value: "Monto",
        access_product: "Acceder a mi producto",
        help_page:
          "Si necesitas ayuda con esta compra, ponte en contacto con el creador o",
        help_page_link: "visita nuestra página de ayuda.",
        payment_info_success: "Pix para ver",
      },
    },
  ],
];
