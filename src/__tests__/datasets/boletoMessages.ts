import { Locales } from "@/types/locales";

export interface BoletoMessages {
  boleto_page: {
    payment_boleto: string;
    copy_boleto_code: string;
    change_payment_method: string;
    order_confirmation: string;
    due_date: string;
    amount: string;
    boleto_code: string;
    view_boleto: string;
    approval_delay: string;
    copied_text: string;
  };
}

export type BoletoMessagesDataset = [Locales, BoletoMessages][];

export const boletoMessages: BoletoMessagesDataset = [
  [
    Locales.EN_US,
    {
      boleto_page: {
        payment_boleto: "Make payment with your boleto",
        copy_boleto_code: "Copy boleto code",
        change_payment_method: "Change payment method",
        order_confirmation:
          "Your order has been placed and will be processed after payment approval. After that, you will receive access data in {email}.",
        due_date: "Due date",
        amount: "Amount",
        boleto_code: "Boleto code",
        view_boleto: "View boleto",
        approval_delay: "Approval can take up to 3 business days to occur",
        copied_text: "Code copied!",
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      boleto_page: {
        payment_boleto: "Faça o pagamento com seu boleto",
        copy_boleto_code: "Copiar código do boleto",
        change_payment_method: "Alterar forma de pagamento",
        order_confirmation:
          "Sua encomenda foi realizada e será processada após a aprovação do pagamento. Após isso, você receberá os dados de acesso em {email}.",
        due_date: "Data de vencimento",
        amount: "Valor",
        boleto_code: "Código do boleto",
        view_boleto: "Ver boleto",
        approval_delay: "A aprovação pode levar até 3 dias úteis para ocorrer",
        copied_text: "Código copiado!",
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      boleto_page: {
        payment_boleto: "Realizar pago con tu boleto",
        copy_boleto_code: "Copiar código de boleto",
        change_payment_method: "Cambiar método de pago",
        order_confirmation:
          "Su pedido ha sido realizado y será procesado después de la aprobación del pago. Después de eso, recibirás los datos de acceso en {email}.",
        due_date: "Fecha de vencimiento",
        amount: "Monto",
        boleto_code: "Código de boleto",
        view_boleto: "Ver boleto",
        approval_delay:
          "La aprobación puede tardar hasta 3 días hábiles en procesarse",
        copied_text: "¡Código copiado!",
      },
    },
  ],
];
