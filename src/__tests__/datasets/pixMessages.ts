import { Locales } from "@/types/locales";

export interface PixMessages {
  pix_page: {
    change_payment_method: string;
    copy_pix_code: string;
    finalize_with_pix: string;
    cant_finalize_payment: string;
    access_help_page: string;
    payment_instructions: string;
    pix_app_instruction_1: string;
    pix_app_instruction_2: string;
    pix_app_instruction_3: string;
    amount: string;
    product: string;
    author: string;
    qr_code_instruction: string;
    order_success_message: string;
    subscription_success_message: string;
    approval_time: string;
    copy_code_instruction: string;
    copied_text: string;
    additional_items: string;
  };
}

export type PixMessagesDataset = [Locales, PixMessages][];

export const pixMessages: PixMessagesDataset = [
  [
    Locales.EN_US,
    {
      pix_page: {
        change_payment_method: "Change payment method",
        copy_pix_code: "Copy Pix code",
        finalize_with_pix: "Finalize with Pix",
        cant_finalize_payment: "Can't finalize payment?",
        access_help_page: "Access our help page.",
        payment_instructions: "Payment instructions",
        pix_app_instruction_1:
          "Enter your bank's app and access the Pix environment;",
        pix_app_instruction_2: "Choose the Read QR Code option;",
        pix_app_instruction_3: "Scan the QR Code and confirm the payment;",
        amount: "Amount:",
        product: "Your product:",
        author: "Author:",
        qr_code_instruction:
          "If you prefer, read the QR Code below in your bank's app:",
        order_success_message:
          "Your order has been made and, after finalizing the payment, you can access the content of your product.",
        subscription_success_message:
          "Your order has been made and, after finalizing the payment, you can continue accessing the content of your subscription.",
        approval_time: "The approval takes no more than 2 minutes",
        copy_code_instruction: "If you prefer, copy the code below:",
        copied_text: "Code copied",
        additional_items: "Additional items:",
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      pix_page: {
        change_payment_method: "Alterar forma de pagamento",
        copy_pix_code: "Copiar código Pix",
        finalize_with_pix: "Finalize o pagamento com Pix",
        cant_finalize_payment: "Não consegue finalizar o pagamento?",
        access_help_page: "Acesse nossa página de ajuda.",
        payment_instructions: "Instruções para o pagamento",
        pix_app_instruction_1:
          "Entre no aplicativo da sua instituição financeira e acesse o ambiente Pix;",
        pix_app_instruction_2: "Escolha a opção de Ler o QR Code;",
        pix_app_instruction_3: "Escaneie o QR Code e confirme o pagamento;",
        amount: "Valor:",
        product: "Seu produto:",
        author: "Autor:",
        qr_code_instruction:
          "Se preferir, leia o QR Code abaixo no app do seu banco:",
        order_success_message:
          "Seu pedido foi feito e, após finalizar o pagamento, você já pode acessar o conteúdo do seu produto.",
        subscription_success_message:
          "Seu pedido foi feito e, após finalizar o pagamento, você já pode continuar acessando o conteúdo da sua assinatura.",
        approval_time: "A aprovação leva no máximo 2 minutos",
        copy_code_instruction: "Se preferir, copie o código abaixo:",
        copied_text: "Código copiado",
        additional_items: "Itens adicionais:",
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      pix_page: {
        change_payment_method: "Cambiar el método de pago",
        copy_pix_code: "Copiar código Pix",
        finalize_with_pix: "Finalizar con Pix",
        cant_finalize_payment: "¿No puede finalizar el pago?",
        access_help_page: "Acceda a nuestra página de ayuda.",
        payment_instructions: "Instrucciones de pago",
        pix_app_instruction_1:
          "Ingrese a su aplicación bancaria y acceda al entorno Pix;",
        pix_app_instruction_2: "Elija la opción de Leer el código QR;",
        pix_app_instruction_3: "Escanee el código QR y confirme el pago;",
        amount: "Cantidad:",
        product: "Su producto:",
        author: "Autor:",
        qr_code_instruction:
          "Si prefiere, lea el código QR a continuación en la aplicación de su banco:",
        order_success_message:
          "Su pedido se ha realizado y, después de finalizar el pago, puede acceder al contenido de su producto.",
        subscription_success_message:
          "Su pedido se ha realizado y, después de finalizar el pago, puede continuar accediendo al contenido de su suscripción.",
        approval_time: "La aprobación tarda no más de 2 minutos",
        copy_code_instruction: "Si prefiere, copie el código a continuación:",
        copied_text: "Código copiado",
        additional_items: "Elementos adicionales:",
      },
    },
  ],
];
