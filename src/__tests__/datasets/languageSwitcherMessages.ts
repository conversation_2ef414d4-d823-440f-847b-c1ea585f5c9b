import { Locales } from "@/types/locales";

export interface LanguageSwitcherMessages {
  language_switcher: {
    english: string;
    portuguese: string;
    spanish: string;
    [key: string]: string;
  };
}

export type LanguageSwitcherMessagesDataset = [
  Locales,
  LanguageSwitcherMessages,
][];

export const languageSwitcherMessages: LanguageSwitcherMessagesDataset = [
  [
    Locales.EN_US,
    {
      language_switcher: {
        english: "English",
        portuguese: "Portuguese",
        spanish: "Spanish",
        "pt-BR": "Portuguese",
        "es-ES": "Spanish",
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      language_switcher: {
        english: "Inglês",
        portuguese: "Português",
        spanish: "Espanhol",
        "en-US": "Inglês",
        "es-ES": "Espanhol",
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      language_switcher: {
        english: "Inglés",
        portuguese: "Portugués",
        spanish: "Español",
        "en-US": "Inglés",
        "pt-BR": "Portugués",
      },
    },
  ],
];
