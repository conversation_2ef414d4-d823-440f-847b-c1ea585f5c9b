export const validCNPJs = [
  "12.345.678/0001-95",
  "12.345.678/0001-95",
  "11.222.333/0001-81",
  "13.913.372/0001-50",
  "02.600.862/0001-80",
];

export const invalidFormatCNPJs = [
  "12.345.678/0001",
  "99.999.999/0000",
  "00.000.000/0000-00",
  "1234.5678/0001-95",
  "12.3456780001-95",
];

export const repeatedSequenceCNPJs = [
  "11.111.111/1111-11",
  "22.222.222/2222-22",
  "33.333.333/3333-33",
  "44.444.444/4444-44",
  "55.555.555/5555-55",
];

export const invalidCheckDigitsCNPJs = [
  "12.345.678/0001-99",
  "11.222.333/0001-12",
  "99.888.777/0001-45",
  "71.569.525/0001-02",
  "45.932.149/0001-78",
];

export const validCNPJsWithoutFormatting = [
  "12345678000195",
  "12345678000195",
  "11222333000181",
  "85702322000135",
  "85702322000135",
];
