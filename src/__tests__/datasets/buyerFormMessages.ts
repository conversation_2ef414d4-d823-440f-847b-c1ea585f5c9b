import { Locales } from "@/types/locales";

export interface BuyerFormMessages {
  home: {
    info_purchase: string;
    purchase_disclaimer: string;
    privacy_policy: string;
    and: string;
    terms_of_use: string;
    help_text: string;
    help_page: string;
    all_rights_reserved: string;
    purchase_disclaimer_credit_card: string;
    card_brand_not_supported: string;
    card_brand_not_supported_one_time: string;
    country_not_supported: string;
  };
  buyer_form: {
    author: string;
    at_view: string;
    or: string;
    submit_button_subscription: string;
    purchase_summary: string;
    group_buy: string;
    add_product: string;
    remove_product: string;
    instalment_required: string;
    full_name: string;
    full_name_error: string;
    full_name_max_leght: string;
    email: string;
    confirm_email: string;
    cpf_or_cnpj: string;
    phone_number: string;
    submit_button: string;
    pix_generate: string;
    generate_boleto: string;
    name_placeholder: string;
    email_placeholder: string;
    confirm_email_placeholder: string;
    document_placeholder: string;
    phone_placeholder: string;
    email_mismatch: string;
    invalid_email: string;
    invalid_document: string;
    invalid_phone: string;
    required_field: string;
    dont_have_brazilianDocument: string;
    card_number_placeholder: string;
    card_code_placeholder: string;
    select_month: string;
    select_year: string;
    enter_email: string;
    document_error: string;
    phone_error: string;
    card_number_max_length: string;
    card: string;
    card_number: string;
    owner_card: string;
    month: string;
    year: string;
    generate_pix: string;
    cvv_card: string;
    cvv_help: string;
    invalid_card_number: string;
    cvv_card_min: string;
    cvv_card_max: string;
    credit_card: string;
    pix: string;
    boleto: string;
    installment: string;
    select_installment: string;
    amount_to_be_paid: string;
    safe_buy: string;
    data_protection_card_security: string;
    pay_until_due_date: string;
    payment_processing_time: string;
    immediate_approval: string;
    secure_transaction_bcb: string;
    easy_payment_via_bank_app: string;
    tokenization_error_title: string;
    tokenization_error_description: string;
    seller_registration_error_title: string;
    seller_registration_error_description: string;
    understood: string;
    cep_error: string;
    cep_max_length: string;
    valid_cep: string;
    cep_exact_length: string;
    street_error: string;
    street_max_length: string;
    district_error: string;
    district_max_length: string;
    complement_max_length: string;
    city_error: string;
    city_max_length: string;
    select_state: string;
    street: string;
    district: string;
    number: string;
    complement: string;
    city: string;
    state: string;
    street_placeholder: string;
    district_placeholder: string;
    number_placeholder: string;
    complement_placeholder: string;
    city_placeholder: string;
    state_placeholder: string;
    without_number: string;
    without_number_error: string;
    free_trial: string;
    subscription: {
      every: string;
      renews_every: string;
      day: string;
      days: string;
      month: string;
      months: string;
      year: string;
      years: string;
    };
  };
}

export type BuyerFormMessagesDataset = [Locales, BuyerFormMessages][];

export const buyerFormMessages: BuyerFormMessagesDataset = [
  [
    Locales.EN_US,
    {
      home: {
        info_purchase: "*The value is split into installments.",
        purchase_disclaimer:
          "By completing the purchase, you declare that you have read and agree (i) that TheMembers checkout is processing this order on behalf of {organization} and has no responsibility for the content and/or prior control of it; (ii) with our",
        privacy_policy: "privacy policy",
        and: "and with the",
        terms_of_use: "terms of use",
        help_text: "Can't complete this purchase?",
        help_page: "Visit our help page",
        all_rights_reserved: "All rights reserved",
        purchase_disclaimer_credit_card:
          "The value is split into installments.",
        card_brand_not_supported:
          "Card brand not supported for subscription. Use a different one.",
        card_brand_not_supported_one_time:
          "Card brand not supported. Use a different one.",
        country_not_supported: "No country found",
      },
      buyer_form: {
        author: "Author:",
        at_view: "at view",
        or: "or",
        submit_button_subscription: "Subscribe now",
        purchase_summary: "Purchase Summary",
        group_buy: "Take advantage and buy together:",
        add_product: "Add product",
        remove_product: "Remove product",
        instalment_required: "Select the instalment",
        full_name: "Full Name",
        full_name_error: "Enter your full name",
        full_name_max_leght: "Maximum 256 characters",
        email: "Email",
        confirm_email: "Confirm Email",
        cpf_or_cnpj: "CPF or CNPJ",
        phone_number: "Phone Number",
        submit_button: "Buy Now",
        pix_generate: "Generate Pix",
        generate_boleto: "Generate Boleto",
        name_placeholder: "Ex: John Doe",
        email_placeholder: "Ex: <EMAIL>",
        confirm_email_placeholder: "Ex: <EMAIL>",
        document_placeholder: "Enter the document",
        phone_placeholder: "(00) 90000-0000",
        email_mismatch: "The emails do not match",
        invalid_email: "Report or email again",
        invalid_document: "Enter a valid document",
        invalid_phone: "Enter a valid phone number",
        required_field: "This field is required",
        dont_have_brazilianDocument: "I don't have a Brazilian document",
        card_number_placeholder: "Enter the card number",
        card_code_placeholder: "Enter the code",
        select_month: "Select the month",
        select_year: "Select the year",
        enter_email: "Enter an email",
        document_error: "Enter the document number",
        phone_error: "Provide a cell phone number",
        card_number_max_length: "Must have a maximum of 16 digits",
        card: "Card",
        card_number: "Card number",
        owner_card: "Cardholder's name (as on the card)",
        month: "Month",
        year: "Year",
        generate_pix: "Generate Pix",
        cvv_card: "Card security code",
        cvv_help:
          "The card security code, known as CVV or CVC, is a sequence of 3or 4 digits printed on the card. It is present only on cards that support online purchases and helps to ensure the security of transactions.",
        invalid_card_number: "Invalid card number",
        cvv_card_min: "Minimum 3 digits",
        cvv_card_max: "Maximum 4 digits",
        credit_card: "Card",
        pix: "Pix",
        boleto: "Boleto",
        installment: "Installment",
        select_installment: "Select the installment",
        amount_to_be_paid: "Total to be paid:",
        safe_buy: "Safe Buy",
        data_protection_card_security:
          "We protect your card data using encryption to provide bank-level security",
        pay_until_due_date: "Pay until the due date to ensure your purchase",
        payment_processing_time:
          "It may take up to 3 business days for the payment to be processed",
        immediate_approval: "Immediate approval with a short processing time",
        secure_transaction_bcb:
          "Secure transaction guaranteed by the Central Bank",
        easy_payment_via_bank_app: "Pay easily through your bank's app",
        tokenization_error_title: "Unable to process payment",
        tokenization_error_description: "Try again in a few moments.",
        seller_registration_error_title: "Purchase not processed",
        seller_registration_error_description:
          "The author of this product has not yet completed the registration on our platform, so the purchase cannot be made.",
        understood: "Understood",
        cep_error: "Enter the ZIP code",
        cep_max_length: "The ZIP code must be at most 9 characters long",
        valid_cep: "Enter a valid ZIP code",
        cep_exact_length: "The ZIP code must be exactly 8 digits long",
        street_error: "Enter the street",
        street_max_length: "The street must be at most 256 characters long",
        district_error: "Enter the district",
        district_max_length: "The district must be at most 100 characters long",
        complement_max_length:
          "The complement must be at most 50 characters long",
        city_error: "Enter the city",
        city_max_length: "The city must be at most 100 characters long",
        select_state: "Select the state",
        street: "Street",
        district: "District",
        number: "Number",
        complement: "Complement",
        city: "City",
        state: "State",
        street_placeholder: "Enter the street",
        district_placeholder: "Enter the district",
        number_placeholder: "Enter the number",
        complement_placeholder: "House, Apartment",
        city_placeholder: "Enter the city",
        state_placeholder: "Select the state",
        without_number: "Without number",
        without_number_error: "Enter a number.",
        free_trial: "Free trial of {days} days",
        subscription: {
          every: "every",
          renews_every: "Renews every",
          day: "day",
          days: "days",
          month: "month",
          months: "months",
          year: "year",
          years: "years",
        },
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      home: {
        info_purchase: "*O valor parcelado possui acréscimo.",
        purchase_disclaimer:
          "Ao concluir a compra, você declara que leu e concorda (i) que o checkout TheMembers está processando este pedido em nome de {organization} e não possui responsabilidade pelo conteúdo e/ou faz controle prévio deste; (ii) com nossa",
        privacy_policy: "política de privacidade",
        and: "e com os",
        terms_of_use: "termos de uso",
        help_text: "Não consegue finalizar essa compra?",
        help_page: "Acesse nossa página de ajuda",
        all_rights_reserved: "Todos os direitos reservados",
        purchase_disclaimer_credit_card: "O valor parcelado possui acréscimo.",
        card_brand_not_supported:
          "Bandeira do cartão não suportada para assinatura. Use um diferente.",
        card_brand_not_supported_one_time:
          "Bandeira do cartão não suportada. Use um diferente.",
        country_not_supported: "Nenhum país encontrado",
      },
      buyer_form: {
        author: "Autor:",
        at_view: "à vista",
        or: "ou",
        submit_button_subscription: "Assinar agora",
        purchase_summary: "Resumo da compra",
        group_buy: "Aproveite e compre junto:",
        add_product: "Adicionar produto",
        remove_product: "Remover produto",
        instalment_required: "Selecione o parcelamento",
        full_name: "Nome completo",
        full_name_error: "Informe seu nome completo",
        full_name_max_leght: "Maximo de 256 caracteres",
        email: "E-mail",
        confirm_email: "Confirmar o e-mail",
        cpf_or_cnpj: "CPF ou CNPJ",
        phone_number: "Celular",
        submit_button: "Comprar agora",
        generate_pix: "Gerar Pix",
        generate_boleto: "Gerar Boleto",
        name_placeholder: "Ex: João da Silva",
        email_placeholder: "Ex: <EMAIL>",
        confirm_email_placeholder: "Ex: <EMAIL>",
        document_placeholder: "Informe o documento",
        phone_placeholder: "(00) 90000-0000",
        email_mismatch: "Os emails não correspondem",
        invalid_email: "Informe o e-mail novamente",
        invalid_document: "Informe um documento válido",
        invalid_phone: "Informe um número válido",
        required_field: "Este campo é obrigatório",
        dont_have_brazilianDocument: "Não tenho um documento brasileiro",
        card_number_placeholder: "Informe o número do cartão",
        card_code_placeholder: "Informe o código",
        select_month: "Selecione o mês",
        select_year: "Selecione o ano",
        enter_email: "Informe um e-mail",
        document_error: "Informe o número do documento",
        phone_error: "Informe um número de celular",
        card_number_max_length: "Deve ter no máximo 16 dígitos",
        card: "Cartão",
        card_number: "Número do cartão",
        owner_card: "Nome do titular (como no cartão)",
        month: "Mês",
        year: "Ano",
        cvv_card: "Código de segurança",
        cvv_help:
          "O código de segurança do cartão, conhecido como CVV ou CVC, é uma sequência de 3ou 4 dígitos impressa no cartão. Ele está presente somente em cartões habilitados paracompras online e ajuda a garantir a segurança das transações.",
        invalid_card_number: "Número de cartão inválido",
        cvv_card_min: "Mínimo 3 digitos",
        cvv_card_max: "Máximo 4 digitos",
        credit_card: "Cartão",
        pix: "Pix",
        boleto: "Boleto",
        installment: "Parcelamento",
        select_installment: "Selecione o parcelamento",
        amount_to_be_paid: "Total a ser pago:",
        safe_buy: "Compra 100% segura",
        data_protection_card_security:
          "Nós protegemos seus dados de cartão usando encriptação para prover segurança no nível de bancos",
        pay_until_due_date: "Pague até o vencimento para garantir sua compra",
        payment_processing_time:
          "Pode levar até 3 dias úteis para o pagamento ser compensado",
        immediate_approval:
          "Aprovação imediata com pouco tempo de processamento",
        secure_transaction_bcb: "Transação segura garantida pelo Banco Central",
        easy_payment_via_bank_app:
          "Pague com facilidade pelo aplicativo do seu banco",
        tokenization_error_title: "Não foi possível processar o pagamento",
        tokenization_error_description: "Tente novamente em alguns instantes.",
        seller_registration_error_title: "Compra não processada",
        seller_registration_error_description:
          "O autor desse produto ainda não concluiu o cadastro na nossa plataforma, por isso, a compra não pode ser realizada.",
        understood: "Entendi",
        cep_error: "Informe o CEP",
        cep_max_length: "O CEP deve ter no máximo 9 caracteres",
        valid_cep: "Informe um CEP válido",
        cep_exact_length: "O CEP deve ter exatamente 8 dígitos",
        street_error: "Informe o logradouro",
        street_max_length: "O logradouro deve ter no máximo 256 caracteres",
        district_error: "Informe o bairro",
        district_max_length: "O bairro deve ter no máximo 100 caracteres",
        complement_max_length: "O complemento deve ter no máximo 50 caracteres",
        city_error: "Informe a cidade",
        city_max_length: "A cidade deve ter no máximo 100 caracteres",
        select_state: "Selecione o estado",
        street: "Logradouro",
        district: "Bairro",
        number: "Número",
        complement: "Complemento",
        city: "Cidade",
        state: "Estado",
        street_placeholder: "Digite o logradouro",
        district_placeholder: "Digite o bairro",
        number_placeholder: "Digite o número",
        complement_placeholder: "Casa, Apto",
        city_placeholder: "Digite a cidade",
        state_placeholder: "Selecione o estado",
        without_number: "Sem número",
        without_number_error: "Informe um número.",
        pix_generate: "Gerar Pix",
        free_trial: "Teste gratuito de {days} dias",
        subscription: {
          every: "a cada",
          renews_every: "Renova a cada",
          day: "dia",
          days: "dias",
          month: "mês",
          months: "meses",
          year: "ano",
          years: "anos",
        },
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      home: {
        info_purchase: "*El valor se divide en cuotas.",
        purchase_disclaimer:
          "Al completar la compra, usted declara que ha leído y acepta (i) que el checkout TheMembers está procesando este pedido en nombre de {organization} y no tiene responsabilidad por el contenido y/o control previo del mismo; (ii) con nuestra",
        privacy_policy: "política de privacidad",
        and: "y con los",
        terms_of_use: "términos de uso",
        help_text: "¿No puede completar esta compra?",
        help_page: "Visite nuestra página de ayuda",
        all_rights_reserved: "Todos los derechos reservados",
        purchase_disclaimer_credit_card: "El valor se divide en cuotas.",
        card_brand_not_supported:
          "Bandeira do cartão não suportada para assinatura. Use um diferente.",
        card_brand_not_supported_one_time:
          "Bandeira do cartão não suportada. Use um diferente.",
        country_not_supported: "No se encontró ningún país",
      },
      buyer_form: {
        author: "Autor:",
        at_view: "a vista",
        or: "o",
        submit_button_subscription: "Suscríbase ahora",
        required_field: "Este campo es requerido",
        purchase_summary: "Resumen de la compra",
        group_buy: "Aprovecha y compra junto:",
        add_product: "Añadir producto",
        remove_product: "Eliminar producto",
        instalment_required: "Seleccione el parcelamiento",
        full_name: "Nombre completo",
        full_name_error: "Informe su nombre completo",
        full_name_max_leght: "Máximo 256 caracteres",
        email: "Correo electrónico",
        confirm_email: "Confirmar correo electrónico",
        cpf_or_cnpj: "CPF o CNPJ",
        phone_number: "Número de teléfono",
        submit_button: "Comprar ahora",
        generate_pix: "Generar Pix",
        generate_boleto: "Generar Boleto",
        name_placeholder: "Ej: Juan Pérez",
        email_placeholder: "Ej: <EMAIL>",
        confirm_email_placeholder: "Ej: <EMAIL>",
        document_placeholder: "Introduzca el documento",
        phone_placeholder: "(00) 90000-0000",
        email_mismatch: "Los correos electrónicos no coinciden",
        invalid_email: "Informar o enviar un correo electrónico nuevamente",
        invalid_document: "Introduzca un documento válido",
        card_number_placeholder: "Ingrese el número de la tarjeta",
        card_code_placeholder: "Ingrese el código",
        dont_have_brazilianDocument: "No tengo un documento brasileño",
        select_month: "Seleccione el mes",
        select_year: "Seleccione el año",
        enter_email: "Ingrese un correo electrónico",
        document_error: "Ingrese el número de documento",
        phone_error: "Ingrese un número de teléfono",
        card_number_max_length: "Debe tener un máximo de 16 dígitos",
        card: "Tarjeta",
        card_number: "Número de tarjeta",
        owner_card:
          "Nombre del titular de la tarjeta (como aparece en la tarjeta)",
        month: "Mes",
        year: "Año",
        pix_generate: "Generar Pix",
        cvv_card: "Código de seguridad",
        cvv_help:
          "El código de seguridad de la tarjeta, conocido como CVV o CVC, es una secuencia de 3o 4 dígitos impresas en la tarjeta. Solo está presente en tarjetas habilitadas para compras en línea y ayuda a garantizar la seguridad de las transacciones.",
        invalid_card_number: "Número de tarjeta inválido",
        cvv_card_min: "Mínimo de 3 dígitos",
        cvv_card_max: "Máximo de 4 dígitos",
        credit_card: "Tarjeta",
        pix: "Pix",
        boleto: "Boleto",
        installment: "Entrega",
        select_installment: "Seleccione la entrega",
        amount_to_be_paid: "Total a pagar:",
        safe_buy: "Compra 100% segura",
        data_protection_card_security:
          "Protegemos los datos de su tarjeta utilizando encriptación para proporcionar seguridad a nivel bancario",
        pay_until_due_date:
          "Pague hasta la fecha de vencimiento para asegurar su compra",
        payment_processing_time:
          "El pago puede tardar hasta 3 días hábiles en procesarse",
        immediate_approval:
          "Aprobación inmediata con un tiempo de procesamiento corto",
        secure_transaction_bcb:
          "Transacción segura garantizada por el Banco Central",
        easy_payment_via_bank_app:
          "Pague fácilmente a través de la aplicación de su banco",
        tokenization_error_title: "No se pudo procesar el pago",
        tokenization_error_description:
          "Vuelva a intentarlo en unos instantes.",
        seller_registration_error_title: "Compra no procesada",
        seller_registration_error_description:
          "El autor de este producto aún no ha completado el registro en nuestra plataforma, por lo que la compra no puede ser realizada.",
        understood: "Entendido",
        cep_error: "Ingrese el código postal",
        cep_max_length: "El código postal debe tener como máximo 9 caracteres",
        valid_cep: "Ingrese un código postal válido",
        cep_exact_length: "El código postal debe tener exactamente 8 dígitos",
        street_error: "Ingrese la calle",
        street_max_length: "La calle debe tener como máximo 256 caracteres",
        district_error: "Ingrese el barrio",
        district_max_length: "El barrio debe tener como máximo 100 caracteres",
        complement_max_length:
          "El complemento debe tener como máximo 50 caracteres",
        city_error: "Ingrese la ciudad",
        city_max_length: "La ciudad debe tener como máximo 100 caracteres",
        select_state: "Seleccione el estado",
        street: "Calle",
        district: "Barrio",
        number: "Número",
        complement: "Complemento",
        city: "Ciudad",
        state: "Estado",
        street_placeholder: "Ingrese la calle",
        district_placeholder: "Ingrese el barrio",
        number_placeholder: "Ingrese el número",
        complement_placeholder: "Casa, Apartamento",
        city_placeholder: "Ingrese la ciudad",
        state_placeholder: "Seleccione el estado",
        without_number: "Sin número",
        without_number_error: "Ingrese un número.",
        invalid_phone: "Ingrese un número de teléfono válido",
        free_trial: "Prueba gratuita de {days} días",
        subscription: {
          every: "cada",
          renews_every: "Renova cada",
          day: "día",
          days: "días",
          month: "mes",
          months: "meses",
          year: "año",
          years: "años",
        },
      },
    },
  ],
];
