import { Locales } from "@/types/locales";

export interface CreditCardMessages {
  credit_card_page: {
    header: string;
    description: string;
    order: string;
    payment_method: string;
    value: string;
    error: {
      title: string;
      description: string;
      button: string;
    };
  };
}

export type CreditCardMessagesDataset = [Locales, CreditCardMessages][];

export const creditCardMessages: CreditCardMessagesDataset = [
  [
    Locales.PT_BR,
    {
      credit_card_page: {
        header: "Seu pagamento está quase lá...",
        description:
          "A compra já está sendo processada e pode levar alguns instantes, mas assim que ela for aprovada nós vamos te avisar.",
        order: "Pedido:",
        payment_method: "Forma de pagamento",
        value: "Valor",
        error: {
          title: "Não foi possível processar o pagamento",
          description:
            "Você pode voltar à página de compra e tentar novamente. Se o erro continuar, entre em contato com a central do cartão para resolver.",
          button: "Voltar à página de compra",
        },
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      credit_card_page: {
        header: "Tu pago está casi listo...",
        description:
          "Tu compra se está procesando y puede tardar unos instantes, pero te avisaremos en cuanto sea aprobada.",
        order: "Pedido:",
        payment_method: "Forma de pago",
        value: "Valor",
        error: {
          title: "No se pudo procesar el pago",
          description:
            "Puedes volver a la página de compra e intentarlo nuevamente. Si el error persiste, contacta con el centro de atención de tu tarjeta.",
          button: "Volver a la página de compra",
        },
      },
    },
  ],
  [
    Locales.EN_US,
    {
      credit_card_page: {
        header: "Your payment is almost there...",
        description:
          "Your purchase is being processed and may take a few moments, but we'll notify you as soon as it's approved.",
        order: "Order:",
        payment_method: "Payment method",
        value: "Amount",
        error: {
          title: "We couldn't process your payment",
          description:
            "You can go back to the purchase page and try again. If the issue persists, contact your card's support center for help.",
          button: "Back to purchase page",
        },
      },
    },
  ],
];
