import { Locales } from "@/types/locales";

export interface PhoneInputMessages {
  buyer_form: {
    phone_number: string;
  };
}

export type PhoneInputMessagesDataset = [Locales, PhoneInputMessages][];

export const phoneInputMessages: PhoneInputMessagesDataset = [
  [
    Locales.EN_US,
    {
      buyer_form: {
        phone_number: "Phone Number",
      },
    },
  ],
  [
    Locales.PT_BR,
    {
      buyer_form: {
        phone_number: "Celular",
      },
    },
  ],
  [
    Locales.ES_ES,
    {
      buyer_form: {
        phone_number: "Número de teléfono",
      },
    },
  ],
];
