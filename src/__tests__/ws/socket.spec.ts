import Echo from "@ably/laravel-echo";
import * as Ably from "ably";

import { createSocketConnection } from "@/ws/socket";

jest.mock("@ably/laravel-echo");
jest.mock("ably");

describe("createSocketConnection", () => {
  it("should initialize Echo and Ably if not already defined", () => {
    process.env.NEXT_PUBLIC_ABLY_KEY = "test-ably-key";
    process.env.NEXT_PUBLIC_BASE_URL = "http://example.com";

    createSocketConnection();

    expect(window.Ably).toStrictEqual(Ably);

    expect(Echo).toHaveBeenCalledWith({
      broadcaster: "ably",
      key: "test-ably-key",
      authEndpoint: "http://example.com/broadcasting/auth",
    });
  });

  it("should not reinitialize Echo and Ably if already defined", () => {
    const mockEcho = {} as Echo;
    const mockAbly = {} as typeof Ably;
    window.Echo = mockEcho;
    window.Ably = mockAbly;

    createSocketConnection();

    expect(window.Ably).toBe(mockAbly);
    expect(window.Echo).toBe(mockEcho);
  });
});
