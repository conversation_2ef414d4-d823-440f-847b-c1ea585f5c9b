import * as Ably from "ably";
import Echo from "laravel-echo";

import {
  createSocketConnection,
  isSocketReady,
  resetSocketState,
} from "@/ws/socket";

jest.mock("laravel-echo");
jest.mock("ably");

describe("createSocketConnection", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    resetSocketState();
  });

  afterEach(() => {
    resetSocketState();
  });

  it("should initialize Echo and Ably if not already defined", async () => {
    process.env.NEXT_PUBLIC_ABLY_KEY = "test-ably-key";
    process.env.NEXT_PUBLIC_BASE_URL = "http://example.com";
    process.env.NEXT_PUBLIC_WEBSOCKET_PROVIDER = "ably";

    await createSocketConnection();

    expect(window.Ably).toStrictEqual(Ably);
    expect(Echo).toHaveBeenCalledWith({
      broadcaster: "ably",
      key: "test-ably-key",
      authEndpoint: "http://example.com/broadcasting/auth",
    });
    expect(isSocketReady()).toBe(true);
  });

  it("should not reinitialize Echo and Ably if already defined", async () => {
    process.env.NEXT_PUBLIC_ABLY_KEY = "test-ably-key";
    process.env.NEXT_PUBLIC_BASE_URL = "http://example.com";
    process.env.NEXT_PUBLIC_WEBSOCKET_PROVIDER = "ably";

    // Primeira inicialização
    await createSocketConnection();

    const firstEcho = window.Echo;
    const firstAbly = window.Ably;

    // Segunda chamada deve retornar imediatamente sem reinicializar
    await createSocketConnection();

    expect(window.Ably).toBe(firstAbly);
    expect(window.Echo).toBe(firstEcho);
    expect(isSocketReady()).toBe(true);

    // Echo deve ter sido chamado apenas uma vez
    expect(Echo).toHaveBeenCalledTimes(1);
  });

  it("should handle multiple concurrent initialization calls", async () => {
    process.env.NEXT_PUBLIC_ABLY_KEY = "test-ably-key";
    process.env.NEXT_PUBLIC_BASE_URL = "http://example.com";
    process.env.NEXT_PUBLIC_WEBSOCKET_PROVIDER = "ably";

    // Múltiplas chamadas simultâneas
    const promises = [
      createSocketConnection(),
      createSocketConnection(),
      createSocketConnection(),
    ];

    await Promise.all(promises);

    // Echo deve ser chamado apenas uma vez
    expect(Echo).toHaveBeenCalledTimes(1);
    expect(isSocketReady()).toBe(true);
  });
});
