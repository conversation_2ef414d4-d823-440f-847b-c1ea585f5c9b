import { NextResponse } from "next/server";

/* eslint-disable @typescript-eslint/no-var-requires */
import middleware from "@/middleware";
import { determineLocale } from "@/support/lang/strategy";

jest.mock("next/server", () => ({
  NextResponse: {
    redirect: jest.fn().mockReturnValue("redirected"),
    next: jest.fn().mockReturnValue("next"),
  },
}));

jest.mock("@/support/lang/strategy", () => ({
  determineLocale: jest.fn(),
}));

describe("Middleware", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test.each([["pt-BR"], ["es-US"], ["es-ES"]])(
    "should redirect based on the accept-language header when no locale is in the URL",
    acceptLanguage => {
      (determineLocale as jest.Mock).mockReturnValue(acceptLanguage);
      const getMock = jest.fn(() => acceptLanguage);

      jest.mock("next-intl/middleware", () => ({
        __esModule: true,
        default: jest.fn(() => jest.fn(() => "redirected")),
      }));

      const requestMock = jest.fn().mockImplementation(() => ({
        headers: {
          get: getMock,
        },
        url: "http://localhost:3000/product",
      }));

      requestMock().headers.get("accept-language");
      const appMiddleware = require("@/middleware");

      const result = appMiddleware.default(requestMock());

      expect(getMock).toHaveBeenCalledWith("accept-language");
      expect(result).toBe("redirected");
      expect(NextResponse.redirect).toHaveBeenCalledWith(
        `http://localhost:3000/${acceptLanguage}/product`
      );
    }
  );

  it("should redirect to en-US locale when there is no locale in the header or url", () => {
    const getMock = jest.fn(() => null);

    const requestMock = jest.fn().mockImplementation(() => ({
      headers: {
        get: getMock,
      },
      url: "http://localhost:3000/product",
    }));

    middleware(requestMock());
    expect(getMock).toHaveBeenCalledWith("accept-language");
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      "http://localhost:3000/en-US/product"
    );
  });

  test.each([["pt-BR"], ["en-US"], ["es-ES"]])(
    "should call next when the locale is in the url",
    locale => {
      (determineLocale as jest.Mock).mockReturnValue(locale);
      const getMock = jest.fn(() => locale);

      jest.mock("next-intl/middleware", () => ({
        __esModule: true,
        default: jest.fn(() => jest.fn()),
      }));

      const requestMock = jest.fn().mockImplementation(() => ({
        headers: {
          get: getMock,
        },
        url: `http://localhost:3000/${locale}/product`,
      }));

      const appMiddleware = require("@/middleware");
      const result = appMiddleware.default(requestMock());
      expect(result).not.toBe("redirected");
    }
  );

  it("should redirect to locale from accept-language header if locale is not in URL", () => {
    const getMock = jest.fn(() => "pt-BR");
    (determineLocale as jest.Mock).mockReturnValue("pt-BR");
    const requestMock = jest.fn().mockImplementation(() => ({
      headers: {
        get: getMock,
      },
      url: "http://localhost:3000/product",
      nextUrl: {
        pathname: "/product",
      },
    }));

    const appMiddleware = require("@/middleware");
    appMiddleware.default(requestMock());

    expect(determineLocale).toHaveBeenCalled();
    expect(getMock).toHaveBeenCalledWith("accept-language");
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      "http://localhost:3000/pt-BR/product"
    );
  });

  test.each([["pt-BR"]])(
    "should call intlMiddleware when no language is provided",
    locale => {
      (determineLocale as jest.Mock).mockReturnValue(locale);
      const getMock = jest.fn(() => "en-US");

      const createMiddlewareMock = jest.fn(() => {
        return jest.fn();
      });

      jest.mock("next-intl/middleware", () => ({
        __esModule: true,
        default: jest.fn(() => createMiddlewareMock),
      }));

      const requestMock = jest.fn().mockImplementation(() => ({
        headers: {
          get: getMock,
        },
        url: `http://localhost:3000/${locale}/product`,
      }));

      const appMiddleware = require("@/middleware");
      const result = appMiddleware.default(requestMock());

      expect(result).toBe("next");
    }
  );

  test.each([["pt-PT", "pt-BR"]])(
    "should redirect to the correct locale when locale is not equal determineLocale",
    (locale, localeResult) => {
      const getMock = jest.fn(() => null);
      const requestMock = jest.fn().mockImplementation(() => ({
        headers: {
          get: getMock,
        },
        url: `http://localhost:3000/${locale}/product`,
      }));

      (determineLocale as jest.Mock).mockReturnValue(localeResult);

      const appMiddleware = require("@/middleware");
      const result = appMiddleware.default(requestMock());

      expect(NextResponse.redirect).toHaveBeenCalledWith(
        `http://localhost:3000/${localeResult}/product`
      );
      expect(result).toBe("redirected");
    }
  );
});
