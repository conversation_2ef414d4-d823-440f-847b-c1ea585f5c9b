import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen } from "@testing-library/react";
import { useSearchParams } from "next/navigation";
import { NextIntlClientProvider } from "next-intl";

import Success from "@/app/[locale]/(app)/success/page";
import { toBase64 } from "@/utils/stringUtils/enconding";

jest.mock("next/navigation", () => ({
  useSearchParams: jest.fn(),
}));

jest.mock("@/hooks/useGetProduct", () => ({
  useProduct: jest.fn(),
}));

jest.mock("next/script", () => {
  return function MockScript() {
    return null;
  };
});

const mockUseSearchParams = useSearchParams as jest.MockedFunction<
  typeof useSearchParams
>;
const mockUseProduct = require("@/hooks/useGetProduct")
  .useProduct as jest.MockedFunction<any>;

const mockMessages = {
  success: {
    title: "Parabéns por sua compra!",
    subscription_title: "<PERSON><PERSON><PERSON>s, sua assinatura começou!",
    subscription_trial_message:
      "A transação foi concluída e em alguns instantes você já pode acessar o seu produto com os dados que enviamos para <bold>{email}</bold>. Bons estudos!",
    description:
      "A transação foi concluída e em alguns instantes você já pode acessar o seu produto com os dados que enviamos para <bold>{email}</bold>. Bons estudos!",
    subscription_success_message:
      "A transação foi concluída e agora você já pode continuar acessando o conteúdo da sua assinatura. Bons estudos!",
    product_order: "Pedido:",
    payment_info: "Forma de Pagamento",
    payment_info_value: "Valor",
    access_product: "Acessar meu produto",
    help_page:
      "Se precisar de ajuda com essa compra, entre em contato com o criador ou",
    help_page_link: "acesse nossa página de ajuda.",
    subscription_help_message:
      "Essa compra inclui produtos por assinatura. Se o pagamento foi feito com cartão, você será avisado por e-mail um dia antes da renovação. Se tiver sido via Pix ou boleto, enviaremos um e-mail 3 dias antes com o link para acessar o QR Code ou o código de barras, que será válido por 4 dias. Assim que o pagamento for confirmado, sua assinatura estará garantida para o próximo período.",
    payment_info_success: "Pix á vista",
  },
};

const mockSuccessDataOrder = {
  order_id: "12345",
  payment_method: "pix",
  customer_email: "<EMAIL>",
  product: {
    id: "product-123",
    title: "Curso de React",
    description: "Aprenda React do zero",
    charge_type: "order",
    platform: {
      name: "Plataforma Teste",
      url: "https://plataforma.com",
    },
  },
  bumps: [],
  payment: {
    status: "approved",
    description: "R$ 99,90",
  },
};

const mockSuccessDataSubscriptionTrial = {
  order_id: "12345",
  payment_method: "credit_card",
  customer_email: "<EMAIL>",
  subscription_id: "sub-123",
  product: {
    id: "product-123",
    title: "Curso de React",
    description: "Aprenda React do zero",
    charge_type: "subscription",
    platform: {
      name: "Plataforma Teste",
      url: "https://plataforma.com",
    },
  },
  bumps: [],
  payment: {
    status: "approved",
    description: "R$ 99,90",
  },
};

const mockProductDataWithoutTrial = {
  id: "product-123",
  title: "Curso de React",
  charge_type: "subscription",
  subscription: {
    trial_days: null,
    periodicity: "monthly",
    interval: 1,
  },
  platform: {
    name: "Plataforma Teste",
    url: "https://plataforma.com",
  },
};

function renderWithProviders(component: React.ReactElement) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <NextIntlClientProvider locale="pt-BR" messages={mockMessages}>
        {component}
      </NextIntlClientProvider>
    </QueryClientProvider>
  );
}

describe("Success Page", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show regular title for normal orders", () => {
    const encodedData = toBase64(JSON.stringify(mockSuccessDataOrder));

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: null,
    });

    renderWithProviders(<Success />);

    expect(screen.getByText("Parabéns por sua compra!")).toBeInTheDocument();
  });

  it("should show subscription title for subscription with trial via subscription_id", () => {
    const mockSuccessDataWithTrialAndSubscriptionId = {
      ...mockSuccessDataSubscriptionTrial,
      product: {
        ...mockSuccessDataSubscriptionTrial.product,
        charge_type: "subscription",
        subscription: {
          periodicity: "monthly",
          interval: 1,
          trial_days: 7,
        },
      },
    };

    const encodedData = toBase64(
      JSON.stringify(mockSuccessDataWithTrialAndSubscriptionId)
    );

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: null,
    });

    renderWithProviders(<Success />);

    expect(
      screen.getByText("Parabéns, sua assinatura começou!")
    ).toBeInTheDocument();
  });

  it("should show subscription title for trialed payment status", () => {
    const mockSuccessDataTrialed = {
      ...mockSuccessDataOrder,
      payment: {
        ...mockSuccessDataOrder.payment,
        status: "trialed",
      },
      product: {
        ...mockSuccessDataOrder.product,
        charge_type: "subscription",
        subscription: {
          periodicity: "monthly",
          interval: 1,
          trial_days: 7,
        },
      },
    };

    const encodedData = toBase64(JSON.stringify(mockSuccessDataTrialed));

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: null,
    });

    renderWithProviders(<Success />);

    expect(
      screen.getByText("Parabéns, sua assinatura começou!")
    ).toBeInTheDocument();
  });

  it("should show regular subscription title for subscription without trial", () => {
    const encodedData = toBase64(
      JSON.stringify(mockSuccessDataSubscriptionTrial)
    );

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: mockProductDataWithoutTrial,
    });

    renderWithProviders(<Success />);

    expect(screen.getByText("Parabéns por sua compra!")).toBeInTheDocument();
  });

  it("should show trial message for subscription with trial", () => {
    const mockSuccessDataWithTrialMessage = {
      ...mockSuccessDataOrder,
      payment: {
        ...mockSuccessDataOrder.payment,
        status: "trialed",
      },
      product: {
        ...mockSuccessDataOrder.product,
        charge_type: "subscription",
        subscription: {
          periodicity: "monthly",
          interval: 1,
          trial_days: 7,
        },
      },
    };

    const encodedData = toBase64(
      JSON.stringify(mockSuccessDataWithTrialMessage)
    );

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: null,
    });

    renderWithProviders(<Success />);

    expect(
      screen.getByText(
        /A transação foi concluída e em alguns instantes você já pode acessar/
      )
    ).toBeInTheDocument();
    expect(screen.getByText(/<EMAIL>/)).toBeInTheDocument();
  });

  it("should show regular subscription message for subscription without trial", () => {
    const encodedData = toBase64(
      JSON.stringify(mockSuccessDataSubscriptionTrial)
    );

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: mockProductDataWithoutTrial,
    });

    renderWithProviders(<Success />);

    expect(
      screen.getByText(
        /A transação foi concluída e agora você já pode continuar acessando/
      )
    ).toBeInTheDocument();
  });

  it("should show subscription help message for subscription", () => {
    const mockSuccessDataSubscription = {
      ...mockSuccessDataOrder,
      subscription_id: "sub-123",
      product: {
        ...mockSuccessDataOrder.product,
        charge_type: "subscription",
      },
    };

    const encodedData = toBase64(JSON.stringify(mockSuccessDataSubscription));

    mockUseSearchParams.mockReturnValue({
      get: jest.fn().mockReturnValue(encodedData),
    } as any);

    mockUseProduct.mockReturnValue({
      data: null,
    });

    renderWithProviders(<Success />);

    expect(
      screen.getByText(/Essa compra inclui produtos por assinatura/)
    ).toBeInTheDocument();
    expect(
      screen.getByText(/Se o pagamento foi feito com cartão/)
    ).toBeInTheDocument();
  });
});
