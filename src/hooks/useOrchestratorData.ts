import { useQuery } from "@tanstack/react-query";

import { theBankService } from "@/services";
import { PaymentOption } from "@/types/paymentOptions";

export const useOrchestratorData = (
  malga: string,
  selectedOption: PaymentOption
) => {
  return useQuery({
    queryKey: ["orchestratorData", malga],
    queryFn: () => theBankService.fetchOrchestratorData(malga),
    enabled: !!malga && selectedOption === PaymentOption.CreditCard,
    refetchOnWindowFocus: false,
  });
};
