import { useCallback, useState } from "react";
import { UseFormSetValue, UseFormTrigger } from "react-hook-form";

import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { viaCEP } from "@/services/viaCEP";
import { State } from "@/types/locations";
export interface UseCepSearchProps {
  setValue: UseFormSetValue<Partial<BuyerFormValuesPartial>>;
  trigger: UseFormTrigger<Partial<BuyerFormValuesPartial>>;
  states: State[];
  setShowFields: (value: boolean) => void;
}

export function useCepSearch({
  setValue,
  trigger,
  setShowFields,
}: UseCepSearchProps) {
  const [isSearching, setIsLoading] = useState(false);

  const searchCep = useCallback(
    (cep: string) => {
      setIsLoading(true);
      viaCEP(cep, {
        onAddressFound: data => {
          setValue("billing_address.street", data.logradouro);
          setValue("billing_address.district", data.bairro);
          setValue("billing_address.city", data.localidade);
          setValue("billing_address.state", data.estado);
          trigger("billing_address.state");
          trigger([
            "billing_address.zipcode",
            "billing_address.street",
            "billing_address.district",
            "billing_address.city",
          ]);
          setShowFields(true);
          setIsLoading(false);
        },
        onError: () => {
          console.error("Erro ao comunicar com via cep");

          trigger([
            "billing_address.zipcode",
            "billing_address.street",
            "billing_address.district",
            "billing_address.city",
            "billing_address.state",
          ]);
          setShowFields(true);
          setIsLoading(false);
        },
        onAddressNotFound: () => {
          console.error("Cep não encontrado");
          trigger([
            "billing_address.zipcode",
            "billing_address.street",
            "billing_address.district",
            "billing_address.city",
            "billing_address.state",
          ]);
          setShowFields(true);
          setIsLoading(false);
        },
      });
    },
    [setValue, trigger]
  );

  return { searchCep, isSearching };
}
