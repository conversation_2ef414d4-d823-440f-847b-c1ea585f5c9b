import dayjs from "dayjs";
import { useMemo } from "react";

import { Product } from "@/types/product";

export const useTrialPeriod = (product: Product) => {
  const hasTrialDays = useMemo(() => {
    return (
      product?.subscription?.trial_days && product.subscription.trial_days > 0
    );
  }, [product?.subscription?.trial_days]);

  const firstChargeDate = useMemo(() => {
    if (!hasTrialDays || !product?.subscription?.trial_days) return null;

    const trialDays = product.subscription.trial_days;
    const firstCharge = dayjs().add(trialDays, "day");

    return firstCharge.format("DD/MM/YYYY");
  }, [hasTrialDays, product?.subscription?.trial_days]);

  return {
    hasTrialDays,
    firstChargeDate,
  };
};
