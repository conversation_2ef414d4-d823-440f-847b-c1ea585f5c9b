"use client";

import { getCreditCardNameByNumber } from "@thebank/creditcard-js";
import { useMemo, useState } from "react";

import { clearString } from "@/utils/stringUtils";

import { useCreditCardList } from "./useCreditCardList";

export const brandMapping = {
  amex: "/brands/American Express (Amex).svg",
  diners: "/brands/Diners Club.svg",
  discover: "/brands/Discover.svg",
  elo: "/brands/Elo.svg",
  jcb: "/brands/JCB.svg",
  mastercard: "/brands/MasterCard.svg",
  visa: "/brands/Visa.svg",
  hipercard: "/brands/Hipercard.svg",
  unionpay: "/brands/Union Pay.svg",
  mir: "/brands/Mir.svg",
  hiper: "/brands/Hiper.svg",
  default: "/brands/defaultcard.svg",
} as const;

export const useCreditCardFormatter = () => {
  const { data: creditCardList } = useCreditCardList();
  const [cardBrandIcon, setCardBrandIcon] = useState<string | null>(null);

  const mappedCreditCards = useMemo(() => {
    if (!creditCardList) return [];

    return creditCardList
      .filter(card => {
        const normalizedBrand = card.brand.toLowerCase();
        return normalizedBrand in brandMapping;
      })
      .map(card => ({
        ...card,
        icon_url: card.icon_url?.trim()
          ? card.icon_url
          : brandMapping[card.brand.toLowerCase() as keyof typeof brandMapping],
      }));
  }, [creditCardList]);

  function formatCreditCard(value: string) {
    return clearString(value).replace(/(\d{4})(?=\d)/g, "$1 ");
  }

  function isUnionPay(cardNumber: number) {
    const cleaned = cardNumber.toString().replace(/\D/g, "");
    const length = cleaned.length;

    const isValidPrefix = /^62/.test(cleaned);

    const isValidLength = length >= 16 && length <= 19;

    return isValidPrefix && isValidLength;
  }

  function isMirCard(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\D/g, "");
    const prefix = parseInt(cleaned.slice(0, 4), 10);
    const isValidPrefix = prefix >= 2200 && prefix <= 2204;
    const isValidLength = cleaned.length === 16;
    return isValidPrefix && isValidLength;
  }

  function isHiper(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\D/g, "");
    const isValidLength = cleaned.length === 16;

    const patterns = [
      "6370",
      "637095",
      "63737423",
      "63743358",
      "637568",
      "637599",
      "637609",
      "637612",
    ];

    const isValidPrefix = patterns.some(pattern => cleaned.startsWith(pattern));

    return isValidLength && isValidPrefix;
  }

  function updateCardBrandIcon(formattedValue: string) {
    const rawCardNumber = clearString(formattedValue);
    const cardBrand = getCreditCardNameByNumber(rawCardNumber);

    let normalizedCardBrand = cardBrand?.toLowerCase();

    if (cardBrand === "Credit card is invalid!") {
      if (isUnionPay(Number(rawCardNumber))) {
        normalizedCardBrand = "unionpay";
      }
      if (isMirCard(rawCardNumber)) {
        normalizedCardBrand = "mir";
      }
      if (isHiper(rawCardNumber)) {
        normalizedCardBrand = "hiper";
      }
    }

    if (normalizedCardBrand) {
      const matchingCard = creditCardList?.find(
        card => card.brand.toLowerCase() === normalizedCardBrand
      );

      if (matchingCard?.icon_url?.trim()) {
        setCardBrandIcon(matchingCard.icon_url);
        return;
      }

      const brandIcon =
        brandMapping[normalizedCardBrand as keyof typeof brandMapping] ||
        brandMapping.default;

      setCardBrandIcon(brandIcon);
    } else {
      setCardBrandIcon(brandMapping.default);
    }
  }

  return {
    cardBrandIcon,
    formatCreditCard,
    updateCardBrandIcon,
    creditCardList: mappedCreditCards,
  };
};
