import { useEffect, useMemo, useState } from "react";

import { SavedData } from "@/types/savedData";
import { formatToCNPJ, formatToCPF } from "@/utils/formatting";
import { isCNPJ } from "@/utils/validations";

export const useSavedData = () => {
  const [savedData, setSavedData] = useState<SavedData | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedData = sessionStorage.getItem("buyerData");
      setSavedData(savedData ? JSON.parse(savedData) : null);
    }
  }, []);

  const formattedSavedData = useMemo(() => {
    if (!savedData) return null;

    const formattedDocument = savedData.document
      ? {
          type: savedData.document.type,
          number: isCNPJ(savedData.document.number)
            ? formatToCNPJ(savedData.document.number)
            : formatToCPF(savedData.document.number),
        }
      : undefined;

    const phoneValue =
      savedData.phone?.ddi && savedData.phone?.number
        ? (() => {
            const ddi = savedData.phone.ddi.startsWith("+")
              ? savedData.phone.ddi
              : `+${savedData.phone.ddi}`;
            return `${ddi}${savedData.phone.number}`;
          })()
        : "";

    return {
      ...savedData,
      document: formattedDocument,
      phoneValue,
    };
  }, [savedData]);

  return {
    savedData,
    formattedSavedData,
  };
};
