import * as Sentry from "@sentry/nextjs";
import { useEffect, useRef } from "react";

import { createSocketConnection, isSocketReady } from "@/ws/socket";

interface SocketManagerOptions {
  enabled?: boolean;
  onConnectionReady?: () => void;
  onConnectionError?: (error: Error) => void;
}

/**
 * Hook para gerenciar conexões WebSocket de forma centralizada
 * Evita múltiplas inicializações e fornece controle sobre quando conectar
 */
export function useSocketManager(options: SocketManagerOptions = {}) {
  const { enabled = true, onConnectionReady, onConnectionError } = options;
  const initializationAttempted = useRef(false);

  useEffect(() => {
    if (!enabled || initializationAttempted.current) {
      return;
    }

    initializationAttempted.current = true;

    const initializeSocket = async () => {
      try {
        await createSocketConnection();

        if (isSocketReady() && onConnectionReady) {
          onConnectionReady();
        }
      } catch (error) {
        const socketError =
          error instanceof Error ? error : new Error(String(error));

        Sentry.captureException(socketError, {
          tags: {
            component: "useSocketManager",
            action: "initializeSocket",
          },
        });

        if (onConnectionError) {
          onConnectionError(socketError);
        }
      }
    };

    initializeSocket();
  }, [enabled, onConnectionReady, onConnectionError]);

  return {
    isReady: isSocketReady(),
  };
}
