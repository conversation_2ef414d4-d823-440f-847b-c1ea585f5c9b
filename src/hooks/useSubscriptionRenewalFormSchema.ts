import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";

import {
  createSubscriptionRenewalSchema,
  SubscriptionRenewalFormValues,
} from "@/schemas/subscriptionRenewalSchema";
import { PaymentOption } from "@/types/paymentOptions";

export const useSubscriptionRenewalFormSchema = (
  selectedOption: PaymentOption
) => {
  const translations = useTranslations("buyer_form");
  const renewalSchema = createSubscriptionRenewalSchema(
    translations,
    selectedOption
  );

  const formMethods = useForm<SubscriptionRenewalFormValues>({
    resolver: zodResolver(renewalSchema),
    mode: "onSubmit",
    defaultValues: {
      cardNumber: "",
      cardHolderName: "",
      cardCvv: "",
      cardExpirationDateMonth: "",
      cardExpirationDateYear: "",
      instalment: undefined,
    },
  });

  return {
    formMethods,
    translations,
  };
};
