import { useQuery } from "@tanstack/react-query";

import { awaitingPaymentService } from "@/services/awaiting-payment";
import { AwaitingPaymentOrder } from "@/types/awaitingPayment";

export const useAwaitingPaymentOrder = (orderId: string) => {
  const { data, ...rest } = useQuery({
    queryKey: ["awaiting-payment/order", orderId],
    queryFn: () => awaitingPaymentService.getOrder(orderId),
    enabled: !!orderId,
  });

  return {
    orderData: data || ({} as AwaitingPaymentOrder),
    ...rest,
  };
};
