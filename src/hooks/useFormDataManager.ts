import { useCallback, useEffect } from "react";
import { UseFormSetValue } from "react-hook-form";

import { useSavedData } from "./useSavedData";

export const useFormDataManager = (updateFormValue: UseFormSetValue<any>) => {
  const { savedData, formattedSavedData } = useSavedData();

  const handlePhoneChange = useCallback(
    (phoneValue: string) => {
      if (phoneValue) {
        const extractDDI = (value: string) => {
          if (value.startsWith("+")) {
            return value.slice(0, 3);
          }
          return "+55";
        };

        const countryCode: string = extractDDI(phoneValue) || "";
        const phoneNumber: string = phoneValue.replace(countryCode, "").trim();

        updateFormValue("phone.ddi", countryCode);
        updateFormValue("phone.number", phoneNumber);
      } else {
        updateFormValue("phone.ddi", "");
        updateFormValue("phone.number", "");
      }
    },
    [updateFormValue]
  );

  useEffect(() => {
    if (savedData) {
      updateFormValue("name", savedData.name ?? "");
      updateFormValue("email", savedData.email ?? "");
      updateFormValue("confirm_email", savedData.confirm_email ?? "");
      updateFormValue("phone.ddi", savedData.phone?.ddi ?? "");
      updateFormValue("phone.number", savedData.phone?.number ?? "");

      if (formattedSavedData?.document) {
        updateFormValue("document", formattedSavedData.document);
      }
    }
  }, [savedData, formattedSavedData, updateFormValue]);

  return {
    handlePhoneChange,
    phoneValue: formattedSavedData?.phoneValue ?? "",
  };
};
