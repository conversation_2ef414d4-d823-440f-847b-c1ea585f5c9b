import { useState } from "react";
import { parsePhoneNumber } from "react-phone-number-input";

export const useDocumentAndPhone = () => {
  const [haveBrazilianDocument, setHaveBrazilianDocument] = useState(true);

  const extractDDI = (phoneNumber: string) => {
    const parsedNumber = parsePhoneNumber(phoneNumber);
    return parsedNumber?.countryCallingCode
      ? `+${parsedNumber.countryCallingCode}`
      : "";
  };

  return {
    haveBrazilianDocument,
    extractDDI,
    setHaveBrazilianDocument,
  };
};
