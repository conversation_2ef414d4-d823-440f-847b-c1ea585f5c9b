import { useQuery } from "@tanstack/react-query";

import { awaitingPaymentService } from "@/services/awaiting-payment";
import { AwaitingPaymentSubscription } from "@/types/awaitingPayment";

export const useAwaitingPaymentSubscription = (subscriptionCode: string) => {
  const { data, ...rest } = useQuery({
    queryKey: ["awaiting-payment/subscription", subscriptionCode],
    queryFn: () => awaitingPaymentService.getSubscription(subscriptionCode),
    enabled: !!subscriptionCode,
  });

  return {
    subscriptionData: data || ({} as AwaitingPaymentSubscription),
    ...rest,
  };
};
