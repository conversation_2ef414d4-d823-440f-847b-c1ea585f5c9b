import { useCreditCardFormatter } from "./useCreditCardFormatter";

export const usePriorityCards = () => {
  const { creditCardList } = useCreditCardFormatter();

  if (!creditCardList)
    return {
      sortedCreditCards: [],
      priorityZeroCards: [],
      priorityZeroCount: 0,
      restCreditCards: [],
    };

  const priorityZeroCards = creditCardList.filter(card => card.priority === 0);
  const priorityZeroCount = priorityZeroCards.length;

  const sortedCreditCards =
    priorityZeroCount === creditCardList.length
      ? creditCardList.slice(0, 3)
      : creditCardList.sort((a, b) => b.priority - a.priority).slice(0, 3);

  const restCreditCards = creditCardList.filter(
    card =>
      !sortedCreditCards.some(
        sortedCard =>
          sortedCard.brand.toLowerCase() === card.brand.toLowerCase()
      )
  );

  return {
    sortedCreditCards,
    priorityZeroCards,
    priorityZeroCount,
    restCreditCards,
  };
};
