import { useCallback, useEffect, useState } from "react";

import { InstallmentsResponse } from "@/types/instalmentsList";
import { Product } from "@/types/product";

interface UseBumpSelectionProps {
  product: Product;
  productInstallments: InstallmentsResponse;
  selectedInstallment: string;
  onAddToCart?: () => void;
}

export const useBumpSelection = ({
  product,
  productInstallments,
  selectedInstallment,
  onAddToCart,
}: UseBumpSelectionProps) => {
  const [selectedBumps, setSelectedBumps] = useState<string[]>([]);
  const [selectedProductBumpsId, setSelectedProductBumpsId] = useState<
    string[]
  >([]);
  const [bumpInstallments, setBumpInstallments] = useState<
    Record<string, string>
  >({});

  const handleBumpSelection = useCallback(
    (bumpId: string, isSelected: boolean) => {
      setSelectedBumps(prev =>
        isSelected ? [...prev, bumpId] : prev.filter(id => id !== bumpId)
      );

      const selectedBump = product.bumps.find(bump => bump.id === bumpId);
      if (selectedBump) {
        setSelectedProductBumpsId(prev =>
          isSelected
            ? [...prev, selectedBump.product_id]
            : prev.filter(id => id !== selectedBump.product_id)
        );
      }

      if (isSelected && onAddToCart) {
        onAddToCart();
      }
    },
    [product.bumps, onAddToCart]
  );

  useEffect(() => {
    if (productInstallments?.bumps && selectedInstallment) {
      const newBumpInstallments: Record<string, string> = {};

      productInstallments.bumps.forEach(bumpProduct => {
        const matchingInstallmentOption = bumpProduct.installments.find(
          installmentOption =>
            installmentOption.installments === parseInt(selectedInstallment)
        );

        if (matchingInstallmentOption) {
          newBumpInstallments[bumpProduct.bump_id] =
            matchingInstallmentOption.description;
        }
      });

      setBumpInstallments(newBumpInstallments);
    }
  }, [selectedInstallment, productInstallments]);

  return {
    selectedBumps,
    selectedProductBumpsId,
    bumpInstallments,
    handleBumpSelection,
  };
};
