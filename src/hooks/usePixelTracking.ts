import { useCallback, useMemo } from "react";

import { PaymentOption } from "@/types/paymentOptions";
import {
  GoogleAdsCredential,
  Pixel,
  PixelProviders,
  Product,
} from "@/types/product";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";

interface UsePixelTrackingProps {
  product: Product;
  totalPrice: number;
  selectedOption: PaymentOption;
  selectedBumps: string[];
}

export const usePixelTracking = ({
  product,
  totalPrice,
  selectedOption,
  selectedBumps,
}: UsePixelTrackingProps) => {
  const tiktokPixel = useMemo(
    () =>
      product.pixels?.find(
        (pixel: { provider: string }): pixel is Pixel =>
          pixel.provider === PixelProviders.TIKTOK
      ),
    [product.pixels]
  );

  const googleAds = useMemo(
    () =>
      product.pixels?.find(
        (pixel: { provider: string }) =>
          pixel.provider === PixelProviders.GOOGLE_ADS
      ) as unknown as any,
    [product.pixels]
  );

  const metaPixel = useMemo(
    () =>
      product.pixels?.find(
        (pixel: any): pixel is Pixel => pixel.provider === PixelProviders.META
      ),
    [product.pixels]
  );

  const hasValidTiktokPixelCredentials = useMemo(
    () => (tiktokPixel?.credentials?.pixels?.length ?? 0) > 0,
    [tiktokPixel?.credentials?.pixels?.length]
  );

  const metaPixelEvents = useMemo(
    () =>
      product.pixels?.find(pixel => pixel?.provider === PixelProviders.META)
        ?.credentials?.events,
    [product.pixels]
  );

  const tiktokPixelEvents = useMemo(
    () =>
      product.pixels?.find(pixel => pixel?.provider === PixelProviders.TIKTOK)
        ?.credentials?.events,
    [product.pixels]
  );

  const handleFacebookPixelEvent = useCallback(
    (eventName: string) => {
      if (metaPixel && typeof window !== "undefined" && window.fbq) {
        window.fbq("track", eventName, {
          payment_method: selectedOption,
          currency: "BRL",
          value: formatToBRL(Number(totalPrice) / CENTS_IN_REAL),
        });
      }
    },
    [metaPixel, selectedOption, totalPrice]
  );

  const handleTiktokPixelEvent = useCallback(
    (eventName: string) => {
      if (
        hasValidTiktokPixelCredentials &&
        typeof window !== "undefined" &&
        window.ttq
      ) {
        window.ttq.track(eventName, {
          contents: [
            {
              content_id: product.id,
              content_type: "product",
              content_name: product.title,
            },
          ],
          value: Number(totalPrice) / CENTS_IN_REAL,
          currency: "BRL",
        });
      }
    },
    [hasValidTiktokPixelCredentials, product.id, product.title, totalPrice]
  );

  const handleGoogleAdsEvent = useCallback(() => {
    if (googleAds?.credentials && typeof window !== "undefined") {
      googleAds?.credentials?.map((credential: GoogleAdsCredential) => {
        if (credential?.events?.includes("purchase_pix_creditcard")) {
          window.gtag?.("event", "conversion", {
            send_to: `${credential?.pixel_id}/${credential?.conversion_label}`,
            value: Number(totalPrice) / CENTS_IN_REAL,
            currency: "BRL",
          });

          window.gtag?.("event", "purchase", {
            transaction_id: new Date().getTime().toString(),
            value: Number(totalPrice) / CENTS_IN_REAL,
            currency: "BRL",
            items: [
              {
                item_id: product.id,
                item_name: product.title,
                price: Number(product.price) / CENTS_IN_REAL,
                quantity: 1,
              },
              ...selectedBumps
                .map(bumpId => {
                  const bump = product.bumps.find(b => b.id === bumpId);
                  return bump
                    ? {
                        item_id: bump.id,
                        item_name: bump.title,
                        price: Number(bump.price) / CENTS_IN_REAL,
                        quantity: 1,
                      }
                    : null;
                })
                .filter(Boolean),
            ],
          });
        }
      });
    }
  }, [googleAds?.credentials, totalPrice, product, selectedBumps]);

  const handleGoogleAdsBeginCheckout = useCallback(() => {
    if (googleAds?.credentials && typeof window !== "undefined") {
      window.gtag?.("event", "begin_checkout", {
        currency: "BRL",
        value: Number(totalPrice) / CENTS_IN_REAL,
        items: [
          {
            item_id: product.id,
            item_name: product.title,
            price: Number(product.price) / CENTS_IN_REAL,
            quantity: 1,
          },
          ...selectedBumps
            .map(bumpId => {
              const bump = product.bumps.find(b => b.id === bumpId);
              return bump
                ? {
                    item_id: bump.id,
                    item_name: bump.title,
                    price: Number(bump.price) / CENTS_IN_REAL,
                    quantity: 1,
                  }
                : null;
            })
            .filter(Boolean),
        ],
      });
    }
  }, [googleAds?.credentials, totalPrice, product, selectedBumps]);

  const handlePurchaseEvent = useCallback(
    (events: any) => {
      if (events && events.length > 0) {
        if (typeof window !== "undefined" && window.fbq) {
          window.fbq("track", "Purchase", {
            value: formatToBRL(Number(totalPrice) / CENTS_IN_REAL),
            currency: "BRL",
          });
        }
      }
    },
    [totalPrice]
  );

  const handlePurchaseEventTiktok = useCallback(
    (events: any) => {
      if (
        events &&
        events.length > 0 &&
        hasValidTiktokPixelCredentials &&
        typeof window !== "undefined" &&
        window.ttq
      ) {
        window.ttq.track("CompletePayment", {
          contents: [
            {
              content_id: product.id,
              content_type: "product",
              content_name: product.title,
            },
          ],
          value: Number(totalPrice) / CENTS_IN_REAL,
          currency: "BRL",
        });
      }
    },
    [hasValidTiktokPixelCredentials, product.id, product.title, totalPrice]
  );

  const handleAddToCartEvent = useCallback(() => {
    if (typeof window !== "undefined" && window.fbq) {
      window.fbq("track", "AddToCart", {
        content_name: product.title,
        content_category: "product",
        content_ids: [product.id],
        content_type: "product",
        value: formatToBRL(Number(product.price) / CENTS_IN_REAL),
        currency: "BRL",
      });
    }

    if (
      hasValidTiktokPixelCredentials &&
      typeof window !== "undefined" &&
      window.ttq
    ) {
      window.ttq.track("AddToCart", {
        contents: [
          {
            content_id: product.id,
            content_type: "product",
            content_name: product.title,
          },
        ],
        value: Number(product.price) / CENTS_IN_REAL,
        currency: "BRL",
      });
    }
  }, [hasValidTiktokPixelCredentials, product]);

  return {
    metaPixelEvents,
    tiktokPixelEvents,
    handleFacebookPixelEvent,
    handleTiktokPixelEvent,
    handleGoogleAdsEvent,
    handleGoogleAdsBeginCheckout,
    handlePurchaseEvent,
    handlePurchaseEventTiktok,
    handleAddToCartEvent,
  };
};

declare global {
  interface Window {
    fbq?: (event: string, eventName: string, params?: object) => void;
    ttq?: {
      track: (event: string, params?: object) => void;
    };
    gtag?: (...args: any[]) => void;
  }
}
