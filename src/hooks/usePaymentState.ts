import { useState } from "react";

import { PaymentOption } from "@/types/paymentOptions";

export const usePaymentState = () => {
  const [selectedOption, setSelectedOption] = useState<PaymentOption>(
    PaymentOption.CreditCard
  );
  const [selectedInstalmentDescription, setSelectedInstalmentDescription] =
    useState("");

  const getCardStyles = (value: string) =>
    selectedOption === value ? "bg-blue-50 border-2 border-blue-700" : "border";

  return {
    selectedOption,
    setSelectedOption,
    selectedInstalmentDescription,
    setSelectedInstalmentDescription,
    getCardStyles,
  };
};
