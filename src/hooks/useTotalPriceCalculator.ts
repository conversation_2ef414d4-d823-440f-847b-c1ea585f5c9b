import { useMemo } from "react";

import { InstallmentsResponse } from "@/types/instalmentsList";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";

interface UseTotalPriceCalculatorProps {
  selectedOption: PaymentOption;
  selectedInstallment: string;
  productInstallments: InstallmentsResponse;
  product: Product;
  selectedBumps: string[];
}

export const useTotalPriceCalculator = ({
  selectedOption,
  selectedInstallment,
  productInstallments,
  product,
  selectedBumps,
}: UseTotalPriceCalculatorProps) => {
  return useMemo(() => {
    if (
      selectedOption === PaymentOption.CreditCard &&
      selectedInstallment &&
      productInstallments
    ) {
      const selectedProductInstallment = productInstallments.installments.find(
        installmentOption =>
          installmentOption.installments.toString() === selectedInstallment
      );

      let totalPrice = selectedProductInstallment
        ? selectedProductInstallment.total
        : 0;

      selectedBumps.forEach(selectedBumpId => {
        const bumpWithInstallments = productInstallments.bumps?.find(
          bumpInstallment => bumpInstallment.bump_id === selectedBumpId
        );

        const selectedBumpInstallment = bumpWithInstallments?.installments.find(
          installmentOption =>
            installmentOption.installments === parseInt(selectedInstallment)
        );

        if (selectedBumpInstallment) {
          totalPrice += selectedBumpInstallment.total;
        }
      });

      return totalPrice;
    }

    let totalPrice = product.price;
    selectedBumps.forEach(selectedBumpId => {
      const selectedBump = product.bumps.find(
        bump => bump.id === selectedBumpId
      );
      if (selectedBump) {
        totalPrice += selectedBump.price;
      }
    });

    return totalPrice;
  }, [
    selectedOption,
    selectedInstallment,
    productInstallments,
    product,
    selectedBumps,
  ]);
};
