import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";

import { BuyerFormValues } from "@/schemas/createBuyerSchema";
import { createBuyerSchema } from "@/schemas/createBuyerSchema";
import { PaymentOption } from "@/types/paymentOptions";

export const useBuyerFormSchema = (
  haveBrazilianDocument: boolean,
  selectedOption: PaymentOption
) => {
  const translations = useTranslations("buyer_form");
  const buyerSchema = createBuyerSchema(
    translations,
    haveBrazilianDocument,
    selectedOption
  );

  const formMethods = useForm<BuyerFormValues>({
    resolver: zodResolver(buyerSchema),
    mode: "onSubmit",
  });

  return {
    formMethods,
    translations,
  };
};
