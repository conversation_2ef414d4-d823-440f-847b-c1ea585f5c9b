import { create } from "zustand";

interface AddressState {
  showFields: boolean;
  setShowFields: (value: boolean) => void;
  hasNumber: boolean;
  streetNumber: string;
  setHasNumber: (value: boolean) => void;
  setStreetNumber: (value: string) => void;
  streetNumberError: boolean;
  setStreetNumberError: (value: boolean) => void;
}

export const useAddressStore = create<AddressState>(set => ({
  showFields: false,
  setShowFields: (value: boolean) => set({ showFields: value }),
  hasNumber: true,
  streetNumber: "",
  setHasNumber: value => set({ hasNumber: value }),
  setStreetNumber: value => set({ streetNumber: value }),
  streetNumberError: false,
  setStreetNumberError: value => set({ streetNumberError: value }),
}));
