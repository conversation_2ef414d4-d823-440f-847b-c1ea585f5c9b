import { useEffect, useState } from "react";

export const useTimer = (initialTimeInSeconds: number) => {
  const oneSecondInMilliseconds = 1000;
  const secondsInOneHour = 3600;
  const secondsInOneDay = 86400;
  const secondsPerMinute = 60;

  const [timeInSeconds, setTimeInSeconds] = useState(initialTimeInSeconds);

  useEffect(() => {
    if (timeInSeconds > 0) {
      const timer = setInterval(() => {
        setTimeInSeconds(prevTime => (prevTime > 0 ? prevTime - 1 : 0));
      }, oneSecondInMilliseconds);

      return () => clearInterval(timer);
    }
  }, [timeInSeconds]);

  const formatTimeOptions = {
    minutes: (seconds: number) => {
      const minutes = Math.floor(seconds / secondsPerMinute);
      const secs = seconds % secondsPerMinute;
      return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    },
    hours: (seconds: number) => {
      const hours = Math.floor(seconds / secondsInOneHour);
      const remainingSeconds = seconds % secondsInOneHour;
      const minutes = Math.floor(remainingSeconds / secondsPerMinute);
      const secs = remainingSeconds % secondsPerMinute;
      return `${hours}h ${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    },
    days: (seconds: number) => {
      const days = Math.floor(seconds / secondsInOneDay);
      const remainingSeconds = seconds % secondsInOneDay;
      const hours = Math.floor(remainingSeconds / secondsInOneHour);
      const remainingSecondsAfterHours = remainingSeconds % secondsInOneHour;
      const minutes = Math.floor(remainingSecondsAfterHours / secondsPerMinute);
      const secs = remainingSecondsAfterHours % secondsPerMinute;
      return `${days}d ${hours}h ${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    },
  };

  const formatTime = (seconds: number) => {
    if (seconds <= 0) return "00:00";

    const formatter =
      seconds <= secondsInOneHour
        ? formatTimeOptions.minutes
        : seconds <= secondsInOneDay
          ? formatTimeOptions.hours
          : formatTimeOptions.days;

    return formatter(seconds);
  };

  return { timeInSeconds, formatTime };
};

export default useTimer;
