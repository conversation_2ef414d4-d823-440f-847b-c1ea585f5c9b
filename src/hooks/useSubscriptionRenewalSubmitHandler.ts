import { useMutation } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";

import { SubscriptionRenewalFormValuesPartial } from "@/schemas/subscriptionRenewalSchema";
import { subscriptionService } from "@/services/subscription";
import { buyerStore } from "@/store/buyerStore";
import { useCardValidationStore } from "@/store/cardValidationStore";
import { Orchestrators } from "@/types/enums";
import { PaymentOption } from "@/types/paymentOptions";
import {
  SubscriptionRenewalResponse,
  SubscriptionRenewalStatus,
} from "@/types/subscriptionRenewalResponse";
import { buildSubscriptionPayload } from "@/utils/createOrderSubmitHandler/buildSubscriptionPayload";
import { processPayment } from "@/utils/createOrderSubmitHandler/processPayment";
import {
  handleSubscriptionSyncPayment,
  redirectToPendingSubscriptionCreditCard,
} from "@/utils/subscriptionRenewalSubmitHandler";

import { useOrchestratorData } from "./useOrchestratorData";

export const useSubscriptionRenewalSubmitHandler = (
  selectedOption: PaymentOption,
  subscriptionId: string,
  signature?: string
) => {
  const params = useParams();
  const router = useRouter();
  const { isCardValid } = useCardValidationStore();
  const [isLoading, setIsLoading] = useState(false);

  const {
    tokenizationError,
    setTokenizationError,
    setPaymentError,
    paymentError,
    setFailedPaymentInformation,
    setPixError,
    pixError,
    setFailedPixInformation,
    setBoletoError,
    boletoError,
    setFailedBoletoInformation,
  } = buyerStore();

  const { data: orchestratorData } = useOrchestratorData(
    Orchestrators.MALGA,
    selectedOption
  );

  const locale = Array.isArray(params.locale)
    ? params.locale[0]
    : params.locale;

  const handleSubscriptionSuccess = (data: SubscriptionRenewalResponse) => {
    if (
      selectedOption === PaymentOption.CreditCard &&
      data?.transaction?.status === SubscriptionRenewalStatus.PENDING
    ) {
      if (locale) {
        redirectToPendingSubscriptionCreditCard(
          data,
          locale,
          subscriptionId,
          router
        );
      }
    } else {
      handleSubscriptionSyncPayment({
        response: data,
        selectedOption,
        locale: locale || "pt-BR",
        subscriptionId,
        signature,
        router,
        setPixError,
        setFailedPixInformation,
        setBoletoError,
        setFailedBoletoInformation,
        setPaymentError,
        setFailedPaymentInformation,
        setIsLoading,
      });
    }
  };

  const handleSubscriptionError = () => {
    setTokenizationError(true);
    setIsLoading(false);
  };

  const mutation = useMutation({
    mutationFn: (payload: any) =>
      subscriptionService.renewSubscription(subscriptionId, payload, signature),
    onSuccess: handleSubscriptionSuccess,
    onError: handleSubscriptionError,
  });

  const onSubmit = async (data: SubscriptionRenewalFormValuesPartial) => {
    if (!isCardValid && selectedOption === PaymentOption.CreditCard) {
      return;
    }

    setTokenizationError(false);
    setIsLoading(true);

    try {
      let token: string | undefined;

      if (selectedOption === PaymentOption.CreditCard) {
        if (!orchestratorData) {
          setIsLoading(false);
          return;
        }

        token = await processPayment(
          data as any,
          selectedOption,
          orchestratorData,
          setTokenizationError
        );
      }

      const payload = buildSubscriptionPayload(
        data,
        subscriptionId,
        selectedOption,
        token,
        signature
      );

      mutation.mutate(payload);
    } catch (error) {
      console.error("Error processing subscription renewal:", error);
      setIsLoading(false);
      setTokenizationError(true);
    }
  };

  return {
    onSubmit,
    isLoading,
    tokenizationError,
    paymentError,
    pixError,
    boletoError,
  };
};
