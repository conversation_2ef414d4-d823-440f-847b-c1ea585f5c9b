import { useCallback } from "react";
import { useForm } from "react-hook-form";

import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { clearString } from "@/utils/stringUtils";
import { formatToCEP } from "@/utils/validations/formatToCEP";

export function useHandleCEP(
  setValue: ReturnType<
    typeof useForm<Partial<BuyerFormValuesPartial>>
  >["setValue"],
  trigger: ReturnType<
    typeof useForm<Partial<BuyerFormValuesPartial>>
  >["trigger"]
) {
  const handleCEPChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.value) return;

      const value = clearString(e.target.value);
      const formattedValue = formatToCEP(value);

      setValue("billing_address.zipcode", formattedValue);
      trigger("billing_address.zipcode");
    },
    [setValue, trigger]
  );

  return { handleCEPChange };
}
