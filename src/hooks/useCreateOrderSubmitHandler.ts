/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation } from "@tanstack/react-query";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { orderService } from "@/services/order";
import { buyerStore } from "@/store/buyerStore";
import { useCardValidationStore } from "@/store/cardValidationStore";
import { Orchestrators } from "@/types/enums";
import { OrderPayload } from "@/types/order";
import { OrderResponse, OrderStatus } from "@/types/orderResponse";
import { PaymentOption } from "@/types/paymentOptions";
import { OrganizationStatus, Product } from "@/types/product";
import { buildOrderPayload } from "@/utils/createOrderSubmitHandler/buildOrderPayload";
import { formatBuyerData } from "@/utils/createOrderSubmitHandler/formatBuyerData";
import { redirectToPendingCreditCardOrder } from "@/utils/createOrderSubmitHandler/navigationUtils";
import { handleSyncPayment } from "@/utils/createOrderSubmitHandler/paymentSyncOrchestrator";
import { processPayment } from "@/utils/createOrderSubmitHandler/processPayment";

import { useCreditCardFormatter } from "./useCreditCardFormatter";
import { useOrchestratorData } from "./useOrchestratorData";

export const useCreateOrderSubmitHandler = (
  haveBrazilianDocument: boolean,
  selectedOption: PaymentOption,
  product: Product,
  selectedProductBumpsId: string[],
  selectedInstalmentDescription?: string
) => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { cardBrandIcon } = useCreditCardFormatter();
  const { isCardValid } = useCardValidationStore();

  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const {
    tokenizationError,
    setTokenizationError,
    setPaymentError,
    paymentError,
    setFailedPaymentInformation,
    setPixError,
    pixError,
    setFailedPixInformation,
    setBoletoError,
    boletoError,
    setFailedBoletoInformation,
  } = buyerStore();

  const isOrganizationCompleteRegistered =
    product?.organization?.status?.value === OrganizationStatus.COMPLETED;

  const { data: orchestratorData } = useOrchestratorData(
    Orchestrators.MALGA,
    selectedOption
  );

  const locale = Array.isArray(params.locale)
    ? params.locale[0]
    : params.locale;

  const handleOrderSuccess = (data: OrderResponse) => {
    if (
      selectedOption === PaymentOption.CreditCard &&
      data?.transaction?.status === OrderStatus.PENDING
    ) {
      const productIdForUrl =
        product?.id || (Array.isArray(params.id) ? params.id[0] : params.id);
      if (productIdForUrl && locale) {
        redirectToPendingCreditCardOrder(data, locale, productIdForUrl, router);
      }
    } else {
      handleSyncPayment({
        orderResponse: data,
        selectedOption,
        locale: locale || "pt-BR",
        cardBrandIcon: cardBrandIcon || undefined,
        router,
        setPixError,
        setFailedPixInformation,
        setBoletoError,
        setFailedBoletoInformation,
        setPaymentError,
        setFailedPaymentInformation,
        setIsLoading,
      });
    }
  };

  const handleOrderError = () => {
    setTokenizationError(true);
    setIsLoading(false);
  };

  const handleRetryOrderError = (
    error: Error & { response: { data: { title: string; detail: string } } }
  ) => {
    const errorTitleMessage = error?.response?.data?.title;
    const errorDescriptionMessage = error?.response?.data?.detail;
    setFailedPaymentInformation({
      title: errorTitleMessage,
      description: errorDescriptionMessage,
    });
    setIsLoading(false);
  };

  const mutation = useMutation({
    mutationFn: (payload: OrderPayload) => orderService.createOrder(payload),
    onSuccess: handleOrderSuccess,
    onError: handleOrderError,
  });

  const orderId = Array.isArray(params.id) ? params.id[0] : params.id;

  const retryOrderMutation = useMutation({
    mutationFn: (payload: OrderPayload) => {
      if (!orderId) {
        throw new Error("Order ID is required");
      }
      return orderService.retryOrder(orderId, payload);
    },
    onSuccess: handleOrderSuccess,
    onError: handleRetryOrderError,
  });

  const saveBuyerData = (data: BuyerFormValuesPartial) => {
    const buyerDataToSave = {
      ...data,
      selectedInstalmentDescription: selectedInstalmentDescription || "",
    };
    sessionStorage.setItem("buyerData", JSON.stringify(buyerDataToSave));
  };

  const onSubmit = async (data: BuyerFormValuesPartial) => {
    if (!isCardValid && selectedOption === PaymentOption.CreditCard) {
      return;
    }

    if (!isOrganizationCompleteRegistered) {
      setShowModal(true);
      return;
    }

    const productId = Array.isArray(params.id) ? params.id[0] : params.id;
    const searchParamsProduct = searchParams.get("product");

    if (!orchestratorData || !productId || typeof productId !== "string") {
      setIsLoading(false);
      return;
    }

    setTokenizationError(false);
    setIsLoading(true);

    const formattedBuyerData = formatBuyerData(data, haveBrazilianDocument);

    try {
      const token = await processPayment(
        data,
        selectedOption,
        orchestratorData,
        setTokenizationError
      );

      const payload = buildOrderPayload(
        formattedBuyerData,
        productId,
        selectedOption,
        token,
        selectedProductBumpsId
      );

      if (searchParamsProduct) {
        const retryPayload = buildOrderPayload(
          formattedBuyerData,
          searchParamsProduct,
          selectedOption,
          token,
          selectedProductBumpsId
        );
        retryOrderMutation.mutate(retryPayload);
      } else {
        mutation.mutate(payload);
      }

      saveBuyerData(data);
    } catch (error) {
      console.error("Error processing payment:", error);
      setIsLoading(false);
      setTokenizationError(true);
    }
  };

  return {
    onSubmit,
    isLoading,
    tokenizationError,
    isCreatingOrder: mutation.isPending,
    showModal,
    setShowModal,
    paymentError,
    pixError,
    boletoError,
  };
};
