import * as Sentry from "@sentry/nextjs";
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

import { buyerStore } from "@/store/buyerStore";
import {
  AwaitingPaymentOrder,
  AwaitingPaymentSubscription,
} from "@/types/awaitingPayment";
import { OrderResponse, OrderStatus } from "@/types/orderResponse";
import { SubscriptionRenewalResponse } from "@/types/subscriptionRenewalResponse";
import { OrderPaymentResponse as OrderPaymentResponseType } from "@/types/successPage";
import { fromBase64, makeQuery } from "@/utils/stringUtils/enconding";

type Response = (OrderResponse | SubscriptionRenewalResponse) & {
  thank_you_page_url?: string;
};

type OrderPaymentResponse = Omit<
  OrderPaymentResponseType,
  "order_id" | "subscription_id" | "payment"
> & {
  order_id?: string;
  subscription_id?: string;
  payment: Omit<OrderPaymentResponseType["payment"], "brand_url_image"> & {
    brand_image_url?: string;
  };
};

const normalizeResponse = (
  response: any,
  orderOrSubscriptionId: string,
  customerEmail = "Customer"
): OrderPaymentResponse => {
  return {
    order_id: orderOrSubscriptionId,
    subscription_id: orderOrSubscriptionId,
    payment_method: response?.payment_method || response?.payment?.method,
    customer_email: response?.customer_email || customerEmail,
    product: {
      id: response?.product?.id || orderOrSubscriptionId,
      title: response?.product?.title,
      description: response?.product?.description,
      platform: {
        name: response?.product?.platform?.name,
        url: response?.product?.platform?.url,
      },
    },
    bumps: response?.bumps || [],
    thank_you_page_url: response?.thank_you_page_url,
    payment: {
      status: response?.payment?.status,
      description: response?.payment?.description,
      brand: response?.payment?.brand,
      brand_image_url: "",
      first_digits: response?.payment?.first_digits,
      last_digits: response?.payment?.last_digits,
      installments: response?.payment?.installments,
      error: {
        title: response?.payment?.title || response?.payment?.error?.title,
        description:
          response?.payment?.description ||
          response?.payment?.error?.description,
      },
    },
  };
};

const decodeData = (
  encodedData: string | null,
  orderOrSubscriptionId: string,
  customerEmail = "Customer"
) => {
  if (encodedData) {
    try {
      const parsed: Response = JSON.parse(fromBase64(encodedData));
      return normalizeResponse(parsed, orderOrSubscriptionId, customerEmail);
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          page: "PaymentPendingPage",
          action: "Parsing search params",
        },
      });
    }
  }
};

type PaymentStatusHandler = (data: OrderPaymentResponse) => void;

export const useAwaitingPaymentData = (
  awaitingPaymentData: AwaitingPaymentOrder | AwaitingPaymentSubscription,
  orderOrSubscriptionId: string,
  customerEmail = "Customer"
) => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const encodedData = searchParams.get("d");
  const decodedData = useMemo<OrderPaymentResponse | undefined>(
    () => decodeData(encodedData, orderOrSubscriptionId, customerEmail),
    [encodedData]
  );

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [data, setData] = useState<OrderPaymentResponse | undefined>(
    decodedData
  );
  const { setPaymentError, setFailedPaymentInformation } = buyerStore();

  const setError = () => {
    const errorInfo = data?.payment?.error;
    setData(undefined);
    setPaymentError(true);

    if (errorInfo?.title && errorInfo?.description) {
      setFailedPaymentInformation({
        title: errorInfo.title,
        description: errorInfo.description,
      });
    }
  };

  const creditCardPaymentStatusHandlers: Partial<
    Record<OrderStatus, PaymentStatusHandler>
  > = {
    [OrderStatus.APPROVED]: (data: OrderPaymentResponse) => {
      if (data?.thank_you_page_url) {
        window.location.href = data.thank_you_page_url;
        return;
      }

      router.replace(`/${params.locale}/success?d=${makeQuery(data)}`);
    },
    [OrderStatus.FAILED]: (data: OrderPaymentResponse) => {
      setIsOpenModal(true);
      setData(data);
    },
  };

  const socketCallback = (data: any) => {
    const handler =
      creditCardPaymentStatusHandlers[data.payment.status as OrderStatus];
    if (handler) {
      handler(normalizeResponse(data, orderOrSubscriptionId, customerEmail));
    } else {
      console.warn("Status não tratado:", data.payment.status);
    }
  };

  useEffect(() => {
    const status = awaitingPaymentData?.transaction?.status;

    if (!status) return;

    if (status === OrderStatus.FAILED) {
      setIsOpenModal(true);
    }

    if (status === OrderStatus.APPROVED) {
      const query = makeQuery({
        ...data,
        payment: { ...data?.payment, status: OrderStatus.APPROVED },
      } as OrderPaymentResponse);

      router.replace(`/${params.locale}/success?d=${query}`);
    }
  }, [awaitingPaymentData]);

  return {
    isOpenModal,
    socketCallback,
    setError,
    data,
    setIsOpenModal,
  };
};
