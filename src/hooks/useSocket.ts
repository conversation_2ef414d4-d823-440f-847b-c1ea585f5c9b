import * as Sentry from "@sentry/nextjs";
import { useEffect, useRef } from "react";

import { createSocketConnection, isSocketReady } from "@/ws/socket";

export type Options = {
  channel: string;
  event: string;
  callBack: (data: any) => void;
  type: "public" | "private" | "presence";
};

export function listen({ channel, event, callBack, type }: Options) {
  if (typeof window !== "undefined" && window.Echo) {
    try {
      const echoChannel = window.Echo.channel(channel);
      echoChannel.listen(`.${event}`, callBack);

      return function cleanUp() {
        if (window.Echo && typeof window.Echo.leaveChannel === "function") {
          window.Echo.leaveChannel(`${type}:${channel}`);
        }
      };
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          component: "useSocket",
          action: "listen",
        },
        extra: {
          channel,
          event,
          type,
        },
      });
      console.error("Error in socket connection:", error);
    }
  }
  return () => {};
}

export const useSocket = ({ channel, event, callBack, type }: Options) => {
  const cleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const setupSocket = async () => {
      try {
        // Garante que o socket está inicializado antes de usar
        await createSocketConnection();

        // Verifica se está realmente pronto
        if (isSocketReady()) {
          // Limpa listener anterior se existir
          if (cleanupRef.current) {
            cleanupRef.current();
          }

          // Configura novo listener
          cleanupRef.current = listen({ channel, event, callBack, type });
        }
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            component: "useSocket",
            action: "setupSocket",
          },
          extra: {
            channel,
            event,
            type,
          },
        });
        console.error("Error in useSocket hook:", error);
      }
    };

    setupSocket();

    // Cleanup na desmontagem
    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, [channel, event, type]); // Dependências para recriar listener se mudarem
};
