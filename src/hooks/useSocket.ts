import * as Sentry from "@sentry/nextjs";
import { useEffect } from "react";

import { createSocketConnection } from "@/ws/socket";

export type Options = {
  channel: string;
  event: string;
  callBack: (data: any) => void;
  type: "public" | "private" | "presence";
};

export function listen({ channel, event, callBack, type }: Options) {
  if (typeof window !== "undefined") {
    try {
      const echoChannel = window.Echo.channel(channel);
      echoChannel.listen(`.${event}`, callBack);

      return function cleanUp() {
        if (window.Echo && typeof window.Echo.leaveChannel === "function") {
          window.Echo.leaveChannel(`${type}:${channel}`);
        }
      };
    } catch (error) {
      Sentry.captureException(error, {
        tags: {
          component: "useSocket",
          action: "listen",
        },
        extra: {
          channel,
          event,
          type,
        },
      });
      console.error("Error in socket connection:", error);
    }
  }
  return () => {};
}

export const useSocket = ({ channel, event, callBack, type }: Options) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        createSocketConnection();
        return listen({ channel, event, callBack, type });
      } catch (error) {
        Sentry.captureException(error, {
          tags: {
            component: "useSocket",
            action: "useEffect",
          },
          extra: {
            channel,
            event,
            type,
          },
        });
        console.error("Error in useSocket hook:", error);
      }
    }
  }, []);
};
