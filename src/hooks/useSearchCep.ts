/* eslint-disable @typescript-eslint/no-unused-vars */
import axios from "axios";

import { ViaCepOptions, ViaCepResponse } from "@/types/viaCepResponse";

export const useSearchCep = ({
  onAddressFound,
  onError,
  onAddressNotFound,
}: ViaCepOptions) => {
  async function execute({ cep }: { cep: string }) {
    try {
      const { data } = await axios.get<ViaCepResponse>(
        `https://viacep.com.br/ws/${cep}/json/`
      );
      if (data.erro) {
        onAddressNotFound();
        return;
      }
      onAddressFound(data);
    } catch (_: unknown) {
      onError();
    }
  }

  return { execute };
};
