"use client";

/* eslint-disable jsx-a11y/label-has-associated-control */
import { CloseFilled, Help } from "@carbon/icons-react";
import cn from "classnames";
import { useTranslations } from "next-intl";
import React from "react";
import { Controller, useFormContext } from "react-hook-form";

import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { buyerStore } from "@/store/buyerStore";
import { Installment } from "@/types/instalmentsList";

import { AlertBox } from "../AlertBox";
import { CreditCardInput } from "../CreditCardInput";
import { FormError } from "../FormError";
import { InputField } from "../InputField";
import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui";

export const CreditCardFormFields = ({
  months,
  years,
  instalments,
  setSelectedInstalmentDescription,
  setSelectedInstallment,
}: {
  months: Array<{ id: string; name: string }>;
  years: Array<{ id: string; name: string }>;
  instalments: Installment[];
  setSelectedInstalmentDescription: React.Dispatch<
    React.SetStateAction<string>
  >;
  setSelectedInstallment: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const {
    control,
    formState: { errors },
    register,
    setValue,
  } = useFormContext<Partial<BuyerFormValuesPartial>>();
  const t = useTranslations("buyer_form");
  const { tokenizationError, paymentError, failedPaymentInformation } =
    buyerStore();

  const [currentInstalment, setCurrentInstalment] = React.useState<
    number | undefined
  >(undefined);

  React.useEffect(() => {
    if (instalments && instalments.length > 0) {
      const lastInstalment = instalments[instalments.length - 1];
      const instalmentValue = Number(lastInstalment.installments);

      setCurrentInstalment(instalmentValue);
      setSelectedInstalmentDescription(lastInstalment.description);
      setSelectedInstallment(lastInstalment.installments.toString());

      setValue("instalment", instalmentValue);
    }
  }, [
    instalments,
    setSelectedInstalmentDescription,
    setSelectedInstallment,
    setValue,
  ]);

  const defaultInstalment = React.useMemo(() => {
    if (instalments && instalments.length > 0) {
      return instalments[instalments.length - 1];
    }
    return null;
  }, [instalments]);

  return (
    <div className="w-full">
      {tokenizationError && (
        <AlertBox
          className="mb-4"
          title={t("tokenization_error_title")}
          description={t("tokenization_error_description")}
          icon={<CloseFilled size={20} color="#D80027" />}
        />
      )}
      {paymentError && (
        <AlertBox
          className="mb-4"
          title={
            failedPaymentInformation?.title ?? t("tokenization_error_title")
          }
          description={
            failedPaymentInformation?.description ??
            t("tokenization_error_description")
          }
          icon={<CloseFilled size={20} color="#D80027" />}
        />
      )}
      <div>
        <CreditCardInput
          control={control}
          errors={errors}
          label={t("card_number")}
          name="cardNumber"
          placeholder="0000 0000 0000 0000"
          type="text"
        />
      </div>
      <div className="mt-4">
        <InputField
          errors={errors}
          label={t("owner_card")}
          name="cardHolderName"
          placeholder={t("name_placeholder")}
          register={register}
          type="text"
        />
      </div>
      <div className="mt-4 flex flex-col gap-4 md:flex-row">
        <div className="flex w-full gap-4 md:w-2/3">
          <div className="w-1/2">
            <div className="flex flex-col gap-1.5">
              <Label
                className={cn(
                  errors.cardExpirationDateMonth && "text-destructive"
                )}
              >
                {t("month")}
              </Label>
              <Controller
                control={control}
                name="cardExpirationDateMonth"
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={value => field.onChange(value)}
                  >
                    <SelectTrigger
                      data-testid="month-select-trigger"
                      aria-label="Month"
                      className={cn(
                        errors.cardExpirationDateMonth
                          ? "ring-text-destructive focus:ring-text-destructive border-destructive"
                          : "border-input focus:ring-ring"
                      )}
                    >
                      <SelectValue placeholder="MM" />
                    </SelectTrigger>
                    <SelectContent>
                      {months.map(month => (
                        <SelectItem key={month.id} value={month.name}>
                          {month.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
            <FormError errors={errors} fieldName="cardExpirationDateMonth" />
          </div>

          <div className="w-1/2">
            <div className="flex flex-col gap-1.5">
              <Label
                className={cn(
                  errors.cardExpirationDateYear && "text-destructive"
                )}
              >
                {t("year")}
              </Label>
              <Controller
                control={control}
                name="cardExpirationDateYear"
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={value => field.onChange(value)}
                  >
                    <SelectTrigger
                      data-testid="year-select-trigger"
                      aria-label="Year"
                      className={cn(
                        errors.cardExpirationDateYear
                          ? "ring-text-destructive focus:ring-text-destructive border-destructive"
                          : "border-input focus:ring-ring"
                      )}
                    >
                      <SelectValue placeholder="AAAA" />
                    </SelectTrigger>
                    <SelectContent>
                      {years.map(year => (
                        <SelectItem
                          data-testid="installment-select-item"
                          key={year.id}
                          value={year.name}
                        >
                          {year.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
            <FormError errors={errors} fieldName="cardExpirationDateYear" />
          </div>
        </div>

        <div className="relative w-full md:w-1/3">
          <InputField
            errors={errors}
            label={t("cvv_card")}
            maxLength={4}
            name="cardCvv"
            placeholder="123"
            register={register}
            type="number"
          />
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Help
                  data-testid="cvv-help-icon"
                  className="absolute right-2 top-[34px] cursor-pointer text-gray-400"
                  size={20}
                />
              </TooltipTrigger>
              <TooltipContent className="mb-12 mr-[-320px] w-[300px]">
                <p>{t("cvv_help")}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* <AddressFields /> */}

      <div className="w-full">
        <div className="flex flex-col gap-1.5">
          <Label className={cn(errors.instalment && "text-destructive")}>
            {t("installment")}
          </Label>
          <Controller
            control={control}
            name="instalment"
            render={({ field }) => (
              <Select
                value={
                  currentInstalment?.toString() ||
                  defaultInstalment?.installments.toString() ||
                  ""
                }
                onValueChange={value => {
                  const instalment = instalments.find(
                    i => i?.installments.toString() === value
                  );
                  setSelectedInstalmentDescription(
                    instalment ? instalment.description : ""
                  );
                  setSelectedInstallment(value);
                  setCurrentInstalment(Number(value));
                  field.onChange(Number(value));
                }}
              >
                <SelectTrigger
                  data-testid="installment-select-trigger"
                  aria-label="Installment"
                  className={cn(
                    errors.instalment
                      ? "ring-text-destructive focus:ring-text-destructive border-destructive"
                      : "border-input focus:ring-ring"
                  )}
                >
                  <SelectValue placeholder={t("select_installment")} />
                </SelectTrigger>
                <SelectContent>
                  {instalments.map((instalment, index) => (
                    <SelectItem
                      data-testid="installment-select-item"
                      key={index}
                      value={instalment?.installments.toString() ?? ""}
                    >
                      {instalment?.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          <div className="mt-[-10px]">
            <FormError errors={errors} fieldName="instalment" />
          </div>
        </div>
      </div>
    </div>
  );
};
