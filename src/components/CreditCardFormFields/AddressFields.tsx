import cn from "classnames";
import { useTranslations } from "next-intl";
import React from "react";
import { Controller, useFormContext } from "react-hook-form";

import { useAddressStore } from "@/hooks/useAddressStore";
import { useBrazilianStates } from "@/hooks/useBrazilianStates";
import { useCepSearch } from "@/hooks/useCepSearch";
import { useHandleCEP } from "@/hooks/useHandleCEP";
import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { clearString } from "@/utils/stringUtils";

import { CEPInput } from "../CEPInput";
import { FormError } from "../FormError";
import { InputField } from "../InputField";
import { LoadingIndicator } from "../LoadingIndicator";
import {
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui";
import { Checkbox } from "../ui/Checkbox";

export const AddressFields = () => {
  const {
    control,
    formState: { errors },
    register,
    setValue,
    trigger,
  } = useFormContext<Partial<BuyerFormValuesPartial>>();
  const t = useTranslations("buyer_form");

  const { data: states } = useBrazilianStates("bra");
  const { handleCEPChange } = useHandleCEP(setValue, trigger);

  const {
    showFields,
    setShowFields,
    hasNumber,
    streetNumber,
    setHasNumber,
    setStreetNumber,
    streetNumberError,
    setStreetNumberError,
  } = useAddressStore();

  const { searchCep, isSearching } = useCepSearch({
    setValue,
    trigger,
    states: states || [],
    setShowFields,
  });

  const onCEPChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleCEPChange(e);
    const cep = clearString(e.target.value);
    if (cep.length === 8) {
      searchCep(cep);
    }
  };

  const handleCheckboxChange = (checked: boolean) => {
    setHasNumber(!checked);
    if (checked && streetNumberError) {
      setStreetNumberError(false);
    }
  };

  return (
    <div className="mb-4 flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <CEPInput
          className="w-[130px]"
          type="text"
          label="CEP"
          name="billing_address.zipcode"
          register={register}
          errors={errors}
          placeholder="00000-000"
          handleCEPChange={onCEPChange}
        />
        {isSearching && <LoadingIndicator />}
      </div>

      {showFields && (
        <>
          <InputField
            type="text"
            label={t("street")}
            name="billing_address.street"
            register={register}
            errors={errors}
            placeholder={t("street_placeholder")}
          />

          <InputField
            type="text"
            label={t("district")}
            name="billing_address.district"
            register={register}
            errors={errors}
            placeholder={t("district_placeholder")}
          />

          <div className="mt-4 grid grid-cols-2 gap-x-4 gap-y-6">
            <div>
              <label htmlFor="streetNumber">{t("number")}</label>
              <Input
                id="streetNumber"
                type="number"
                value={streetNumber}
                onChange={e => setStreetNumber(e.target.value)}
                disabled={!hasNumber}
                placeholder={t("number_placeholder")}
                className={cn(streetNumberError && "text-destructive")}
              />
              {streetNumberError && (
                <span className="text-destructive">
                  {t("without_number_error")}
                </span>
              )}
              <div className="mt-2 flex items-center gap-2">
                <Checkbox
                  checked={!hasNumber}
                  onCheckedChange={handleCheckboxChange}
                />
                <label className="text-sm">{t("without_number")}</label>
              </div>
            </div>

            <InputField
              type="text"
              label={t("complement")}
              name="billing_address.complement"
              register={register}
              errors={errors}
              placeholder={t("complement_placeholder")}
            />
          </div>

          <div className="mt-4 grid grid-cols-2 gap-x-4 gap-y-6">
            <InputField
              type="text"
              label={t("city")}
              name="billing_address.city"
              register={register}
              errors={errors}
              placeholder={t("city_placeholder")}
            />

            <div>
              <div className="flex flex-col gap-1.5">
                <Label
                  className={cn(
                    errors.billing_address?.state && "text-destructive"
                  )}
                >
                  {t("state")}
                </Label>
                <Controller
                  name="billing_address.state"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={value => field.onChange(value)}
                    >
                      <SelectTrigger
                        className={cn(
                          "flex h-11 w-full items-center justify-between rounded-md border px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
                          errors.billing_address?.state
                            ? "ring-text-destructive focus:ring-text-destructive border-destructive"
                            : "border-input focus:ring-ring"
                        )}
                      >
                        <SelectValue placeholder={t("state_placeholder")} />
                      </SelectTrigger>
                      <SelectContent>
                        {states?.map(state => (
                          <SelectItem key={state.id} value={state.name}>
                            {state.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <FormError errors={errors} fieldName="billing_address.state" />
            </div>
          </div>
        </>
      )}
    </div>
  );
};
