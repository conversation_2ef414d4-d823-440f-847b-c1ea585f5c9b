"use client";

import { useState } from "react";

interface Props {
  url: string;
  fallbackUrl: string;
  className?: string;
  alt?: string;
}

export const ImageWithFallback = ({
  fallbackUrl,
  url,
  className,
  alt,
}: Props) => {
  const [loaded, setLoaded] = useState(false);

  return (
    <>
      <img
        src={fallbackUrl}
        style={{ ...(loaded && { display: "none" }) }}
        alt={alt}
        className={className}
      />
      <img
        src={url}
        style={{ ...(!loaded && { display: "none" }) }}
        onLoad={() => setLoaded(true)}
        alt={alt}
        className={className}
      />
    </>
  );
};
