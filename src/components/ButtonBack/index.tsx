"use client";

import { ArrowLeft } from "@carbon/icons-react";
import { useParams, useRouter } from "next/navigation";

import { buyerStore } from "@/store/buyerStore";

import { Button } from "../ui";

interface ButtonBackProps {
  text: string;
  orderId?: string;
  productId?: string;
  subscriptionId?: string;
}

export default function ButtonBack({
  text,
  orderId,
  productId,
  subscriptionId,
}: ButtonBackProps) {
  const router = useRouter();
  const params = useParams();

  const { setTokenizationError, setPaymentError } = buyerStore();

  return (
    <Button
      variant="link"
      className="flex gap-2 font-medium text-gray-900"
      onClick={() => {
        setTokenizationError(false);
        setPaymentError(false);

        if (subscriptionId) {
          return router.back();
        }

        router.push(`/${params.locale}/order/${orderId}?product=${productId}`);
      }}
    >
      <ArrowLeft size={18} />
      {text}
    </Button>
  );
}
