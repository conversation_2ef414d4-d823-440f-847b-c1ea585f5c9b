"use client";

import { get } from "lodash";
import { Control, Controller, FieldErrors, Path } from "react-hook-form";
import { z, ZodSchema } from "zod";

import { getDocumentHandler } from "@/factories/documentHandlerFactory";
import { DocumentType } from "@/types/document";
import { clearString } from "@/utils/stringUtils";

import { FormError } from "../FormError";
import { Input } from "../ui/Input";
import { Label } from "../ui/Label";

interface DocumentInputProps<T extends ZodSchema> {
  type: string;
  label?: string;
  name: Path<z.infer<T>>;
  control: Control<z.infer<T>>;
  errors: FieldErrors<z.infer<T>>;
  placeholder?: string;
}

export function DocumentInput<T extends ZodSchema>({
  type,
  label,
  name,
  control,
  errors,
  placeholder,
}: DocumentInputProps<T>) {
  const inputId = `input-${name}`;
  const fieldError = get(errors, name);
  const hasError = Boolean(fieldError);

  return (
    <div>
      <div className="flex flex-col gap-1.5">
        {label && (
          <Label
            className={hasError ? "text-destructive" : ""}
            htmlFor={inputId}
          >
            {label}
          </Label>
        )}
        <Controller
          control={control}
          name={name}
          render={({ field: { value, onChange } }) => (
            <Input
              className={hasError ? "border-destructive" : ""}
              hasError={hasError}
              id={inputId}
              placeholder={placeholder}
              type={type}
              value={value?.number}
              onChange={e => {
                const inputValue = e.target.value;

                if (!inputValue) {
                  onChange({ number: "", type: undefined });
                  return;
                }

                const cleanValue = clearString(inputValue);

                const documentTypes = [DocumentType.CPF, DocumentType.CNPJ];
                let documentType: DocumentType = DocumentType.CPF;
                let formattedValue = cleanValue;

                for (const type of documentTypes) {
                  const { validate, format } = getDocumentHandler(type);
                  if (validate(cleanValue)) {
                    documentType = type;
                    formattedValue = format(cleanValue);
                    break;
                  }
                }

                onChange({ number: formattedValue, type: documentType });
                e.target.value = formattedValue;
              }}
            />
          )}
        />
      </div>
      <FormError errors={errors} fieldName={name} />
    </div>
  );
}
