import { useTranslations } from "next-intl";

import { InfoPixCard } from "../InfoPixCard";

export function PixCard() {
  const t = useTranslations("buyer_form");
  const pixItems = [
    {
      text: t("immediate_approval"),
      iconSrc: "/images/check-icon.svg",
      iconAlt: "Check",
    },
    {
      text: t("secure_transaction_bcb"),
      iconSrc: "/images/check-icon.svg",
      iconAlt: "Check",
    },
    {
      text: t("easy_payment_via_bank_app"),
      iconSrc: "/images/check-icon.svg",
      iconAlt: "Check",
    },
  ];

  return <InfoPixCard items={pixItems} />;
}
