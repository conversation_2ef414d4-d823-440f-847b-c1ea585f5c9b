"use client";

import { ChevronSort } from "@carbon/icons-react";
import { CheckIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import * as React from "react";
import { useEffect, useState } from "react";
import { FieldErrors } from "react-hook-form";
import * as RPNInput from "react-phone-number-input";
import { getCountries, getCountryCallingCode } from "react-phone-number-input";
import flags from "react-phone-number-input/flags";
import { z, ZodSchema } from "zod";

import { cn } from "@/lib/utils";
import { SavedData } from "@/types/savedData";

import { Button } from "../Button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../Command";
import { Input, InputProps } from "../Input";
import { Label } from "../Label";
import { Popover, PopoverContent, PopoverTrigger } from "../Popover";
import { ScrollArea } from "../ScrollArea";

type PhoneInputProps<T extends ZodSchema> = Omit<
  React.InputHTMLAttributes<HTMLInputElement>,
  "onChange" | "value"
> &
  Omit<RPNInput.Props<typeof RPNInput.default>, "onChange"> & {
    onChange?: (value: RPNInput.Value) => void;
    label: string;
    errors: FieldErrors<z.infer<T>>;
  };

const PhoneInput = React.forwardRef<
  React.ElementRef<typeof RPNInput.default>,
  PhoneInputProps<ZodSchema>
>(({ className, onChange, label, errors, ...props }, ref) => {
  const InputComponent = React.forwardRef<HTMLInputElement, InputProps>(
    ({ className, ...props }, ref) => (
      <Input
        className={cn("rounded-e-lg rounded-s-none border-l-0", className)}
        hasError={errors?.phone?.message ? true : false}
        {...props}
        ref={ref}
      />
    )
  );
  InputComponent.displayName = "InputComponent";

  return (
    <div className="grid gap-1.5">
      <Label className={cn(errors?.phone?.message ? "text-destructive" : "")}>
        {label}
      </Label>
      <RPNInput.default
        className={cn("flex", className)}
        countrySelectComponent={CountrySelect}
        flagComponent={FlagComponent}
        ref={ref}
        inputComponent={InputComponent}
        /**
         * Handles the onChange event.
         *
         * react-phone-number-input might trigger the onChange event as undefined
         * when a valid phone number is not entered. To prevent this,
         * the value is coerced to an empty string.
         *
         * @param {E164Number | undefined} value - The entered value
         */
        onChange={value => onChange?.(value as RPNInput.Value)}
        {...props}
      />
    </div>
  );
});
PhoneInput.displayName = "PhoneInput";

type CountrySelectOption = { label: string; value: RPNInput.Country };

type CountrySelectProps = {
  disabled?: boolean;
  value: RPNInput.Country;
  onChange: (value: RPNInput.Country) => void;
  options: CountrySelectOption[];
};

const CountrySelect = ({
  disabled,
  value,
  onChange,
  options,
}: CountrySelectProps) => {
  const handleSelect = React.useCallback(
    (country: RPNInput.Country) => {
      onChange(country);
    },
    [onChange]
  );

  const t = useTranslations("home");

  const [savedData, setSavedData] = useState<SavedData | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedData = sessionStorage.getItem("buyerData");
      setSavedData(savedData ? JSON.parse(savedData) : null);
    }
  }, []);

  const parsedData = savedData;

  const mapDDIToCountry = (ddi: string | undefined) => {
    if (!ddi) return "BR";

    const formattedDDI = ddi.replace("+", "");
    const countries = getCountries();

    for (const country of countries) {
      if (getCountryCallingCode(country) === formattedDDI) {
        return country;
      }
    }

    return "BR";
  };

  const ddi = parsedData?.phone?.ddi ?? "BR";

  const countryFromDDI = mapDDIToCountry(ddi);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className={cn("flex gap-1 rounded-e-none rounded-s-lg px-3")}
          disabled={disabled}
          type="button"
          variant={"outline"}
        >
          <FlagComponent
            country={value ?? countryFromDDI}
            countryName={value ?? countryFromDDI}
          />
          <span>{`+${RPNInput.getCountryCallingCode(value ?? countryFromDDI)}`}</span>
          <ChevronSort
            className={cn(
              "-mr-2 h-4 w-4 opacity-50",
              disabled ? "hidden" : "opacity-100"
            )}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="ml-[55px] w-[280px] p-0 sm:ml-[220px] sm:w-[300px]">
        <Command>
          <CommandList>
            <ScrollArea className="h-72">
              <CommandInput placeholder="Search country..." />
              <CommandEmpty>{t("country_not_supported")}</CommandEmpty>
              <CommandGroup>
                {options
                  .filter(x => x.value)
                  .map(option => (
                    <CommandItem
                      className="gap-2"
                      key={option.value}
                      onSelect={() => handleSelect(option.value)}
                    >
                      <FlagComponent
                        country={option.value}
                        countryName={option.label}
                      />
                      <span className="flex-1 text-sm">{option.label}</span>
                      {option.value && (
                        <span className="text-sm text-foreground/50">{`+${RPNInput.getCountryCallingCode(option.value)}`}</span>
                      )}
                      <CheckIcon
                        className={cn(
                          "ml-auto h-4 w-4",
                          option.value === value ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  ))}
              </CommandGroup>
            </ScrollArea>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

const FlagComponent = ({ country, countryName }: RPNInput.FlagProps) => {
  const Flag = flags[country];

  return (
    <span className="flex h-4 w-6 overflow-hidden rounded-sm bg-foreground/20">
      {Flag && <Flag title={countryName} />}
    </span>
  );
};
FlagComponent.displayName = "FlagComponent";

export { PhoneInput };
