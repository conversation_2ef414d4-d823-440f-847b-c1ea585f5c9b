"use client";

import * as SeparatorPrimitive from "@radix-ui/react-separator";
import * as React from "react";

import { cn } from "@/lib/utils";

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root> & {
    "data-testid"?: string;
  }
>(
  (
    {
      className,
      orientation = "horizontal",
      decorative = true,
      "data-testid": testId,
      ...props
    },
    ref
  ) => (
    <SeparatorPrimitive.Root
      className={cn(
        "shrink-0 bg-border",
        orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
        className
      )}
      data-testid={testId}
      decorative={decorative}
      orientation={orientation}
      ref={ref}
      role={decorative ? "none" : "separator"}
      {...props}
    />
  )
);

Separator.displayName = SeparatorPrimitive.Root.displayName;

export { Separator };
