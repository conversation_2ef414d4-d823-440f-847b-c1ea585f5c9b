"use client";

import { Launch } from "@carbon/icons-react";
import React from "react";

interface PulsatingButtonProps {
  children: React.ReactNode;
  pulseColor: string;
  backgroundColor: string;
  textColor: string;
  animationDuration: string;
  buttonWidth: string;
  buttonHeight: string;
  className?: string;
  onClick?: () => void;
}

export const PulsatingButton: React.FC<PulsatingButtonProps> = ({
  children,
  pulseColor,
  backgroundColor,
  textColor,
  animationDuration,
  buttonWidth,
  buttonHeight,
  className,
  onClick,
}) => {
  const pulseKeyframes = {
    "--tw-pulse-color": pulseColor,
    animation: `pulse ${animationDuration} linear infinite`,
  };

  return (
    <div className="flex items-center justify-center">
      <button
        data-testid="pulsating-button"
        onClick={onClick}
        className={`relative flex cursor-pointer items-center justify-center text-center ${className}`}
        style={{
          color: textColor,
          backgroundColor,
          width: buttonWidth,
          height: buttonHeight,
          ...pulseKeyframes,
        }}
      >
        <div className="flex items-center justify-center gap-2 text-sm font-medium leading-5">
          {children}
          <Launch size={18} />
        </div>
        <style>{`
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(var(--tw-pulse-color), 0);
            }
            50% {
              box-shadow: 0 0 0 8px rgba(var(--tw-pulse-color), 0.5);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(var(--tw-pulse-color), 0);
            }
          }
          button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            border-radius: 20px;
            background: inherit;
            animation: inherit;
            transform: translate(-50%, -50%);
            z-index: -1;
          }
        `}</style>
      </button>
    </div>
  );
};

export default PulsatingButton;
