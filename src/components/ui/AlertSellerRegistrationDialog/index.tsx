import { useTranslations } from "next-intl";

import { Dialog, DialogContent } from "../Dialog";

interface AlertSellerRegistrationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  setShowModal: (value: boolean) => void;
}

export function AlertSellerRegistrationDialog({
  isOpen,
  onClose,
  setShowModal,
}: AlertSellerRegistrationDialogProps) {
  const t = useTranslations("buyer_form");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[350px] sm:w-[440px]">
        <div className="flex flex-col items-start gap-4 p-2">
          <div className="text-yellow-500">
            <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
              <path d="M24 4L2 44H46L24 4Z" fill="currentColor" />
              <path
                d="M24 36H24.02M24 16V28"
                stroke="white"
                strokeWidth="4"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <h2 className="text-xl font-bold">
            {t("seller_registration_error_title")}
          </h2>
          <p className="text-sm">
            {t("seller_registration_error_description")}
          </p>
          <button
            onClick={() => setShowModal(false)}
            className="self-end rounded-lg bg-gray-100 px-6 py-2"
          >
            {t("understood")}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
