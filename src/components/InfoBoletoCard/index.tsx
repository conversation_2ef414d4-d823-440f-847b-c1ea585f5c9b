"use client";

import { CloseFilled } from "@carbon/icons-react";
import Image from "next/image";
import { useTranslations } from "next-intl";

import { buyerStore } from "@/store/buyerStore";

import { AlertBox } from "../AlertBox";

interface InfoBoletoCardProps {
  items: { text: string; iconSrc: string; iconAlt: string }[];
}

export function InfoBoletoCard({ items }: InfoBoletoCardProps) {
  const t = useTranslations("buyer_form");
  const { failedBoletoInformation, boletoError } = buyerStore();
  return (
    <div className="flex flex-col gap-4">
      {(failedBoletoInformation || boletoError) && (
        <AlertBox
          title={
            failedBoletoInformation?.title ?? t("tokenization_error_title")
          }
          description={
            failedBoletoInformation?.description ??
            t("tokenization_error_description")
          }
          icon={<CloseFilled size={20} color="#D80027" />}
        />
      )}
      {items.map((item, index) => (
        <div key={index} className="flex items-start gap-2 sm:items-center">
          <Image src={item.iconSrc} alt={item.iconAlt} width={20} height={20} />
          <p className="text-sm font-medium leading-4 text-gray-900">
            {item.text}
          </p>
        </div>
      ))}
    </div>
  );
}
