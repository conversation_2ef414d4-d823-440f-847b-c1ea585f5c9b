import { FieldErrors, Path, UseFormRegister } from "react-hook-form";
import { ZodSchema } from "zod";
import { z } from "zod";

import { InputField } from "../InputField";

export interface InputCEPProps<T extends ZodSchema> {
  type: string;
  label?: string;
  name: Path<z.infer<T>>;
  register: UseFormRegister<z.infer<T>>;
  errors: FieldErrors<z.infer<T>>;
  placeholder?: string;
  handleCEPChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  className?: string;
}

export function CEPInput<T extends ZodSchema>({
  type,
  label,
  name,
  register,
  errors,
  placeholder,
  handleCEPChange,
  disabled,
  className,
}: InputCEPProps<T>) {
  return (
    <InputField
      data-testid="cep-input"
      disabled={disabled}
      type={type}
      label={label}
      name={name}
      register={register}
      errors={errors}
      onChange={handleCEPChange}
      placeholder={placeholder}
      className={className}
      maxLength={9}
    />
  );
}
