"use client";

import Image from "next/image";
import { useMemo } from "react";

import { OrderPaymentResponse } from "@/types/successPage";

const brandMapping = {
  "american express": "/brands/American Express (Amex).svg",
  "diners club": "/brands/Diners Club.svg",
  discover: "/brands/Discover.svg",
  elo: "/brands/Elo.svg",
  jcb: "/brands/JCB.svg",
  mastercard: "/brands/MasterCard.svg",
  visa: "/brands/Visa.svg",
  default: "/brands/defaultcard.svg",
} as const;

interface PaymentInfoProps {
  data: OrderPaymentResponse;
}

export function PaymentInfoSuccessCreditCard({ data }: PaymentInfoProps) {
  const brandImageUrl = useMemo(() => {
    if (!data?.payment?.brand) return brandMapping.default;

    const normalizedBrand = data.payment.brand.toLowerCase();
    return data.payment.brand_url_image?.trim()
      ? data.payment.brand_url_image
      : brandMapping[normalizedBrand as keyof typeof brandMapping] ||
          brandMapping.default;
  }, [data?.payment]);

  return (
    <div className="flex items-center gap-1">
      <Image alt="Card brand" height={30} src={brandImageUrl} width={30} />
      <p className="text-sm font-medium leading-4 text-gray-900">
        *{data?.payment?.last_digits}
      </p>
    </div>
  );
}
