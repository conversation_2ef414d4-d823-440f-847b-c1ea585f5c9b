"use client";

import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";

interface TrialInfoProps {
  className?: string;
  subscription: {
    periodicity: string;
    interval: number;
    trial_days?: number;
  };
}

export function TrialInfo({ className = "", subscription }: TrialInfoProps) {
  const t = useTranslations("buyer_form");

  return (
    <div className={cn("", className)}>
      {!!subscription.trial_days && subscription.trial_days > 0 && (
        <button className="inline-block rounded-full bg-emerald-700 px-2 py-1 text-xs leading-none text-white">
          {t("free_trial", {
            days: subscription.trial_days,
          })}
        </button>
      )}
    </div>
  );
}
