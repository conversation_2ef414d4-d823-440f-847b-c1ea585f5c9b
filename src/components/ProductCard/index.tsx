"use client";

import { useTranslations } from "next-intl";
import { useMemo } from "react";

import { Bump, Installment } from "@/types/instalmentsList";
import { Product, ProductPeriodicity } from "@/types/product";
import { SubscriptionProductForComponents } from "@/types/subscription";
import formatToBRL from "@/utils/formatting/formatToBRL";
import {
  formatSubscriptionInterval,
  formatSubscriptionPeriodicity,
} from "@/utils/formatting/subscriptionFormatting";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";

interface ProductInstallments {
  installments: Installment[];
  bumps: Bump[];
  isSubscriptionPage?: boolean;
}

export default function ProductCard({
  product,
  productInstallments,
  isSubscriptionPage = false,
}: {
  product: Product | SubscriptionProductForComponents;
  productInstallments: ProductInstallments;
  isSubscriptionPage?: boolean;
}) {
  const translations = useTranslations("buyer_form");

  const hasSubscriptionProduct = useMemo(
    () => product?.subscription,
    [product?.subscription]
  );
  const hasFreeTrial = useMemo(
    () =>
      product?.subscription?.trial_days &&
      product?.subscription?.trial_days > 0,
    [product.subscription?.trial_days]
  );

  const formattedPrice = useMemo(
    () => formatToBRL((product?.price ?? 0) / CENTS_IN_REAL),
    [product?.price]
  );

  const subscriptionInterval = useMemo(() => {
    if (!hasSubscriptionProduct) return null;
    return formatSubscriptionInterval(
      product?.subscription?.interval ?? 0,
      product?.subscription?.periodicity ?? ProductPeriodicity.MONTHLY,
      translations
    );
  }, [
    hasSubscriptionProduct,
    product?.subscription?.interval,
    product?.subscription?.periodicity,
    translations,
  ]);

  const subscriptionPeriodicity = useMemo(() => {
    if (!hasSubscriptionProduct) return null;
    return formatSubscriptionPeriodicity(
      product?.subscription?.interval ?? 0,
      product?.subscription?.periodicity ?? ProductPeriodicity.MONTHLY,
      translations
    );
  }, [
    hasSubscriptionProduct,
    product?.subscription?.interval,
    product?.subscription?.periodicity,
    translations,
  ]);

  const installmentDescription = useMemo(() => {
    return productInstallments?.installments[
      productInstallments?.installments?.length - 1
    ]?.description;
  }, [productInstallments?.installments]);

  if (isSubscriptionPage) {
    return (
      <div className="flex-1" role="group">
        <h2 className="mb-2 font-semibold leading-5 text-gray-900">
          {product?.title ?? "Título do produto"}
        </h2>
        <p className="mb-1 text-xl font-semibold leading-5 text-blue-700">
          {formattedPrice}
        </p>
        {hasSubscriptionProduct && (
          <p className="mb-2 text-sm text-gray-500">{subscriptionInterval}</p>
        )}
        <p className="inline-block rounded-full bg-gray-100 px-2 py-1 text-sm text-black">
          <span className="mr-1 text-sm text-gray-500">
            {translations("author")}
          </span>
          {product?.platform?.name ?? "Plataforma"}
        </p>
      </div>
    );
  }

  return (
    <div className="flex-1 md:w-[450px]" role="group">
      <h2 className="mb-2 font-semibold leading-5">
        {product?.title ?? "Título do produto"}
      </h2>
      {!!hasFreeTrial &&
        product?.subscription?.trial_days &&
        product?.subscription?.trial_days > 0 && (
          <button className="mb-[8px] inline-block rounded-full bg-emerald-700 px-2 py-1 text-xs leading-none text-white">
            {translations("free_trial", {
              days: product?.subscription?.trial_days,
            })}
          </button>
        )}
      <div className="flex items-center gap-1">
        <p className="mb-1 text-xl font-semibold leading-5 text-blue-700">
          {installmentDescription}
        </p>
        {hasSubscriptionProduct && (
          <p className="text-xs text-gray-500">{subscriptionPeriodicity}</p>
        )}
      </div>
      <div>
        <p className="mb-2.5 text-xs text-gray-500">
          {translations("or")} {formattedPrice} {translations("at_view")}
        </p>
      </div>
      <p className="inline-block rounded-full bg-gray-100 px-2 py-1 text-sm text-black">
        <span className="mr-1 text-sm text-gray-500">
          {translations("author")}
        </span>
        {product?.platform?.name ?? "Plataforma"}
      </p>
    </div>
  );
}
