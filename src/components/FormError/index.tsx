import { get } from "lodash";
import { type FieldErrors, type FieldValues } from "react-hook-form";

import { cn } from "@/lib/utils";

type Props = React.HTMLAttributes<HTMLDivElement> & {
  fieldName: string;
  errors: FieldErrors<FieldValues>;
};

export const FormError = ({ fieldName, errors, ...props }: Props) => {
  const directErrorMessage = get(errors, `${fieldName}.message`);

  const nestedErrorMessage = get(errors, `${fieldName}.number.message`);

  const errorMessage = directErrorMessage || nestedErrorMessage;

  if (!errorMessage) return null;

  return (
    <div
      data-testid={`error-${fieldName}`}
      {...props}
      className={cn(
        "mt-1 flex items-center text-sm text-destructive",
        props.className
      )}
    >
      <span>{String(errorMessage)}</span>
    </div>
  );
};
