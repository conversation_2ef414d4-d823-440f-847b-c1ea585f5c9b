"use client";

import { capitalize } from "lodash";
import { useTranslations } from "next-intl";

import Header from "@/components/HeaderCreditCard";
import HelpSection from "@/components/HelpSection";
import { ImageWithFallback } from "@/components/ImageWithFallback";
import ProductInfo from "@/components/ProductInfo";
import { Separator } from "@/components/ui";
import { brandMapping } from "@/hooks/useCreditCardFormatter";
import {
  AwaitingPaymentOrder,
  AwaitingPaymentSubscription,
} from "@/types/awaitingPayment";

interface Props {
  data: AwaitingPaymentOrder | AwaitingPaymentSubscription;
  id: string;
}

export function CreditCardPage({ data, id }: Props) {
  const t = useTranslations("credit_card_page");

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-accent p-5">
      <div className="w-full max-w-[678px] md:mt-12">
        <Header />
        <main className="h-auto w-full rounded-b-lg bg-white p-8">
          <div className="flex flex-col-reverse justify-between md:flex-row">
            <p className="w-full leading-6 text-gray-900">{t("description")}</p>
          </div>
          <div className="mt-6 flex flex-col gap-4 border p-5">
            <p className="text-gray-500">
              <span className="text-sm text-foreground">{t("order")}</span> #
              {id}
            </p>
            <Separator />
            <ProductInfo
              className="mt-0 border-none p-0"
              infoData={data?.product}
              additionalProductInfo={data?.bumps}
            />
            <Separator />
            <div className="grid grid-cols-1 gap-1 text-sm sm:grid-cols-2">
              <span className="text-gray-500">{t("payment_method")}</span>
              <div className="flex items-center gap-2 sm:justify-self-end">
                <ImageWithFallback
                  url={
                    brandMapping[
                      data?.credit_card?.brand as keyof typeof brandMapping
                    ]
                  }
                  fallbackUrl={brandMapping.default}
                  className="h-5 w-fit"
                  alt="Credit Card Brand"
                />
                <p className="font-medium text-foreground">
                  {capitalize(data?.credit_card?.brand)} *
                  {data?.credit_card?.last_digits}
                </p>
              </div>
              <span className="text-gray-500">{t("value")}</span>
              <p className="font-medium text-emerald-700 sm:justify-self-end">
                {data?.transaction?.description}
              </p>
            </div>
          </div>
          <div className="mt-6">
            <HelpSection />
          </div>
        </main>
      </div>
    </div>
  );
}
