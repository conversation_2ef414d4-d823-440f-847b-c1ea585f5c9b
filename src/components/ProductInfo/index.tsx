"use client";

import dayjs from "dayjs";
import { useTranslations } from "next-intl";

import { TrialInfo } from "@/components/SubscriptionInfo";
import { cn } from "@/lib/utils";
import { ProductInfoProps } from "@/types/productInfo";
import { formatSubscriptionInterval } from "@/utils/formatting/subscriptionFormatting";

export default function ProductInfo({
  className = "",
  infoData,
  additionalProductInfo,
}: ProductInfoProps) {
  const t = useTranslations("pix_page");
  const tInfoTrial = useTranslations("buyer_form");
  const subscriptionInterval = formatSubscriptionInterval(
    infoData?.subscription?.interval ?? 1,
    infoData?.subscription?.periodicity as "monthly",
    tInfoTrial
  );

  return (
    <div className={cn("mt-6 border p-5", className)}>
      <p className="mb-3 text-xs font-medium text-gray-500">{t("product")}</p>
      <div className="flex items-center gap-2">
        <p className="font-medium leading-5 text-gray-900">{infoData?.title}</p>
        {infoData?.charge_type === "subscription" && infoData?.subscription && (
          <TrialInfo subscription={infoData.subscription} />
        )}
      </div>
      {infoData?.charge_type === "subscription" && infoData?.subscription && (
        <div className="flex flex-col gap-1 text-sm text-gray-600">
          <p>{subscriptionInterval}</p>
          <p className="text-gray-500">
            {tInfoTrial("first_charge_on")}{" "}
            {dayjs(infoData.subscription.next_billing_at).format("DD/MM/YYYY")}
          </p>
        </div>
      )}
      <p className="mt-2 inline-block rounded-full bg-gray-100 px-2 py-1 text-sm text-black">
        <span className="mr-1 text-sm text-gray-400">{t("author")}</span>
        {infoData?.platform?.name}
      </p>

      {additionalProductInfo && additionalProductInfo.length > 0 && (
        <>
          <div className="my-6 h-[1px] w-full bg-gray-200" />
          <p className="mb-3 text-xs font-medium text-gray-500">
            {t("additional_items")}
          </p>
          <div className="flex flex-col gap-y-3">
            {additionalProductInfo?.map((bump, index) => (
              <div key={index} className="flex items-center gap-2">
                <span className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-50 text-sm font-medium text-blue-700">
                  {index + 1}
                </span>
                <p className="text-sm font-medium text-gray-900">
                  {bump?.title}
                </p>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
