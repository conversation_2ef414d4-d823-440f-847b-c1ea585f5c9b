"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import { useMemo } from "react";

import { PaymentOption } from "@/types/paymentOptions";
import { InfoData } from "@/types/productInfo";
import { OrderPaymentResponse } from "@/types/successPage";

import { PaymentInfoSuccessCreditCard } from "../PaymentInfoSuccessCreditCard";
import ProductInfo from "../ProductInfo";
import { Separator } from "../ui";
import { PulsatingButton } from "../ui/PulsatingButton";

export const ProductOrderDetails = ({
  data,
}: {
  data: OrderPaymentResponse;
}) => {
  const t = useTranslations("success");

  const isTrialProduct = useMemo(() => {
    return (
      data?.product?.charge_type === "subscription" &&
      data?.product?.subscription?.trial_days !== null &&
      data?.product?.subscription?.trial_days &&
      data?.product?.subscription?.trial_days > 0 &&
      (data?.payment?.status === "trialed" || data?.subscription_id)
    );
  }, [data]);

  const displayPaymentValue = useMemo(() => {
    if (isTrialProduct) {
      return "R$ 0,00";
    }
    return data?.payment?.description;
  }, [isTrialProduct, data?.payment?.description]);

  const handleButtonClick = () => {
    const urlToOpen = data?.product?.platform?.url;
    window.open(urlToOpen, "_blank");
  };

  return (
    <div className="border-2 p-5">
      <div className="flex items-center gap-1">
        <p className="text-sm font-medium leading-4 text-gray-900">
          {t("product_order")}{" "}
        </p>

        <span className="text-sm leading-5 text-gray-500">
          #{data?.order_id}
        </span>
      </div>
      <Separator className="my-2 h-0.5" />
      <ProductInfo
        infoData={data?.product as unknown as InfoData}
        additionalProductInfo={data?.bumps}
        className="mt-[10px] border-none p-[1px]"
      />
      <Separator className="my-3 h-0.5" />
      <div className="flex items-center justify-between">
        <p className="text-sm leading-5 text-gray-600">{t("payment_info")}</p>
        {data?.payment_method === PaymentOption.CreditCard && (
          <PaymentInfoSuccessCreditCard data={data} />
        )}
        {data?.payment_method === PaymentOption.Pix && (
          <div className="flex items-center gap-2">
            <Image alt="Pix" height={30} src="/images/pix.svg" width={30} />
            <p className="text-sm font-medium leading-4 text-gray-900">
              {t("payment_info_success")}
            </p>
          </div>
        )}
        {data?.payment_method === PaymentOption.Boleto && (
          <div className="flex items-center gap-2">
            <Image
              alt="Boleto"
              height={30}
              src="/images/boleto.svg"
              width={30}
            />
            <p className="text-sm font-medium leading-4 text-gray-900">
              {t("payment_info_boleto")}
            </p>
          </div>
        )}
      </div>
      <div className="mt-1 flex items-center justify-between">
        <p className="text-sm leading-5 text-gray-600">
          {t("payment_info_value")}
        </p>
        <span className="text-sm font-medium leading-4 text-emerald-700">
          {displayPaymentValue}
        </span>
      </div>
      <div className="mt-4">
        <PulsatingButton
          pulseColor="26, 71, 247"
          backgroundColor="#1A47F7"
          textColor="#ffffff"
          animationDuration="1.5s"
          buttonWidth="566px"
          buttonHeight="40px"
          onClick={handleButtonClick}
        >
          {t("access_product")}
        </PulsatingButton>
      </div>
    </div>
  );
};
