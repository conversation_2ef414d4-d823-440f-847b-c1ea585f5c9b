"use client";

import { useTranslations } from "next-intl";
import { useState } from "react";

import { Checkbox } from "@/components/ui/Checkbox";
import { ProductBump, ProductChargeType } from "@/types/product";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { formatSubscriptionInterval } from "@/utils/formatting/subscriptionFormatting";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";

interface OrderBumpPreviewProps {
  bump: ProductBump;
  selectedInstallmentBump?: string;
  installmentsDescription?: { [key: string]: string };
  onSelect: (isSelected: boolean) => void;
}

export const OrderBumpPreview = ({
  bump,
  selectedInstallmentBump,
  installmentsDescription,
  onSelect,
}: OrderBumpPreviewProps) => {
  const t = useTranslations("buyer_form");
  const [isChecked, setIsChecked] = useState(false);

  const handleSelect = (checked: boolean) => {
    setIsChecked(checked);
    onSelect(checked);
  };

  const isSubscription = bump.charge_type === ProductChargeType.SUBSCRIPTION;

  const buttonTextMap = {
    subscription: {
      checked: t("remove_subscription"),
      unchecked: t("add_subscription"),
    },
    product: {
      checked: t("remove_product"),
      unchecked: t("add_product"),
    },
  };

  const getButtonText = () => {
    const type = isSubscription ? "subscription" : "product";
    const state = isChecked ? "checked" : "unchecked";
    return buttonTextMap[type][state];
  };

  const displayPrice = () => {
    if (selectedInstallmentBump && installmentsDescription) {
      const bumpInstallmentDescription = installmentsDescription[bump.id];
      return bumpInstallmentDescription;
    }
    const formattedPrice = formatToBRL(bump.price / CENTS_IN_REAL);
    return formattedPrice;
  };

  return (
    <div className="mt-4" data-bump-id={bump.id}>
      <div className="overflow-hidden">
        {bump.cta && (
          <div className="bg-blue-700 p-4">
            <p className="font-medium leading-none text-white">{bump.cta}</p>
          </div>
        )}

        <div
          className={`border-l border-r border-green-500/50 p-4 ${!bump.cta ? "border-t border-green-500/50" : ""} border-b-0`}
        >
          <div className="flex flex-col gap-3">
            <div className="flex gap-1">
              <p className="break-words text-sm text-gray-800">
                {bump.title}
                {bump.description && (
                  <span className="text-gray-600">
                    {" "}
                    {`- ${bump.description}`}
                  </span>
                )}
              </p>
            </div>
            <p className="text-sm font-medium text-green-700">
              {displayPrice()}
            </p>
            {isSubscription && bump.subscription && (
              <p className="text-xs text-gray-500">
                {formatSubscriptionInterval(
                  bump.subscription.interval,
                  bump.subscription.periodicity,
                  t
                )}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2 border-x border-b border-green-500/50 bg-green-50 p-4">
          <Checkbox
            onCheckedChange={checked => handleSelect(checked as boolean)}
            className="h-4 w-4 rounded-none border border-green-500 data-[state=checked]:border-green-500 data-[state=checked]:bg-green-500"
            data-testid={`bump-checkbox-${bump.id}`}
          />
          <p className="text-sm font-medium">{getButtonText()}</p>
        </div>
      </div>
    </div>
  );
};
