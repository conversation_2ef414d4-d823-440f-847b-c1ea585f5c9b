"use client";

import { useTranslations } from "next-intl";

export default function PaymentInstructions({
  hiddenOnSmall,
}: {
  hiddenOnSmall: boolean;
}) {
  const t = useTranslations("pix_page");
  return (
    <div
      className={`w-full ${hiddenOnSmall ? "sm:hidden" : "hidden sm:block"}`}
    >
      <h6 className="text-sm font-medium leading-4 text-gray-900">
        {t("payment_instructions")}
      </h6>
      <ol className="ml-2 mt-2 list-decimal pl-4 text-sm leading-5 text-gray-600">
        <li>{t("pix_app_instruction_1")}</li>
        <li>{t("pix_app_instruction_2")}</li>
        <li>{t("pix_app_instruction_3")}</li>
      </ol>
    </div>
  );
}
