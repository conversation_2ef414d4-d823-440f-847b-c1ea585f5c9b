"use client";

import Image from "next/image";
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState, useTransition } from "react";

import { Select, SelectContent, SelectItem, SelectTrigger } from "../ui/Select";

export function LanguageSwitcher({
  isRetryOrderPage = false,
}: {
  isRetryOrderPage?: boolean;
}) {
  const t = useTranslations("language_switcher");
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const currentLocale = useLocale();
  const [selectedLanguage, setSelectedLanguage] = useState(t("portuguese"));
  const [isLoadingFlag, setIsLoadingFlag] = useState(false);
  const params = useParams();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  useEffect(() => {
    const localeMap: Record<string, string> = {
      "en-US": t("english"),
      "es-ES": t("spanish"),
      "pt-BR": t("portuguese"),
    };

    setSelectedLanguage(localeMap[currentLocale]);
  }, [currentLocale, t]);

  const handleLanguageChange = (language: string, locale: string) => {
    setIsLoadingFlag(true);
    setSelectedLanguage(language);
    startTransition(() => {
      const isSubscriptionPage = pathname.includes("/subscriptions/");
      const isOrderPage = pathname.includes("/order/") || isRetryOrderPage;

      const buildQueryString = () => {
        const currentParams = new URLSearchParams();

        searchParams.forEach((value, key) => {
          currentParams.set(key, value);
        });

        const queryString = currentParams.toString();
        return queryString ? `?${queryString}` : "";
      };

      if (isOrderPage) {
        const queryString = buildQueryString();
        router.replace(`/${locale}/order/${params.id}${queryString}`);
      } else if (isSubscriptionPage) {
        const queryString = buildQueryString();
        router.replace(`/${locale}/subscriptions/${params.id}${queryString}`);
      } else {
        const queryString = buildQueryString();
        router.replace(`/${locale}/${params.id}${queryString}`);
      }
      setIsLoadingFlag(false);
    });
  };

  const languages = [
    {
      locale: "en-US",
      label: t("english"),
      flag: "/images/en.svg",
    },
    {
      locale: "pt-BR",
      label: t("portuguese"),
      flag: "/images/pt.svg",
    },
    {
      locale: "es-ES",
      label: t("spanish"),
      flag: "/images/es.svg",
    },
  ];

  const filteredLanguages = languages.filter(
    language => language.locale !== currentLocale
  );

  const flagMap: Record<string, string> = {
    [t("portuguese")]: "/images/pt.svg",
    [t("english")]: "/images/en.svg",
    [t("spanish")]: "/images/es.svg",
  };

  return (
    <Select
      onValueChange={locale => {
        const language =
          languages.find(l => l.locale === locale)?.label || t("english");
        handleLanguageChange(language, locale);
      }}
    >
      <SelectTrigger
        className="mb-6 flex w-full items-center justify-between gap-2 pr-3 text-sm sm:mb-0 md:w-40"
        data-testid="language-switcher-button"
        disabled={isPending}
      >
        {isLoadingFlag ? (
          <div className="loader h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-transparent"></div>
        ) : (
          <div className="flex items-center gap-2">
            <Image
              alt={selectedLanguage}
              height={16}
              src={flagMap[selectedLanguage]}
              style={{ aspectRatio: "24/16", objectFit: "cover" }}
              width={24}
              className="ml-0"
            />
            <span>{selectedLanguage}</span>
          </div>
        )}
      </SelectTrigger>
      <SelectContent>
        {filteredLanguages.map(language => (
          <SelectItem
            key={language.locale}
            value={language.locale}
            data-testid={`language-switcher-option-${language.locale}`}
            className="pl-2"
          >
            <div className="flex items-center gap-2">
              <Image
                alt={language.label}
                height={16}
                src={language.flag}
                style={{ aspectRatio: "24/16", objectFit: "cover" }}
                width={24}
                className="ml-0"
              />
              {language.label}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
