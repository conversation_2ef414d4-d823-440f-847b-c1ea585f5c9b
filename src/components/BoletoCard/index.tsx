import { useTranslations } from "next-intl";

import { InfoBoletoCard } from "../InfoBoletoCard";

export function BoletoCard() {
  const t = useTranslations("buyer_form");
  const boletoItems = [
    {
      text: t("pay_until_due_date"),
      iconSrc: "/images/information.svg",
      iconAlt: "Icone Boleto",
    },
    {
      text: t("payment_processing_time"),
      iconSrc: "/images/information.svg",
      iconAlt: "Icone Boleto",
    },
  ];

  return <InfoBoletoCard items={boletoItems} />;
}
