import Image from "next/image";
import { useTranslations } from "next-intl";

import PaymentInstructions from "../PaymentInstructions";

export function QRCodeSection({
  isVisibleOnSmall,
}: {
  isVisibleOnSmall: boolean;
}) {
  const t = useTranslations("pix_page");
  return (
    <div
      className={`mt-6 flex flex-wrap gap-6 ${isVisibleOnSmall ? "sm:hidden" : "hidden sm:block"}`}
    >
      <p className="text-sm leading-5 text-gray-900">
        {isVisibleOnSmall
          ? t("qr_code_instruction")
          : t("payment_instructions")}
      </p>
      <Image src="/images/qrcode.svg" alt="QR Code" width={200} height={200} />
      <PaymentInstructions hiddenOnSmall={!isVisibleOnSmall} />
    </div>
  );
}
