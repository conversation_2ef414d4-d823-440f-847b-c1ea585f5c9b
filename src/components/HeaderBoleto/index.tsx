"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

export default function HeaderBoleto() {
  const t = useTranslations("boleto_page");

  return (
    <header className="flex flex-col items-start justify-between rounded-t-lg bg-amber-400 p-8 sm:flex-row sm:items-center">
      <div className="flex items-start gap-4 sm:items-center">
        <div>
          <Image
            src="/images/barcode-dark.svg"
            alt="Boleto"
            width={40}
            height={40}
          />
        </div>
        <h5 className="text-2xl font-semibold leading-7">
          {t("payment_boleto")}
        </h5>
      </div>
    </header>
  );
}
