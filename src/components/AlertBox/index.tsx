import { cva, type VariantProps } from "class-variance-authority";
import React from "react";

import { cn } from "@/lib/utils";

const alertBoxVariants = cva("flex items-start p-4 rounded-md text-sm w-full", {
  variants: {
    variant: {
      primary: "bg-red-50 text-red-950",
      secondary: "bg-violet-50 text-violet-950",
    },
  },
  defaultVariants: {
    variant: "primary",
  },
});

interface AlertBoxProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertBoxVariants> {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
}

const AlertBox: React.FC<AlertBoxProps> = ({
  className,
  variant,
  title,
  description,
  icon,
  children,
  ...props
}) => {
  return (
    <div
      role="alert"
      className={cn(alertBoxVariants({ variant }), className)}
      {...props}
    >
      {icon && <div className="mr-3">{icon}</div>}
      <div>
        {title && (
          <div className="text-base font-semibold leading-5">{title}</div>
        )}
        {description && <div className="text-sm">{description}</div>}
        {children}
      </div>
    </div>
  );
};

export { AlertBox, alertBoxVariants };
