import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";

import { Separator } from "../ui/Separator";

interface Props {
  organization: string;
}

export function SubscriptionFooter({ organization }: Props) {
  const t = useTranslations("home");

  return (
    <div>
      <div className="mt-5 text-xs leading-4 text-gray-600">
        <p>
          {t("purchase_disclaimer", { organization })}{" "}
          <Link
            className="text-xs font-medium leading-4 text-blue-700"
            href="https://site.themembers.com.br/thebank/infoprodutor/termos-de-uso"
          >
            {t("privacy_policy")}
          </Link>{" "}
          {t("and")}{" "}
          <Link
            className="text-xs font-medium leading-4 text-blue-700"
            href="https://site.themembers.com.br/thebank/comprador/termos-de-uso"
          >
            {t("terms_of_use")}
          </Link>
          .
        </p>
        <p className="mb-4 mt-4 text-xs leading-4 text-gray-600">
          {t("purchase_disclaimer_credit_card")}
        </p>
        <p className="mt-2">
          {t("help_text")}{" "}
          <Link
            className="text-xs font-medium leading-4 text-blue-700"
            href="https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
          >
            {t("help_page")}
          </Link>
          .
        </p>
      </div>
      <Separator className="mb-8 mt-8" />
      <div>
        <Image
          src="/images/logo.svg"
          alt="Checkout TheMembers"
          width={100}
          height={30}
          className="mt-4"
        />
        <p className="mt-3 text-xs leading-4 text-gray-600">
          © 2025 - {t("all_rights_reserved")}
        </p>
      </div>
    </div>
  );
}
