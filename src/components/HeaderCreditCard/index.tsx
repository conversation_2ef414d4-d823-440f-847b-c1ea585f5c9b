"use client";

import { TimeFilled } from "@carbon/icons-react";
import { useTranslations } from "next-intl";

export default function HeaderCreditCard() {
  const t = useTranslations("credit_card_page");

  return (
    <header className="flex flex-col items-start justify-between rounded-t-lg bg-amber-400 p-8 sm:flex-row sm:items-center">
      <div className="flex items-start gap-4 sm:items-center">
        <TimeFilled className="h-10 w-10 text-foreground" />
        <h5 className="text-2xl font-semibold leading-7">{t("header")}</h5>
      </div>
    </header>
  );
}
