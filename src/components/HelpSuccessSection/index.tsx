import Link from "next/link";
import { useTranslations } from "next-intl";

interface HelpSuccessSectionProps {
  isSubscription?: boolean;
}

export const HelpSuccessSection = ({
  isSubscription = false,
}: HelpSuccessSectionProps) => {
  const t = useTranslations("success");

  if (isSubscription) {
    return (
      <div className="mb-6 mt-6 space-y-4">
        <p className="text-xs leading-5 text-gray-600">
          {t("subscription_help_message")}
        </p>
        <p className="text-xs leading-5 text-gray-600">
          {t("help_page")}{" "}
          <Link
            className="text-xs font-medium leading-4 text-blue-500"
            href="https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
          >
            {t("help_page_link")}
          </Link>
        </p>
      </div>
    );
  }

  return (
    <p className="mb-6 mt-6 text-xs leading-5 text-gray-600">
      {t("help_page")}{" "}
      <Link
        className="text-xs font-medium leading-4 text-blue-500"
        href="https://ajuda.themembers.com.br/hc/pt-br/categories/**************-TheBank-O-Checkout-da-TheMembers-Em-Breve"
      >
        {t("help_page_link")}
      </Link>
    </p>
  );
};
