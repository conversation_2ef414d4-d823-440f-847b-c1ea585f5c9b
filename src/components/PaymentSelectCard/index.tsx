"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";
import { PaymentOption } from "@/types/paymentOptions";

import { Card, CardContent, Label } from "../ui";

export const PaymentSelectCard = ({
  title,
  setSelectedOption,
  selectedOption,
  getCardStyles,
}: {
  title: string;
  setSelectedOption: React.Dispatch<React.SetStateAction<PaymentOption>>;
  selectedOption: PaymentOption;
  getCardStyles: (value: string) => string;
}) => {
  const t = useTranslations("buyer_form");

  return (
    <Card
      role="button"
      className={cn(
        "relative max-w-xs flex-1 basis-1/3 cursor-pointer shadow-none",
        getCardStyles(title)
      )}
      onClick={() => setSelectedOption(title as PaymentOption)}
    >
      <CardContent className="flex h-14 flex-col items-start justify-between p-2 sm:flex-row sm:items-center sm:p-[10px]">
        <div className="mb-2 flex items-center space-x-0 sm:mb-0 sm:space-x-2">
          <div className="relative hidden size-4 rounded-full border border-gray-900 bg-transparent sm:block">
            {selectedOption === title && (
              <div
                data-testid="selection-indicator"
                className="absolute left-1/2 top-1/2 size-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary"
              ></div>
            )}
          </div>
          <Label className="cursor-pointer sm:py-2" htmlFor={title}>
            {t(`${title}`)}
          </Label>
        </div>
        <Image
          alt={title}
          height={24}
          src={`/images/${title}.svg`}
          width={36}
        />
      </CardContent>
      {selectedOption === title && (
        <div className="-bottom-0.4 absolute h-1 w-full bg-blue-50"></div>
      )}
    </Card>
  );
};
