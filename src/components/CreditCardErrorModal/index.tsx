import { Misuse } from "@carbon/icons-react";
import { useTranslations } from "next-intl";

import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui";

interface Props {
  isOpen?: boolean;
  onClose?: () => void;
}

export function CreditCardErrorModal({
  isOpen = false,
  onClose = () => {},
}: Props) {
  const t = useTranslations("credit_card_page");

  const onOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[440px] gap-6 rounded-lg bg-white p-7 shadow-lg">
        <DialogHeader>
          <Misuse className="mb-2 h-12 w-12 text-red-600" />
          <DialogTitle className="text-xl">{t("error.title")}</DialogTitle>
          <DialogDescription className="text-gray-500">
            {t("error.description")}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={onClose}>{t("error.button")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
