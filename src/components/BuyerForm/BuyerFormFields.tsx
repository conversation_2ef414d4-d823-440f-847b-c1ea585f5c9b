import { memo } from "react";
import { Control, FieldErrors, UseFormRegister } from "react-hook-form";

import { DocumentInput, FormError, InputField } from "@/components";
import { PhoneInput, Switch } from "@/components/ui";
import { PaymentOption } from "@/types/paymentOptions";

interface BuyerFormFieldsProps {
  errors: FieldErrors;
  register: UseFormRegister<any>;
  control: Control<any>;
  translations: (key: string) => string;
  haveBrazilianDocument: boolean;
  phoneValue: string;
  onPhoneChange: (phoneValue: string) => void;
  onSwitchDocumentType: (isInternational: boolean) => void;
  selectedOption: PaymentOption;
}

export const BuyerFormFields = memo(
  ({
    errors,
    register,
    control,
    translations,
    haveBrazilianDocument,
    phoneValue,
    onPhoneChange,
    onSwitchDocumentType,
    selectedOption,
  }: BuyerFormFieldsProps) => {
    return (
      <div className="w-full">
        <div className="mb-4 mt-8">
          <InputField
            errors={errors}
            label={translations("full_name")}
            name="name"
            placeholder={translations("name_placeholder")}
            register={register}
            type="text"
          />
        </div>
        <div className="mb-4 mt-4">
          <InputField
            errors={errors}
            label={translations("email")}
            name="email"
            placeholder={translations("email_placeholder")}
            register={register}
            type="text"
          />
        </div>
        <div className="mb-4 mt-4">
          <InputField
            errors={errors}
            label={translations("confirm_email")}
            name="confirm_email"
            placeholder={translations("confirm_email_placeholder")}
            register={register}
            type="text"
          />
        </div>
        <div className="mt-4 grid grid-cols-1 gap-x-4 gap-y-6 md:grid-cols-2">
          {haveBrazilianDocument && (
            <DocumentInput
              control={control}
              errors={errors}
              label={translations("cpf_or_cnpj")}
              name="document"
              placeholder={translations("document_placeholder")}
              type="text"
            />
          )}
          <div>
            <PhoneInput
              defaultCountry="BR"
              errors={errors}
              label={translations("phone_number")}
              placeholder={translations("phone_placeholder")}
              value={phoneValue}
              name="phone"
              onChange={onPhoneChange}
            />
            <FormError errors={errors} fieldName={"phone"} />
          </div>
        </div>

        <div className="mt-4 flex items-center gap-2">
          {selectedOption !== PaymentOption.Pix &&
            selectedOption !== PaymentOption.Boleto && (
              <>
                <Switch
                  checked={!haveBrazilianDocument}
                  className="h-4 w-8 border-none data-[state=unchecked]:bg-gray-500"
                  thumbClassName="h-[10px] w-[10px] data-[state=checked]:translate-x-[19px] data-[state=unchecked]:translate-x-[3px]"
                  onCheckedChange={onSwitchDocumentType}
                />
                <p className="text-xs font-medium leading-4 sm:text-sm">
                  {translations("dont_have_brazilianDocument")}
                </p>
              </>
            )}
        </div>
      </div>
    );
  }
);

BuyerFormFields.displayName = "BuyerFormFields";
