import { memo } from "react";

import { OrderBumpPreview } from "@/components/OrderBumpPreview";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";

interface BumpSelectionProps {
  product: Product;
  selectedOption: PaymentOption;
  selectedInstallment: string;
  bumpInstallments: Record<string, string>;
  onBumpSelect: (bumpId: string, isSelected: boolean) => void;
  translations: (key: string) => string;
}

export const BumpSelection = memo(
  ({
    product,
    selectedOption,
    selectedInstallment,
    bumpInstallments,
    onBumpSelect,
    translations,
  }: BumpSelectionProps) => {
    if (!product?.bumps?.length) {
      return null;
    }

    return (
      <>
        <p className="mt-4 text-lg font-semibold leading-[22px] text-gray-900 sm:mt-6">
          {translations("group_buy")}
        </p>
        {product.bumps.map(bump => (
          <OrderBumpPreview
            key={bump.id}
            bump={bump}
            selectedInstallmentBump={
              selectedOption === PaymentOption.CreditCard
                ? selectedInstallment
                : undefined
            }
            installmentsDescription={
              selectedOption === PaymentOption.CreditCard
                ? bumpInstallments
                : undefined
            }
            onSelect={(isSelected: boolean) =>
              onBumpSelect(bump.id, isSelected)
            }
          />
        ))}
      </>
    );
  }
);

BumpSelection.displayName = "BumpSelection";
