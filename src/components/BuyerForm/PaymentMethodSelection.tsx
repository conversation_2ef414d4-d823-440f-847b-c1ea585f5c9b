import { memo, useMemo } from "react";

import { PaymentSelectCard } from "@/components";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";

interface PaymentMethodSelectionProps {
  product: Product;
  selectedOption: PaymentOption;
  setSelectedOption: React.Dispatch<React.SetStateAction<PaymentOption>>;
  getCardStyles: (value: string) => string;
}

export const PaymentMethodSelection = memo(
  ({
    product,
    selectedOption,
    setSelectedOption,
    getCardStyles,
  }: PaymentMethodSelectionProps) => {
    const availablePaymentMethods = useMemo(
      () => product?.payment_methods ?? [PaymentOption.CreditCard],
      [product?.payment_methods]
    );

    return (
      <div className="mt-8 flex gap-3">
        {availablePaymentMethods.map(paymentMethod => (
          <PaymentSelectCard
            getCardStyles={getCardStyles}
            key={paymentMethod}
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            title={paymentMethod}
          />
        ))}
      </div>
    );
  }
);

PaymentMethodSelection.displayName = "PaymentMethodSelection";
