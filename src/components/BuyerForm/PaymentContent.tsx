import { memo, useMemo } from "react";

import { BoletoCard, CreditCardFormFields, PixCard } from "@/components";
import { months, years } from "@/mocks";
import { InstallmentsResponse } from "@/types/instalmentsList";
import { PaymentOption } from "@/types/paymentOptions";

interface PaymentContentProps {
  selectedOption: PaymentOption;
  productInstallments: InstallmentsResponse;
  setSelectedInstalmentDescription: React.Dispatch<
    React.SetStateAction<string>
  >;
  setSelectedInstallment: React.Dispatch<React.SetStateAction<string>>;
}

const createPaymentContentConfig = (
  productInstallments: InstallmentsResponse,
  setSelectedInstalmentDescription: React.Dispatch<
    React.SetStateAction<string>
  >,
  setSelectedInstallment: React.Dispatch<React.SetStateAction<string>>
) => ({
  [PaymentOption.CreditCard]: (
    <CreditCardFormFields
      instalments={productInstallments?.installments ?? []}
      months={months}
      years={years}
      setSelectedInstalmentDescription={setSelectedInstalmentDescription}
      setSelectedInstallment={setSelectedInstallment}
    />
  ),
  [PaymentOption.Boleto]: <BoletoCard />,
  [PaymentOption.Pix]: <PixCard />,
});

export const PaymentContent = memo(
  ({
    selectedOption,
    productInstallments,
    setSelectedInstalmentDescription,
    setSelectedInstallment,
  }: PaymentContentProps) => {
    const content = useMemo(() => {
      const config = createPaymentContentConfig(
        productInstallments,
        setSelectedInstalmentDescription,
        setSelectedInstallment
      );
      return config[selectedOption] || null;
    }, [
      selectedOption,
      productInstallments?.installments,
      setSelectedInstalmentDescription,
      setSelectedInstallment,
    ]);

    return <div className="border-2 border-blue-700 p-4">{content}</div>;
  }
);

PaymentContent.displayName = "PaymentContent";
