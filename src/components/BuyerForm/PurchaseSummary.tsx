import Image from "next/image";
import { memo, useMemo } from "react";

import { InstallmentsResponse } from "@/types/instalmentsList";
import { PaymentOption } from "@/types/paymentOptions";
import { Product, ProductChargeType } from "@/types/product";
import formatToBRL from "@/utils/formatting/formatToBRL";
import { formatSubscriptionInterval } from "@/utils/formatting/subscriptionFormatting";
import { CENTS_IN_REAL } from "@/utils/miscellaneous/constants";

interface PurchaseSummaryProps {
  product: Product;
  selectedOption: PaymentOption;
  selectedInstallment: string;
  selectedBumps: string[];
  bumpInstallments: Record<string, string>;
  totalPrice: number;
  hasTrialDays: boolean;
  firstChargeDate: string | null;
  productInstallments: InstallmentsResponse;
  translations: (key: string) => string;
}

export const PurchaseSummary = memo(
  ({
    product,
    selectedOption,
    selectedInstallment,
    selectedBumps,
    bumpInstallments,
    totalPrice,
    hasTrialDays,
    firstChargeDate,
    productInstallments,
    translations,
  }: PurchaseSummaryProps) => {
    const mainProductInstallmentDescription = useMemo(() => {
      if (!selectedInstallment || !productInstallments?.installments) return "";

      const installmentOption = productInstallments.installments.find(
        installment =>
          installment.installments.toString() === selectedInstallment
      );
      return installmentOption?.description ?? "";
    }, [selectedInstallment, productInstallments?.installments]);

    const shouldShowSummary = useMemo(() => {
      return (
        selectedOption !== PaymentOption.CreditCard ||
        (selectedOption === PaymentOption.CreditCard && selectedInstallment)
      );
    }, [selectedOption, selectedInstallment]);

    if (!shouldShowSummary) return null;

    return (
      <>
        <p className="mt-4 text-lg font-semibold leading-[22px] text-gray-900 sm:mt-6">
          {translations("purchase_summary")}
        </p>

        <div>
          {selectedOption === PaymentOption.CreditCard ? (
            <CreditCardSummary
              product={product}
              selectedBumps={selectedBumps}
              bumpInstallments={bumpInstallments}
              hasTrialDays={hasTrialDays}
              firstChargeDate={firstChargeDate}
              mainProductInstallmentDescription={
                mainProductInstallmentDescription
              }
              selectedInstallment={selectedInstallment}
              totalPrice={totalPrice}
              translations={translations}
            />
          ) : (
            <NonCreditCardSummary
              product={product}
              selectedBumps={selectedBumps}
              hasTrialDays={hasTrialDays}
              firstChargeDate={firstChargeDate}
              totalPrice={totalPrice}
              translations={translations}
            />
          )}
        </div>

        {selectedOption === PaymentOption.CreditCard && (
          <div className="mt-6 flex items-center gap-2">
            <Image
              className="self-start"
              alt="Check"
              height={40}
              src="/images/check.svg"
              width={40}
            />
            <div className="flex flex-col">
              <p className="mb-1 text-sm font-medium leading-tight">
                {translations("safe_buy")}
              </p>
              <span className="text-xs leading-tight text-gray-600">
                {translations("data_protection_card_security")}
              </span>
            </div>
          </div>
        )}
      </>
    );
  }
);

const CreditCardSummary = memo(
  ({
    product,
    selectedBumps,
    bumpInstallments,
    hasTrialDays,
    firstChargeDate,
    mainProductInstallmentDescription,
    selectedInstallment,
    totalPrice,
    translations,
  }: {
    product: Product;
    selectedBumps: string[];
    bumpInstallments: Record<string, string>;
    hasTrialDays: boolean;
    firstChargeDate: string | null;
    mainProductInstallmentDescription: string;
    selectedInstallment: string;
    totalPrice: number;
    translations: (key: string) => string;
  }) => (
    <>
      <div className="">
        <div className="flex flex-col">
          <div className="flex items-center justify-between border-b border-gray-200 pb-3 pt-4">
            <div className="flex flex-col">
              <span className="px-4 text-sm leading-5 text-gray-900">
                {product.title}
              </span>
              {product?.subscription && (
                <span className="px-4 text-xs text-gray-500">
                  {formatSubscriptionInterval(
                    product?.subscription?.interval,
                    product?.subscription?.periodicity,
                    translations
                  )}
                </span>
              )}
            </div>
            <div className="flex flex-col items-end">
              {hasTrialDays &&
              product?.subscription &&
              product.subscription.trial_days &&
              product.subscription.trial_days > 0 ? (
                <div className="flex flex-col items-end">
                  <div className="flex items-center gap-2">
                    <span className="rounded-full bg-emerald-700 px-3 py-1 text-xs font-medium text-white">
                      R$ 0,00 {translations("subscription.trial_period")}{" "}
                      {product.subscription.trial_days}{" "}
                      {translations("subscription.days")}
                    </span>
                    <span className="text-gray-300">|</span>
                    <span className="text-sm text-gray-900">
                      {translations("subscription.then")}{" "}
                      {mainProductInstallmentDescription}
                    </span>
                  </div>
                  <span className="mt-1 text-xs text-gray-500">
                    {translations("subscription.first_charge_on")}{" "}
                    {firstChargeDate}
                  </span>
                </div>
              ) : (
                <p className="left-4 px-4 text-sm">
                  {mainProductInstallmentDescription}
                </p>
              )}
            </div>
          </div>

          {selectedBumps.map(bumpId => {
            const selectedBump = product.bumps.find(bump => bump.id === bumpId);
            if (!selectedBump) return null;

            const isSubscriptionBump =
              selectedBump.charge_type === ProductChargeType.SUBSCRIPTION;

            return (
              <div
                key={bumpId}
                className="flex items-center justify-between border-b border-gray-200 py-4"
              >
                <div className="px-4">
                  <span className="text-sm leading-5 text-gray-900">
                    {selectedBump.title}
                  </span>
                  {isSubscriptionBump && selectedBump.subscription && (
                    <p className="text-xs text-gray-500">
                      {formatSubscriptionInterval(
                        selectedBump.subscription.interval,
                        selectedBump.subscription.periodicity,
                        translations
                      )}
                    </p>
                  )}
                </div>
                <p className="left-4 px-4 text-sm">
                  {bumpInstallments[bumpId]}
                </p>
              </div>
            );
          })}
        </div>
      </div>
      {selectedBumps.length > 0 && (
        <div className="bg-[#F3F4F680]">
          <div className="flex w-full items-center justify-between gap-2 py-4">
            <p className="px-4 text-sm leading-5 text-gray-600">
              {translations("amount_to_be_paid")}
            </p>
            <p className="left-4 px-4 text-sm font-medium">
              {`${selectedInstallment} x de `}
              {formatToBRL(
                Number(totalPrice) /
                  CENTS_IN_REAL /
                  parseInt(selectedInstallment)
              )}
            </p>
          </div>
        </div>
      )}
    </>
  )
);

const NonCreditCardSummary = memo(
  ({
    product,
    selectedBumps,
    hasTrialDays,
    firstChargeDate,
    totalPrice,
    translations,
  }: {
    product: Product;
    selectedBumps: string[];
    hasTrialDays: boolean;
    firstChargeDate: string | null;
    totalPrice: number;
    translations: (key: string) => string;
  }) => (
    <>
      <div className="mt-4">
        <div className="flex flex-col">
          <div className="flex items-center justify-between border-b border-gray-200 py-4">
            <div className="flex flex-col">
              <span className="px-4 text-sm leading-5 text-gray-900">
                {product.title}
              </span>
              {product?.subscription && (
                <span className="px-4 text-xs text-gray-500">
                  {formatSubscriptionInterval(
                    product?.subscription?.interval,
                    product?.subscription?.periodicity,
                    translations
                  )}
                </span>
              )}
            </div>
            <div className="flex flex-col items-end">
              {hasTrialDays &&
              product?.subscription &&
              product.subscription.trial_days &&
              product.subscription.trial_days > 0 ? (
                <div className="flex flex-col items-end">
                  <div className="flex items-center gap-2">
                    <span className="rounded-full bg-emerald-700 px-3 py-1 text-xs font-medium text-white">
                      R$ 0,00 {translations("subscription.trial_period")}{" "}
                      {product.subscription.trial_days}{" "}
                      {translations("subscription.days")}
                    </span>
                    <span className="text-gray-300">|</span>
                    <span className="text-xs text-gray-500">
                      {translations("subscription.then")}{" "}
                      {formatToBRL((product?.price ?? 0) / CENTS_IN_REAL)}
                    </span>
                  </div>
                  <span className="mt-1 text-xs text-gray-500">
                    {translations("subscription.first_charge_on")}{" "}
                    {firstChargeDate}
                  </span>
                </div>
              ) : (
                <p className="left-4 px-4 text-sm leading-[18px]">
                  {formatToBRL((product?.price ?? 0) / CENTS_IN_REAL)}
                </p>
              )}
            </div>
          </div>
          {selectedBumps.map(bumpId => {
            const selectedBump = product.bumps.find(bump => bump.id === bumpId);
            if (!selectedBump) return null;

            const isSubscriptionBump =
              selectedBump.charge_type === ProductChargeType.SUBSCRIPTION;

            return (
              <div
                key={bumpId}
                className="flex items-center justify-between border-b border-gray-200 py-4"
              >
                <div className="px-4">
                  <span className="text-sm leading-5 text-gray-900">
                    {selectedBump.title}
                  </span>
                  {isSubscriptionBump && selectedBump.subscription && (
                    <p className="text-xs text-gray-500">
                      {formatSubscriptionInterval(
                        selectedBump.subscription.interval,
                        selectedBump.subscription.periodicity,
                        translations
                      )}
                    </p>
                  )}
                </div>
                <p className="left-4 px-4 text-sm font-medium">
                  {formatToBRL(selectedBump.price / CENTS_IN_REAL)}
                </p>
              </div>
            );
          })}
        </div>
      </div>
      {selectedBumps.length > 0 && (
        <div className="bg-[#F3F4F680]">
          <div className="flex w-full items-center justify-between gap-2 py-4">
            <p className="px-4 text-sm leading-5 text-gray-600">
              {translations("amount_to_be_paid")}
            </p>
            <p className="left-4 px-4 text-sm font-medium">
              {formatToBRL(Number(totalPrice) / CENTS_IN_REAL)}
            </p>
          </div>
        </div>
      )}
    </>
  )
);

CreditCardSummary.displayName = "CreditCardSummary";
NonCreditCardSummary.displayName = "NonCreditCardSummary";
PurchaseSummary.displayName = "PurchaseSummary";
