import { Locked, QrCode } from "@carbon/icons-react";
import Image from "next/image";
import { memo } from "react";

import { Button } from "@/components/ui";
import { PaymentOption } from "@/types/paymentOptions";
import { Product, ProductChargeType } from "@/types/product";

interface PaymentButtonProps {
  selectedOption: PaymentOption;
  product: Product;
  translations: (key: string) => string;
  onClick: (option: PaymentOption) => void;
}

const createPaymentButtonConfig = (
  product: Product,
  translations: (key: string) => string
) => ({
  [PaymentOption.CreditCard]: {
    icon: <Locked size={18} />,
    text:
      product?.charge_type === ProductChargeType.SUBSCRIPTION
        ? translations("submit_button_subscription")
        : translations("submit_button"),
  },
  [PaymentOption.Pix]: {
    icon: <QrCode size={18} />,
    text: translations("generate_pix"),
  },
  [PaymentOption.Boleto]: {
    icon: (
      <Image src="/images/barcode.svg" alt="Boleto" width={18} height={18} />
    ),
    text: translations("generate_boleto"),
  },
});

export const PaymentButton = memo(
  ({ selectedOption, product, translations, onClick }: PaymentButtonProps) => {
    const config = createPaymentButtonConfig(product, translations);
    const { icon, text } = config[selectedOption] || {
      icon: <Locked size={18} />,
      text: translations("submit_button"),
    };

    return (
      <Button
        className="mt-4 flex w-full items-center gap-2 bg-green-600 hover:bg-green-600 hover:opacity-90"
        type="submit"
        onClick={() => onClick(selectedOption)}
      >
        {icon}
        {text}
      </Button>
    );
  }
);

PaymentButton.displayName = "PaymentButton";
