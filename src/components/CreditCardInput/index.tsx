"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";
import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { ZodSchema } from "zod";

import { useCreditCardFormatter } from "@/hooks/useCreditCardFormatter";
import { useCreditCardList } from "@/hooks/useCreditCardList";
import { usePriorityCards } from "@/hooks/usePriorityCards";
import { useCardValidationStore } from "@/store/cardValidationStore";
import { useProductStore } from "@/store/productStore";

import { FormError } from "../FormError";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui";
import { Input } from "../ui/Input";
import { Label } from "../ui/Label";
import { VALID_CARD_BRANDS_FOR_SUBSCRIPTION } from "./constants";
import { CreditCardInputProps } from "./types";

export function CreditCardInput<T extends ZodSchema>({
  type,
  label,
  name,
  control,
  errors,
  placeholder,
}: CreditCardInputProps<T>) {
  const inputId = `input-${name}`;
  const hasError = Boolean(errors?.[name]);
  const { setError, clearErrors } = useFormContext();
  const { setIsCardValid } = useCardValidationStore();
  const { isSubscription } = useProductStore();
  const t = useTranslations("home");

  const { cardBrandIcon, formatCreditCard, updateCardBrandIcon } =
    useCreditCardFormatter();
  const { sortedCreditCards } = usePriorityCards();
  const { data: creditCardList } = useCreditCardList();

  const VALID_CARD_BRANDS_FOR_ONE_TIME =
    creditCardList?.map(card => card.brand.toLowerCase()) || [];

  const validateCardBrand = (
    cardBrandIcon: string | null,
    isSubscription: boolean
  ): boolean => {
    if (!cardBrandIcon) return false;

    const brandName = cardBrandIcon
      .replace("/brands/", "")
      .replace(".svg", "")
      .toLowerCase();

    return isSubscription
      ? VALID_CARD_BRANDS_FOR_SUBSCRIPTION.some(
          validBrand => validBrand.toLowerCase() === brandName
        )
      : VALID_CARD_BRANDS_FOR_ONE_TIME.some(
          validBrand => validBrand.toLowerCase() === brandName
        );
  };

  const filteredCreditCards = creditCardList?.filter(
    card =>
      !sortedCreditCards.some(sortedCard => sortedCard.brand === card.brand)
  );

  const isCardBrandValid = validateCardBrand(cardBrandIcon, isSubscription);

  React.useEffect(() => {
    if (cardBrandIcon) {
      if (!isCardBrandValid) {
        setError(name, {
          type: "manual",
          message: isSubscription
            ? t("card_brand_not_supported")
            : t("card_brand_not_supported_one_time"),
        });
      } else {
        clearErrors(name);
      }
    }
  }, [
    cardBrandIcon,
    isCardBrandValid,
    name,
    setError,
    clearErrors,
    isSubscription,
  ]);

  React.useEffect(() => {
    setIsCardValid(isCardBrandValid);
  }, [isCardBrandValid, setIsCardValid]);

  return (
    <div>
      <div className="flex flex-col gap-1.5">
        {label && (
          <Label
            className={
              hasError || (Boolean(cardBrandIcon) && !isCardBrandValid)
                ? "text-destructive"
                : ""
            }
            htmlFor={inputId}
          >
            {label}
          </Label>
        )}
        <div className="relative flex items-center gap-2">
          <Controller
            control={control}
            name={name}
            render={({ field: { value, onChange } }) => (
              <Input
                className={
                  hasError || (Boolean(cardBrandIcon) && !isCardBrandValid)
                    ? "border-destructive"
                    : ""
                }
                hasError={
                  hasError || (Boolean(cardBrandIcon) && !isCardBrandValid)
                }
                id={inputId}
                placeholder={placeholder}
                type={type}
                value={value ?? ""}
                onChange={e => {
                  const formattedValue = formatCreditCard(e.target.value);
                  onChange(formattedValue);
                  e.target.value = formattedValue;
                  updateCardBrandIcon(formattedValue);
                }}
              />
            )}
          />
          {cardBrandIcon ? (
            <Image
              alt="Card brand"
              className="absolute right-2 top-1/2 -translate-y-1/2"
              height={32}
              width={32}
              src={cardBrandIcon ?? "/images/brands/defaultcard.svg"}
            />
          ) : (
            <div className="absolute right-2 top-1/2 hidden -translate-y-1/2 gap-2 sm:flex">
              {sortedCreditCards.map(card => (
                <Image
                  alt="Card brand"
                  height={32}
                  key={card.brand}
                  src={
                    typeof card.icon_url === "string" &&
                    card.icon_url.trim() !== ""
                      ? card.icon_url
                      : "/images/brands/defaultcard.svg"
                  }
                  width={32}
                />
              ))}
              {Array.isArray(filteredCreditCards) &&
                filteredCreditCards.length > 0 && (
                  <span className="sm: hidden h-5 w-[38px] cursor-pointer items-center justify-center rounded-full bg-gray-100 text-sm sm:flex">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="text-xs">
                          +{filteredCreditCards.length}
                        </TooltipTrigger>
                        <TooltipContent className="w-[300px]">
                          {filteredCreditCards.map(card => (
                            <p key={card.brand}>{card.brand}</p>
                          ))}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </span>
                )}
            </div>
          )}
        </div>
      </div>
      <FormError errors={errors} fieldName={name} />
    </div>
  );
}
