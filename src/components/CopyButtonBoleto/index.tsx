"use client";

import { Checkmark, Copy } from "@carbon/icons-react";
import { useTranslations } from "next-intl";
import React, { useState } from "react";

import { BoletoPaymentDetails } from "@/types/paymentDetails";

import { Button } from "../ui";

interface CopyButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
  copiedTextKey?: string;
  boletoData: BoletoPaymentDetails;
}

export const CopyButtonBoleto = React.forwardRef<
  HTMLButtonElement,
  CopyButtonProps
>(
  (
    { className, text, boletoData, copiedTextKey = "copied_text", ...props },
    ref
  ) => {
    const t = useTranslations("boleto_page");
    const [isCopied, setIsCopied] = useState(false);
    const [buttonText, setButtonText] = useState(text);
    const boleto = boletoData?.boleto?.barcode_data;

    const handleCopy = async () => {
      try {
        await navigator.clipboard.writeText(boleto);
        setIsCopied(true);
        setButtonText(t(copiedTextKey));
        setTimeout(() => {
          setIsCopied(false);
          setButtonText(text);
        }, 6000);
      } catch (error) {
        console.error("Failed to copy text: ", error);
      }
    };

    return (
      <Button
        ref={ref}
        className={`mt-4 flex h-[44px] w-full items-center justify-start gap-2 bg-gray-100 pb-3 pl-4 pr-4 pt-3 text-gray-900 hover:bg-gray-200 hover:opacity-90 ${className}`}
        type="button"
        onClick={handleCopy}
        {...props}
      >
        {isCopied ? <Checkmark size={18} /> : <Copy size={18} />}
        {buttonText}
      </Button>
    );
  }
);

CopyButtonBoleto.displayName = "CopyButtonBoleto";
