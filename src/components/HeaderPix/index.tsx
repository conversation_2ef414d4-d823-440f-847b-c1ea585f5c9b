"use client";

import { TimeFilled } from "@carbon/icons-react";
import { useTranslations } from "next-intl";

import useTimer from "@/hooks/useTimer";

export default function Header({ pixExpiresIn }: { pixExpiresIn: number }) {
  const t = useTranslations("pix_page");

  const initialTimeInSeconds = pixExpiresIn;

  const { timeInSeconds, formatTime } = useTimer(initialTimeInSeconds);

  return (
    <header className="flex flex-col items-start justify-between rounded-t-lg bg-amber-400 p-8 sm:flex-row sm:items-center">
      <div className="flex items-start gap-2 sm:items-center">
        <div>
          <TimeFilled size={40} />
        </div>
        <h5 className="text-2xl font-semibold leading-7">
          {t("finalize_with_pix")}
        </h5>
      </div>
      <span className="ml-12 mt-2 text-2xl font-semibold leading-7 sm:ml-0 sm:mt-0">
        {formatTime(timeInSeconds)}
      </span>
    </header>
  );
}
