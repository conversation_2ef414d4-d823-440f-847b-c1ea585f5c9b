"use client";

import { Renew } from "@carbon/icons-react";
import { useTranslations } from "next-intl";

import { Installment } from "@/types/instalmentsList";
import { Product } from "@/types/product";
import {
  SubscriptionApiResponse,
  SubscriptionProductForComponents,
} from "@/types/subscription";
import { maskDocument, maskPhone } from "@/utils/masks";

import { LanguageSwitcher } from "../LanguageSwitcher";
import ProductCard from "../ProductCard";

interface ProductInstallments {
  installments: Installment[];
  bumps: any[];
}

interface SubscriptionHeaderProps {
  subscriptionApiData: SubscriptionApiResponse;
  product: Product | SubscriptionProductForComponents;
  productInstallments: ProductInstallments;
}

export const SubscriptionHeader = ({
  subscriptionApiData,
  product,
  productInstallments,
}: SubscriptionHeaderProps) => {
  const t = useTranslations("signature");

  return (
    <div className="w-full">
      <div className="-mx-6 bg-purple-600 p-6 text-white sm:-mx-5">
        <div className="flex items-center gap-3">
          <Renew size={24} className="text-white" />
          <h1 className="text-lg font-medium">{t("title")}</h1>
        </div>
      </div>
      <div className="-mx-6 p-6 sm:-mx-5">
        <div className="mb-4 flex flex-col-reverse justify-between md:flex-row">
          <ProductCard
            product={product}
            productInstallments={productInstallments}
            isSubscriptionPage={true}
          />
          <LanguageSwitcher />
        </div>
        <div>
          <h3 className="mb-3 text-sm font-medium text-gray-900">
            {t("your_data")}
          </h3>
          <div className="grid grid-cols-[auto_1fr] gap-x-16 gap-y-2 text-sm">
            <span className="text-gray-600">{t("full_name")}</span>
            <span className="font-medium text-gray-900">
              {subscriptionApiData.subscriber.name}
            </span>
            <span className="text-gray-600">{t("email")}</span>
            <span className="font-medium text-gray-900">
              {subscriptionApiData.subscriber.email}
            </span>
            <span className="text-gray-600">{t("phone")}</span>
            <span className="font-medium text-gray-900">
              {maskPhone(subscriptionApiData.subscriber.phone)}
            </span>
            <span className="text-gray-600">{t("document")}</span>
            <span className="font-medium text-gray-900">
              {maskDocument(subscriptionApiData.subscriber.document)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
