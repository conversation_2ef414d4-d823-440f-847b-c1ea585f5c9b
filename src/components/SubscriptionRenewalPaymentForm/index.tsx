"use client";

import { Locked, QrCode } from "@carbon/icons-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import { FormProvider } from "react-hook-form";

import { LoadingOverlay } from "@/components";
import { BoletoCard } from "@/components/BoletoCard";
import { CreditCardFormFields } from "@/components/CreditCardFormFields";
import { PaymentSelectCard } from "@/components/PaymentSelectCard";
import { PixCard } from "@/components/PixCard";
import { Button } from "@/components/ui";
import { usePaymentState } from "@/hooks/usePaymentState";
import { useSubscriptionRenewalFormSchema } from "@/hooks/useSubscriptionRenewalFormSchema";
import { useSubscriptionRenewalSubmitHandler } from "@/hooks/useSubscriptionRenewalSubmitHandler";
import { months, years } from "@/mocks";
import { InstallmentsResponse } from "@/types/instalmentsList";
import { PaymentOption } from "@/types/paymentOptions";
import { Product } from "@/types/product";
import { SubscriptionProductForComponents } from "@/types/subscription";

interface SubscriptionRenewalPaymentFormProps {
  product: Product | SubscriptionProductForComponents;
  productInstallments: InstallmentsResponse;
  subscriptionId: string;
  signature?: string;
  onSubmit?: (data: any) => void;
}

export default function SubscriptionRenewalPaymentForm({
  product,
  productInstallments,
  subscriptionId,
  signature,
  onSubmit,
}: SubscriptionRenewalPaymentFormProps) {
  const t = useTranslations("buyer_form");
  const [selectedInstallment, setSelectedInstallment] = useState<string>("");

  const {
    selectedOption,
    setSelectedOption,
    setSelectedInstalmentDescription,
    getCardStyles,
  } = usePaymentState();

  const { formMethods } = useSubscriptionRenewalFormSchema(selectedOption);
  const { onSubmit: handleRenewalSubmit, isLoading } =
    useSubscriptionRenewalSubmitHandler(
      selectedOption,
      subscriptionId,
      signature
    );

  const { handleSubmit } = formMethods;

  const PAYMENT_OPTIONS_CONTENT = {
    [PaymentOption.CreditCard]: (
      <CreditCardFormFields
        instalments={productInstallments?.installments ?? []}
        months={months}
        years={years}
        setSelectedInstalmentDescription={setSelectedInstalmentDescription}
        setSelectedInstallment={setSelectedInstallment}
      />
    ),
    [PaymentOption.Boleto]: <BoletoCard />,
    [PaymentOption.Pix]: <PixCard />,
  };

  const paymentButtonContent = {
    [PaymentOption.CreditCard]: {
      icon: <Locked size={18} />,
      text: t("submit_button_subscription"),
    },
    [PaymentOption.Pix]: {
      icon: <QrCode size={18} />,
      text: t("generate_pix"),
    },
    [PaymentOption.Boleto]: {
      icon: (
        <Image src="/images/barcode.svg" alt="Boleto" width={18} height={18} />
      ),
      text: t("generate_boleto"),
    },
  };

  const handleFormSubmit = async (data: any) => {
    const formData = {
      ...data,
    };

    if (onSubmit) {
      onSubmit({
        ...formData,
        paymentMethod: selectedOption,
        installments: data.instalment || selectedInstallment,
      });
    } else {
      await handleRenewalSubmit(formData);
    }
  };

  const availablePaymentMethods = product?.payment_methods ?? [
    PaymentOption.CreditCard,
  ];

  const selectedContent = PAYMENT_OPTIONS_CONTENT[selectedOption] || null;

  return (
    <FormProvider {...formMethods}>
      {isLoading && <LoadingOverlay />}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="w-full">
        <div className="flex gap-3">
          {availablePaymentMethods?.map(paymentMethod => (
            <PaymentSelectCard
              getCardStyles={getCardStyles}
              key={paymentMethod}
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              title={paymentMethod}
            />
          ))}
        </div>

        <div className="mb-6 rounded-lg border-2 border-blue-700 p-4">
          {selectedContent}
        </div>

        <Button
          className="flex w-full items-center gap-2 bg-green-600 hover:bg-green-600 hover:opacity-90"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
              <LoadingOverlay />
            </>
          ) : (
            <>
              {paymentButtonContent[selectedOption]?.icon}
              {paymentButtonContent[selectedOption]?.text}
            </>
          )}
        </Button>
      </form>
    </FormProvider>
  );
}
