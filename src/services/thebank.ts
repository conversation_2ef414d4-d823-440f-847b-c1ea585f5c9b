import { api } from "@/http/api-client";
import { CardProps } from "@/types/card";
import { OrchestratorResponse } from "@/types/fetchOrchestratorData";

import { MalgaService } from "./malga";

export class TheBankService {
  private malgaService = new MalgaService();

  public async fetchOrchestratorData(orchestrator: string) {
    const response = await api.get<OrchestratorResponse>(
      `v1/checkout/orchestrators/${orchestrator}`
    );
    return response.data;
  }

  public async tokenizeCreditCard(
    card: CardProps,
    client_id: string,
    public_key: string
  ) {
    return this.malgaService.tokenizeCreditCard(card, client_id, public_key);
  }

  public async fetchCreditCardList(): Promise<
    { brand: string; icon_url: string; priority: number }[]
  > {
    const response =
      await api.get<{ brand: string; icon_url: string; priority: number }[]>(
        "/brands"
      );
    return response.data;
  }

  public async fetchProduct(productId: string) {
    const response = await api.get(`/v1/checkout/products/${productId}`);
    return response.data;
  }
}

export const theBankService = new TheBankService();
