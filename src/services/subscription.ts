import { api } from "@/http/api-client";
import { SubscriptionRenewalPayload } from "@/utils/createOrderSubmitHandler/buildSubscriptionPayload";

export class SubscriptionService {
  public async renewSubscription(
    subscriptionId: string,
    payload: SubscriptionRenewalPayload,
    signature?: string
  ) {
    const url = `/v1/checkout/subscriptions/${subscriptionId}`;
    const params = signature ? { signature } : {};

    const response = await api.post(url, payload, { params });
    return response.data;
  }
}

export const subscriptionService = new SubscriptionService();
