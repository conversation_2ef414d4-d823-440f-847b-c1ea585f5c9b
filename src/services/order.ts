import { api } from "@/http/api-client";
import { OrderPayload } from "@/types/order";

export class OrderService {
  public async createOrder(payload: OrderPayload) {
    const response = await api.post("/v1/checkout/orders", payload);
    return response.data;
  }

  public async retryOrder(orderId: string, payload: OrderPayload) {
    const response = await api.post(
      `/v1/checkout/orders/${orderId}/checkout`,
      payload
    );
    return response.data;
  }
}

export const orderService = new OrderService();
