import { SubscriptionApiResponse } from "@/types/subscription";

export async function getSubscriptionData(
  subscriptionId: string,
  signature?: string,
  acceptLanguage?: string
): Promise<SubscriptionApiResponse> {
  const url = new URL(
    `${process.env.NEXT_PUBLIC_BASE_URL}/v1/checkout/subscriptions/${subscriptionId}`
  );
  if (signature) {
    url.searchParams.append("signature", signature);
  }

  const response = await fetch(url.toString(), {
    cache: "no-store",
    headers: {
      "accept-language": acceptLanguage || "pt-BR",
      accept: "application/json",
    },
  });

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error("SUBSCRIPTION_NOT_FOUND");
    }
    throw new Error("SUBSCRIPTION_FETCH_ERROR");
  }

  return response.json();
}
