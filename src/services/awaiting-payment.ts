import { api } from "@/http/api-client";
import {
  AwaitingPaymentOrder,
  AwaitingPaymentSubscription,
} from "@/types/awaitingPayment";

export class AwaitingPaymentService {
  public async getOrder(orderId: string): Promise<AwaitingPaymentOrder> {
    const response = await api.get(
      `/v1/checkout/orders/${orderId}/awaiting-payment`
    );
    return response.data;
  }

  public async getSubscription(
    subscriptionCode: string
  ): Promise<AwaitingPaymentSubscription> {
    const response = await api.get(
      `/v1/checkout/subscriptions/${subscriptionCode}/awaiting-payment`
    );
    return response.data;
  }
}

export const awaitingPaymentService = new AwaitingPaymentService();
