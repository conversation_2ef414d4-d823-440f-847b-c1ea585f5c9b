import axios from "axios";

interface CardDetails {
  cardHolderName: string;
  cardNumber: string;
  cardCvv: string;
  cardExpirationDate: string;
}

export class MalgaService {
  private apiMalga = axios.create({
    baseURL: process.env.NEXT_PUBLIC_TOKENIZATION_MALGA,
  });

  public async tokenizeCreditCard(
    card: CardDetails,
    clientId: string,
    apiKey: string
  ): Promise<{ tokenId: string }> {
    const response = await this.apiMalga.post<{ tokenId: string }>(
      "/tokens",
      card,
      {
        headers: {
          "X-Client-Id": clientId,
          "X-Api-Key": apiKey,
        },
      }
    );

    return response.data;
  }
}

export const malgaService = new MalgaService();
