import { create } from "zustand";

interface BuyerStoreState {
  tokenizationError: boolean;
  setTokenizationError: (error: boolean) => void;
  paymentError: boolean;
  setPaymentError: (error: boolean) => void;
  failedPaymentInformation: { title: string; description: string } | null;
  setFailedPaymentInformation: (info: {
    title: string;
    description: string;
  }) => void;
  pixError: boolean;
  setPixError: (error: boolean) => void;
  failedPixInformation: { title: string; description: string } | null;
  setFailedPixInformation: (info: {
    title: string;
    description: string;
  }) => void;

  boletoError: boolean;
  setBoletoError: (error: boolean) => void;
  failedBoletoInformation: { title: string; description: string } | null;
  setFailedBoletoInformation: (info: {
    title: string;
    description: string;
  }) => void;
}

export const buyerStore = create<BuyerStoreState>(set => ({
  tokenizationError: false,
  setTokenizationError: error => set({ tokenizationError: error }),
  paymentError: false,
  setPaymentError: error => set({ paymentError: error }),
  failedPaymentInformation: null,
  setFailedPaymentInformation: info => set({ failedPaymentInformation: info }),
  pixError: false,
  setPixError: error => set({ pixError: error }),
  failedPixInformation: null,
  setFailedPixInformation: info => set({ failedPixInformation: info }),
  boletoError: false,
  setBoletoError: error => set({ boletoError: error }),
  failedBoletoInformation: null,
  setFailedBoletoInformation: info => set({ failedBoletoInformation: info }),
}));
