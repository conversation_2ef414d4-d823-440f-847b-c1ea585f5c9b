import { PaymentOption } from "@/types/paymentOptions";
import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";

import {
  SubscriptionBoletoResponse,
  SubscriptionPixResponse,
  SubscriptionSyncPaymentParams,
} from "./types";

/**
 * Manipula o pagamento sincronizado de renovação de assinatura via PIX
 *
 * Esta função processa a resposta do pagamento PIX para renovação de assinatura
 * e prepara os dados necessários para exibição da página de PIX com QR code
 * e informações de pagamento.
 *
 * @param params - Parâmetros contendo response, selectedOption, locale e subscriptionId
 * @returns Objeto com response formatada e path de redirecionamento
 */
export const handleSyncPixSubscriptionPayment = ({
  response,
  selectedOption,
  locale,
  subscriptionId,
}: SubscriptionSyncPaymentParams): {
  response: SubscriptionPixResponse;
  path: string;
} | null => {
  const pixResponse: SubscriptionPixResponse = {
    subscription_id: subscriptionId,
    payment_method:
      (response?.transaction?.payment_method as PaymentOption) ??
      selectedOption,
    status:
      (response?.transaction?.status as SubscriptionRenewalStatus) ??
      SubscriptionRenewalStatus.PENDING,
    pix: {
      amount: response?.transaction?.amount ?? 0,
      qr_code: response?.pix?.qr_code ?? "",
      copy_paste: response?.pix?.copy_paste ?? "",
      expires_in: response?.pix?.expires ?? 0,
    },
    product: response?.product,
    bumps: [],
  };

  return { response: pixResponse, path: `/${locale}/order/pix` };
};

/**
 * Manipula o pagamento sincronizado de renovação de assinatura via Boleto
 *
 * Esta função processa a resposta do pagamento boleto para renovação de assinatura
 * e prepara os dados necessários para exibição da página de boleto com código
 * de barras e informações de vencimento.
 *
 * @param params - Parâmetros contendo response, selectedOption, locale e subscriptionId
 * @returns Objeto com response formatada e path de redirecionamento
 */
export const handleSyncBoletoSubscriptionPayment = ({
  response,
  selectedOption,
  locale,
  subscriptionId,
}: SubscriptionSyncPaymentParams): {
  response: SubscriptionBoletoResponse;
  path: string;
} | null => {
  const formatDate = (stringDate: string | number | undefined): string => {
    if (!stringDate) return "";
    return new Date(stringDate).toLocaleDateString("pt-BR");
  };

  const boletoResponse: SubscriptionBoletoResponse = {
    subscription_id: subscriptionId,
    payment_method:
      (response?.transaction?.payment_method as PaymentOption) ??
      selectedOption,
    status:
      (response?.transaction?.status as SubscriptionRenewalStatus) ??
      SubscriptionRenewalStatus.PENDING,
    customer_email: response?.subscriber?.name || "Customer",
    boleto: {
      amount: response?.transaction?.amount ?? 0,
      barcode_image_url: response?.boleto?.barcode_image_url ?? "",
      barcode_data: response?.boleto?.barcode_data ?? "",
      expires_date: formatDate(response?.boleto?.expires_date),
    },
    product: response?.product,
    bumps: [],
  };

  return { response: boletoResponse, path: `/${locale}/order/boleto` };
};

/**
 * Manipula o pagamento sincronizado de renovação de assinatura via Cartão de Crédito
 *
 * Esta função processa a resposta do pagamento com cartão de crédito para renovação
 * de assinatura. Se o pagamento foi aprovado, redireciona para a página de sucesso.
 *
 * @param params - Parâmetros contendo response e locale
 * @returns Objeto com response e path para sucesso, ou null se não aprovado
 */
export const handleSyncCreditCardSubscriptionPayment = ({
  response,
  locale,
}: SubscriptionSyncPaymentParams): { response: any; path: string } | null => {
  if (response?.transaction?.status === SubscriptionRenewalStatus.APPROVED) {
    return { response, path: `/${locale}/success` };
  }
  return null;
};
