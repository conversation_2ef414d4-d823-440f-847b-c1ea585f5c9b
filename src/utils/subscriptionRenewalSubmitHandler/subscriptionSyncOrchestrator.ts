import { PaymentOption } from "@/types/paymentOptions";
import { SubscriptionRenewalResponse } from "@/types/subscriptionRenewalResponse";

import { handleSubscriptionError } from "./errorHandler";
import { pushSubscriptionWithData } from "./navigationUtils";
import { subscriptionSyncPaymentHandlers } from "./subscriptionSyncPaymentHandlers";

interface SubscriptionSyncOrchestratorParams {
  response: SubscriptionRenewalResponse;
  selectedOption: PaymentOption;
  locale: string;
  subscriptionId: string;
  signature?: string;
  router: any;
  setPixError: (failed: boolean) => void;
  setFailedPixInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setBoletoError: (failed: boolean) => void;
  setFailedBoletoInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setPaymentError: (failed: boolean) => void;
  setFailedPaymentInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setIsLoading: (loading: boolean) => void;
}

export const handleSubscriptionSyncPayment = ({
  response,
  selectedOption,
  locale,
  subscriptionId,
  signature,
  router,
  setPixError,
  setFailedPixInformation,
  setBoletoError,
  setFailedBoletoInformation,
  setPaymentError,
  setFailedPaymentInformation,
  setIsLoading,
}: SubscriptionSyncOrchestratorParams): void => {
  const errorHandlersMap = {
    [PaymentOption.Pix]: {
      setError: setPixError,
      setInfo: setFailedPixInformation,
    },
    [PaymentOption.Boleto]: {
      setError: setBoletoError,
      setInfo: setFailedBoletoInformation,
    },
    [PaymentOption.CreditCard]: {
      setError: setPaymentError,
      setInfo: setFailedPaymentInformation,
    },
  } as const;

  const defaultErrorHandlers = {
    setError: setPaymentError,
    setInfo: setFailedPaymentInformation,
  };

  const { setError, setInfo } =
    errorHandlersMap[selectedOption] || defaultErrorHandlers;

  if (handleSubscriptionError({ response, setError, setInfo })) {
    setIsLoading(false);
    return;
  }

  const handler = subscriptionSyncPaymentHandlers[selectedOption];
  if (!handler) {
    console.error(`No handler found for payment option: ${selectedOption}`);
    setIsLoading(false);
    return;
  }

  const result = handler({
    response,
    selectedOption,
    locale,
    subscriptionId,
    signature,
  });

  if (result) {
    if (selectedOption === PaymentOption.CreditCard) {
      // Para cartão de crédito, usamos router.replace com makeQuery
      const { makeQuery } = require("@/utils/stringUtils/enconding");
      const query = makeQuery(result.response);
      router.replace(`${result.path}?d=${query}`);
    } else {
      // Para PIX e Boleto, usamos pushSubscriptionWithData
      pushSubscriptionWithData(
        result.response,
        result.path,
        router,
        signature,
        subscriptionId
      );
    }
  }

  setIsLoading(false);
};
