import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";

import { SubscriptionErrorHandlerParams } from "./types";

/**
 * Verifica e trata erros de renovação de subscription.
 *
 * @description Esta função analisa a resposta de uma renovação de subscription
 * para identificar se houve falha no pagamento e, caso positivo, executa as
 * funções de callback para definir o estado de erro e informações detalhadas.
 *
 * @param params - Parâmetros contendo a resposta e funções de callback
 * @param params.response - Resposta da renovação de subscription a ser verificada
 * @param params.setError - Função para definir o estado de erro
 * @param params.setInfo - Função para definir informações detalhadas do erro
 *
 * @returns `true` se houve erro e foi tratado, `false` caso contrário
 *
 * @example
 * ```typescript
 * const hasError = handleSubscriptionError({
 *   response: subscriptionResponse,
 *   setError: setPaymentError,
 *   setInfo: setFailedPaymentInformation
 * });
 *
 * if (hasError) {
 *   // Erro foi tratado, interromper processamento
 *   return;
 * }
 * ```
 *
 * @see {@link handlePaymentError} - Função equivalente para orders
 * @since 1.0.0
 */
export const handleSubscriptionError = ({
  response,
  setError,
  setInfo,
}: SubscriptionErrorHandlerParams): boolean => {
  if (
    response.transaction?.status === SubscriptionRenewalStatus.FAILED &&
    response?.transaction?.error
  ) {
    setError(true);
    setInfo({
      title: response?.transaction?.error?.title,
      description: response?.transaction?.error?.description,
    });
    return true;
  }
  return false;
};
