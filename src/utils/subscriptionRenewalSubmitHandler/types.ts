import { PaymentOption } from "@/types/paymentOptions";
import {
  SubscriptionRenewalResponse,
  SubscriptionRenewalStatus,
} from "@/types/subscriptionRenewalResponse";

export interface SubscriptionSyncPaymentHandler {
  (
    params: SubscriptionSyncPaymentParams
  ): { response: any; path: string } | null;
}

export interface SubscriptionSyncPaymentHandlers {
  [PaymentOption.Pix]: SubscriptionSyncPaymentHandler;
  [PaymentOption.Boleto]: SubscriptionSyncPaymentHandler;
  [PaymentOption.CreditCard]: SubscriptionSyncPaymentHandler;
  default?: SubscriptionSyncPaymentHandler;
}

export interface SubscriptionErrorHandlerParams {
  response: SubscriptionRenewalResponse;
  setError: (failed: boolean) => void;
  setInfo: (info: { title: string; description: string }) => void;
}

export interface SubscriptionSyncPaymentParams {
  response: SubscriptionRenewalResponse;
  selectedOption: PaymentOption;
  locale: string;
  subscriptionId: string;
  signature?: string;
}

export interface SubscriptionSuccessResponse {
  subscription_id: string;
  payment_method: PaymentOption;
  status: SubscriptionRenewalStatus;
  product: any;
  bumps: any[];
}

export interface SubscriptionPixResponse {
  subscription_id: string;
  payment_method: PaymentOption;
  status: SubscriptionRenewalStatus;
  pix: {
    amount: string | number;
    qr_code: string;
    copy_paste: string;
    expires_in: string | number;
  };
  product: any;
  bumps: any[];
}

export interface SubscriptionBoletoResponse {
  subscription_id: string;
  payment_method: PaymentOption;
  status: SubscriptionRenewalStatus;
  customer_email: string;
  boleto: {
    amount: string | number;
    barcode_data: string;
    barcode_image_url: string;
    expires_date: string;
  };
  product: any;
  bumps: any[];
}
