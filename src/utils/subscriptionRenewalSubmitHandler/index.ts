/**
 * @fileoverview Utilitários para Manipulação de Renovação de Assinatura
 *
 * Este módulo fornece utilitários para lidar com a renovação de assinaturas e processamento
 * de pagamentos no sistema de checkout. Inclui funções para processar diferentes métodos
 * de pagamento (PIX, Boleto, Cartão de Crédito), tratar erros específicos de assinatura
 * e gerenciar o fluxo de navegação para renovações.
 *
 * @module subscriptionRenewalSubmitHandler
 * @version 1.0.0
 */

// Funções de tratamento de erro
export { handleSubscriptionError } from "./errorHandler";

// Funções de navegação
export {
  redirectToPendingSubscriptionCreditCard,
  pushSubscriptionWithData,
} from "./navigationUtils";

// Handlers de sincronização de pagamento
export {
  handleSyncPixSubscriptionPayment,
  handleSyncBoletoSubscriptionPayment,
  handleSyncCreditCardSubscriptionPayment,
} from "./subscriptionPaymentHandlers";

// Orquestrador de sincronização
export { handleSubscriptionSyncPayment } from "./subscriptionSyncOrchestrator";

// Tipos
export type {
  SubscriptionSyncPaymentHandler,
  SubscriptionSyncPaymentHandlers,
  SubscriptionErrorHandlerParams,
  SubscriptionSyncPaymentParams,
  SubscriptionSuccessResponse,
  SubscriptionPixResponse,
  SubscriptionBoletoResponse,
} from "./types";
