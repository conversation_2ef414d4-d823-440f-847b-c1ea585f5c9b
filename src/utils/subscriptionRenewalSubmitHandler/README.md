# Utilitários de Renovação de Subscription

Esta pasta contém as funções utilitárias para o domínio de renovação de
subscriptions, refatoradas do hook `useSubscriptionRenewalSubmitHandler`
seguindo o mesmo padrão de organização aplicado ao
`useCreateOrderSubmitHandler`.

## Estrutura dos Arquivos

### Tratamento de Erros

- `errorHandler.ts` - Função utilitária para tratamento de erros específicos de
  subscription renewal

### Navegação

- `navigationUtils.ts` - Funções de redirecionamento e navegação específicas
  para subscriptions

### Handlers de Pagamento

- `subscriptionPaymentHandlers.ts` - Handlers específicos para cada método de
  pagamento (PIX, Boleto, Cartão) em subscriptions
- `subscriptionSyncPaymentHandlers.ts` - Objeto que mapeia os handlers por
  método de pagamento
- `subscriptionSyncOrchestrator.ts` - Orquestrador que injeta dependências e
  executa a sincronização

### Tipos

- `types.ts` - Definições de tipos específicos do domínio de subscription
  renewal

### Exportações

- `index.ts` - Arquivo de índice que exporta todas as funções utilitárias

## Reutilização de Código

### Funções Reutilizadas do Domínio de Orders

- `buildSubscriptionPayload` - Reutilizada de `utils/createOrderSubmitHandler/`
- `processPayment` - Reutilizada de `utils/createOrderSubmitHandler/`

### Padrões Aplicados

- **Object Literals**: Uso de hashmap ao invés de switch case para error
  handlers
- **Separação de Responsabilidades**: Cada arquivo tem uma função específica
- **Modularização**: Funções pequenas e puras
- **Type Safety**: Tipos explícitos em todas as funções

## Diferenças Específicas para Subscriptions

1. **Tipos de Response**: `SubscriptionRenewalResponse` vs `OrderResponse`
2. **Status**: `SubscriptionRenewalStatus` vs `OrderStatus`
3. **Navegação**: URLs específicas para subscriptions (`/subscriptions/` vs
   `/order/`)
4. **Parâmetros Extras**: `signature` e `subscriptionId` específicos para
   subscriptions

## Benefícios da Refatoração

1. **Consistência**: Mesmo padrão arquitetural do hook de orders
2. **Reutilização**: Aproveitamento de funções comuns entre orders e
   subscriptions
3. **Manutenibilidade**: Código organizado e bem estruturado
4. **Testabilidade**: Funções isoladas e testáveis
5. **Escalabilidade**: Fácil adicionar novos métodos de pagamento

## Como Usar

```typescript
import {
  handleSubscriptionSyncPayment,
  redirectToPendingSubscriptionCreditCard,
} from "@/utils/subscriptionRenewalSubmitHandler";
```

O hook `useSubscriptionRenewalSubmitHandler` agora atua como um orquestrador,
delegando as responsabilidades específicas para essas funções utilitárias,
mantendo consistência com a arquitetura refatorada do hook de orders.
