import { SubscriptionRenewalResponse } from "@/types/subscriptionRenewalResponse";
import { makeQuery } from "@/utils/stringUtils/enconding";

export const redirectToPendingSubscriptionCreditCard = (
  data: SubscriptionRenewalResponse,
  locale: string,
  subscriptionId: string,
  router: any
): void => {
  router.push(
    `/${locale}/subscriptions/credit-card/${subscriptionId}&d=${makeQuery(data)}`
  );
};

export const pushSubscriptionWithData = <T>(
  data: T,
  path: string,
  router: any,
  signature?: string,
  subscriptionId?: string
): void => {
  const payload = makeQuery(data);
  const baseUrl = `${path}?d=${payload}`;
  const urlWithSignature =
    signature && subscriptionId
      ? `${baseUrl}&signature=${signature}&subscriptionId=${subscriptionId}`
      : baseUrl;
  router.push(urlWithSignature);
};
