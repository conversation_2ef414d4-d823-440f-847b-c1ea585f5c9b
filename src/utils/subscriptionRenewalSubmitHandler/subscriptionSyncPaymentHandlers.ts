import { PaymentOption } from "@/types/paymentOptions";

import {
  handleSyncBoletoSubscriptionPayment,
  handleSyncCreditCardSubscriptionPayment,
  handleSyncPixSubscriptionPayment,
} from "./subscriptionPaymentHandlers";
import { SubscriptionSyncPaymentHandlers } from "./types";

export const subscriptionSyncPaymentHandlers: SubscriptionSyncPaymentHandlers =
  {
    [PaymentOption.Pix]: handleSyncPixSubscriptionPayment,
    [PaymentOption.Boleto]: handleSyncBoletoSubscriptionPayment,
    [PaymentOption.CreditCard]: handleSyncCreditCardSubscriptionPayment,
  };
