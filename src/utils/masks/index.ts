/**
 * Aplica máscara de privacidade em número de telefone.
 *
 * @description Esta função mascara um número de telefone, ocultando os primeiros
 * dígitos e mostrando apenas os últimos 4 dígitos no formato (**) *****-XXXX.
 *
 * @param phoneSuffix - String contendo os últimos 4 dígitos do telefone
 * @returns String com máscara aplicada ou string original se inválida
 *
 * @example
 * ```typescript
 * maskPhone("1234")        // "(**) *****-1234"
 * maskPhone("9999")        // "(**) *****-9999"
 * maskPhone("123")         // "123" (retorna original se não tiver 4 dígitos)
 * maskPhone("12345")       // "12345" (retorna original se tiver mais de 4)
 * maskPhone("")            // "" (retorna original se vazio)
 * ```
 *
 * @example
 * ```typescript
 * // Uso em exibição de dados sensíveis
 * const phoneLastDigits = "1234";
 * const maskedPhone = maskPhone(phoneLastDigits); // "(**) *****-1234"
 *
 * // Uso em confirmação de dados
 * const confirmationText = `Confirmar envio para ${maskPhone(lastDigits)}?`;
 * ```
 *
 * @since 1.0.0
 */
export function maskPhone(phoneSuffix: string): string {
  const digits = phoneSuffix.replace(/\D/g, "");
  if (digits.length !== 4) return phoneSuffix;
  return `(**) *****-${digits}`;
}

/**
 * Aplica máscara de privacidade em documento (CPF).
 *
 * @description Esta função mascara um CPF, ocultando a maior parte dos dígitos
 * e mostrando apenas o terceiro dígito do último grupo e os dois dígitos verificadores
 * no formato ***.***.**X-XX.
 *
 * @param documentSuffix - String contendo os últimos 3 dígitos do CPF
 * @returns String com máscara aplicada ou string original se inválida
 *
 * @example
 * ```typescript
 * maskDocument("901")      // "***.***.**9-01"
 * maskDocument("123")      // "***.***.**1-23"
 * maskDocument("12")       // "12" (retorna original se não tiver 3 dígitos)
 * maskDocument("1234")     // "1234" (retorna original se tiver mais de 3)
 * maskDocument("")         // "" (retorna original se vazio)
 * ```
 *
 * @example
 * ```typescript
 * // Uso em exibição de dados sensíveis
 * const cpfLastDigits = "901";
 * const maskedCPF = maskDocument(cpfLastDigits); // "***.***.**9-01"
 *
 * // Uso em confirmação de identidade
 * const confirmText = `CPF terminado em ${maskDocument(lastDigits)}`;
 * ```
 *
 * @since 1.0.0
 */
export function maskDocument(documentSuffix: string): string {
  const digits = documentSuffix.replace(/\D/g, "");
  if (digits.length !== 3) return documentSuffix;
  const third = digits.charAt(0);
  const lastTwo = digits.slice(1);
  return `***.***.**${third}-${lastTwo}`;
}
