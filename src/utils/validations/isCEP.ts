/* eslint-disable no-useless-escape */
const CEP_PATTERN = /^(\d{8}|\d{2}\.?\d{3}\-\d{3})$/;

/**
 * Valida se uma string é um CEP válido no formato brasileiro.
 *
 * @description Esta função verifica se um CEP está no formato válido brasileiro:
 * - 8 dígitos consecutivos (ex: "12345678")
 * - Formato com hífen (ex: "12.345-678" ou "12345-678")
 *
 * @param value - String contendo o CEP a ser validado
 * @returns `true` se o CEP estiver no formato válido, `false` caso contrário
 *
 * @example
 * ```typescript
 * isCEP("12345678")        // true (formato sem pontuação)
 * isCEP("12.345-678")      // true (formato com ponto e hífen)
 * isCEP("12345-678")       // true (formato com hífen)
 * isCEP("1234567")         // false (CEP incompleto)
 * isCEP("123456789")       // false (CEP com dígitos extras)
 * isCEP("12.345.678")      // false (formato incorreto)
 * isCEP("")                // false (string vazia)
 * isCEP("abcdefgh")        // false (caracteres não numéricos)
 * ```
 *
 * @example
 * ```typescript
 * // Uso em validação de formulários
 * const validateCEPInput = (cep: string) => {
 *   if (!isCEP(cep)) {
 *     setError("CEP inválido");
 *     return false;
 *   }
 *   return true;
 * };
 *
 * // Uso em validação de endereços
 * const address = { cep: "01234-567" };
 * if (isCEP(address.cep)) {
 *   // Buscar dados do CEP via API
 *   fetchAddressByCEP(address.cep);
 * }
 * ```
 *
 * @see {@link formatToCEP} - Função para formatação de CEP
 * @since 1.0.0
 */
export const isCEP = (value: string): boolean => CEP_PATTERN.test(value);
