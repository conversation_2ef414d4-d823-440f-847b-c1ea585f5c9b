import { generateCheckSums, getRemaining } from "../mathUtils";
import { CPF_PATTERN } from "../miscellaneous";
import { mapToNumbers } from "../stringUtils";
import { isRepeatedArray } from "./isRepeatedArray";

/**
 * Valida se uma string é um CPF válido.
 *
 * @description Esta função verifica se um CPF é válido através de:
 * 1. Verificação do padrão (11 dígitos numéricos)
 * 2. Verificação se não são todos os dígitos iguais
 * 3. Validação dos dígitos verificadores usando o algoritmo oficial
 *
 * @param value - String contendo o CPF a ser validado (com ou sem formatação)
 * @returns `true` se o CPF for válido, `false` caso contrário
 *
 * @example
 * ```typescript
 * isCPF("123.456.789-09")    // true (CPF válido)
 * isCPF("12345678909")       // true (mesmo CPF sem formatação)
 * isCPF("111.111.111-11")    // false (todos os dígitos iguais)
 * isCPF("123.456.789-00")    // false (dígitos verificadores incorretos)
 * isCPF("123.456.789")       // false (CPF incompleto)
 * isCPF("")                  // false (string vazia)
 * ```
 *
 * @example
 * ```typescript
 * // Uso em validação de formulários
 * const validateCPFInput = (cpf: string) => {
 *   if (!isCPF(cpf)) {
 *     setError("CPF inválido");
 *     return false;
 *   }
 *   return true;
 * };
 *
 * // Uso em validação de dados de API
 * const user = { cpf: "123.456.789-09" };
 * if (isCPF(user.cpf)) {
 *   // Processar usuário com CPF válido
 * }
 * ```
 *
 * @see {@link isCNPJ} - Função similar para validação de CNPJ
 * @see {@link formatToCPF} - Função para formatação de CPF
 * @since 1.0.0
 */
export const isCPF = (value: string): boolean => {
  if (!CPF_PATTERN.test(value)) return false;

  const numbers = mapToNumbers(value);

  if (isRepeatedArray(numbers)) return false;

  const validators = [11, 10, 9, 8, 7, 6, 5, 4, 3, 2];
  const checkers = generateCheckSums(numbers, validators);

  return (
    numbers[9] === getRemaining(checkers[0]) &&
    numbers[10] === getRemaining(checkers[1])
  );
};
