/**
 * Tipos de periodicidade de assinatura suportados
 */
type Periodicity = "monthly";

/**
 * Formata o intervalo de renovação de assinatura com suporte à internacionalização
 *
 * @param interval - O intervalo numérico (ex: 1, 3, 6, 12)
 * @param periodicity - O tipo de periodicidade (atualmente apenas "monthly")
 * @param t - Função de tradução para internacionalização
 * @returns String formatada como "Renova a cada 1 mês" ou "Renews every 3 months"
 *
 * @example
 * ```typescript
 * formatSubscriptionInterval(1, "monthly", t) // "Renova a cada 1 mês"
 * formatSubscriptionInterval(3, "monthly", t) // "Renova a cada 3 meses"
 * ```
 */
export function formatSubscriptionInterval(
  interval: number,
  periodicity: Periodicity,
  t: any
) {
  const periodicityMap = {
    monthly: interval === 1 ? "month" : "months",
  };
  return `${t("subscription.renews_every")} ${interval} ${t(`subscription.${periodicityMap[periodicity]}`)}`;
}

/**
 * Formata a periodicidade da assinatura para exibição (tipicamente usado em preços)
 *
 * @param interval - O intervalo numérico (ex: 1, 3, 6, 12)
 * @param periodicity - O tipo de periodicidade (atualmente apenas "monthly")
 * @param t - Função de tradução para internacionalização
 * @returns String formatada como "/ a cada 1 mês" ou "/ every 6 months"
 *
 * @example
 * ```typescript
 * formatSubscriptionPeriodicity(1, "monthly", t) // "/ a cada 1 mês"
 * formatSubscriptionPeriodicity(6, "monthly", t) // "/ a cada 6 meses"
 * ```
 */
export function formatSubscriptionPeriodicity(
  interval: number,
  periodicity: Periodicity,
  t: any
) {
  const periodicityMap = {
    monthly: interval === 1 ? "month" : "months",
  };
  return `/ ${t("subscription.every")} ${interval} ${t(`subscription.${periodicityMap[periodicity]}`)}`;
}
