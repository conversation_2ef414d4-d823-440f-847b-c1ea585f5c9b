import { mapToNumeric } from "../stringUtils";

/**
 * Formata uma string para o padrão de CPF brasileiro.
 *
 * @description Esta função aplica a máscara de CPF (XXX.XXX.XXX-XX) em uma string,
 * removendo caracteres não numéricos e aplicando a formatação padrão brasileira.
 *
 * @param value - String contendo números a serem formatados como CPF
 * @returns String formatada no padrão CPF (ex: "123.456.789-01")
 *
 * @example
 * ```typescript
 * formatToCPF("12345678901")     // "123.456.789-01"
 * formatToCPF("123456789")       // "123.456.789"
 * formatToCPF("123abc456def")    // "123.456"
 * formatToCPF("")                // ""
 * ```
 *
 * @example
 * ```typescript
 * // Uso em formulários
 * const handleCPFChange = (event) => {
 *   const formatted = formatToCPF(event.target.value);
 *   setInputValue(formatted);
 * };
 * ```
 *
 * @see {@link mapToNumeric} - Função auxiliar para extrair apenas números
 * @see {@link formatToCNPJ} - Função similar para CNPJ
 * @since 1.0.0
 */
export const formatToCPF = (value: string): string =>
  mapToNumeric(value)
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d{1,2})$/, "$1-$2");
