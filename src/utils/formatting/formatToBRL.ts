import addPeriods from "../mathUtils/addPeriods";

/**
 * Formata um valor numérico para o formato de moeda brasileira (Real - BRL).
 *
 * @description Esta função converte números ou strings numéricas para o formato
 * padrão de moeda brasileira, incluindo o símbolo "R$", separadores de milhares
 * com pontos e decimais com vírgula.
 *
 * @param value - O valor a ser formatado. Pode ser um número ou string numérica.
 * @returns Uma string formatada no padrão brasileiro de moeda (ex: "R$ 1.234,56").
 *
 * @example
 * ```typescript
 * formatToBRL(1234.56)        // "R$ 1.234,56"
 * formatToBRL("1234.56")      // "R$ 1.234,56"
 * formatToBRL(0)              // "R$ 0,00"
 * formatToBRL("invalid")      // "R$ 0,00"
 * formatToBRL(1000000)        // "R$ 1.000.000,00"
 * ```
 *
 * @example
 * ```typescript
 * // Uso comum em componentes
 * const price = 2599.90;
 * const formattedPrice = formatToBRL(price); // "R$ 2.599,90"
 *
 * // Uso com valores de API
 * const apiValue = "1500.75";
 * const displayValue = formatToBRL(apiValue); // "R$ 1.500,75"
 * ```
 *
 * @throws Não lança erros. Valores inválidos são convertidos para 0.
 *
 * @see {@link addPeriods} - Função auxiliar para adicionar separadores de milhares
 *
 * @since 1.0.0
 * <AUTHOR> Web Checkout Team
 */
const formatToBRL = (value: number | string): string => {
  const parsedValue = isNaN(Number(value)) ? 0 : Number(value);
  const number = parsedValue.toFixed(2).replace(".", ",");
  return "R$ " + addPeriods(number);
};

export default formatToBRL;
