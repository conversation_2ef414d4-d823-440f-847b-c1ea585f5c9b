import { mapToNumeric } from "../stringUtils";

/**
 * Formata uma string para o padrão de CNPJ brasileiro.
 *
 * @description Esta função aplica a máscara de CNPJ (XX.XXX.XXX/XXXX-XX) em uma string,
 * removendo caracteres não numéricos e aplicando a formatação padrão brasileira.
 *
 * @param value - String contendo números a serem formatados como CNPJ
 * @returns String formatada no padrão CNPJ (ex: "12.345.678/0001-90")
 *
 * @example
 * ```typescript
 * formatToCNPJ("12345678000190")     // "12.345.678/0001-90"
 * formatToCNPJ("12345678000")        // "12.345.678/000"
 * formatToCNPJ("123abc456def")       // "12.3"
 * formatToCNPJ("")                   // ""
 * ```
 *
 * @example
 * ```typescript
 * // Uso em formulários
 * const handleCNPJChange = (event) => {
 *   const formatted = formatToCNPJ(event.target.value);
 *   setInputValue(formatted);
 * };
 * ```
 *
 * @see {@link mapToNumeric} - Função auxiliar para extrair apenas números
 * @see {@link formatToCPF} - Função similar para CPF
 * @since 1.0.0
 */
export const formatToCNPJ = (value: string): string =>
  mapToNumeric(value)
    .replace(/(\d{2})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1/$2")
    .replace(/(\d{4})(\d{1,2})$/, "$1-$2");
