import { NON_NUMERIC_REGEX } from "../miscellaneous";

/**
 * Remove todos os caracteres não numéricos de uma string usando regex predefinida.
 *
 * @description Esta função utiliza uma regex predefinida para remover todos os
 * caracteres que não sejam dígitos numéricos, mantendo apenas os números 0-9.
 *
 * @param value - String da qual remover caracteres não numéricos
 * @returns String contendo apenas dígitos numéricos
 *
 * @example
 * ```typescript
 * mapToNumeric("123.456.789-01")    // "12345678901"
 * mapToNumeric("(11) 99999-9999")   // "11999999999"
 * mapToNumeric("R$ 1.234,56")       // "123456"
 * mapToNumeric("abc123def456")      // "123456"
 * mapToNumeric("")                  // ""
 * ```
 *
 * @example
 * ```typescript
 * // Uso em formatação de documentos
 * const cleanCPF = mapToNumeric(cpfInput);     // Remove pontos e traços
 * const cleanCNPJ = mapToNumeric(cnpjInput);   // Remove pontos, barras e traços
 * const cleanPhone = mapToNumeric(phoneInput); // Remove parênteses e traços
 * ```
 *
 * @see {@link clearString} - Função similar com implementação direta
 * @see {@link NON_NUMERIC_REGEX} - Regex utilizada para remoção de caracteres
 * @since 1.0.0
 */
export const mapToNumeric = (value: string): string =>
  value.replace(NON_NUMERIC_REGEX, "");
