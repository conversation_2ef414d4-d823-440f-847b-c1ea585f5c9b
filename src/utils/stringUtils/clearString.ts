/**
 * Remove todos os caracteres não numéricos de uma string.
 *
 * @description Esta função utilitária remove todos os caracteres que não sejam
 * dígitos (0-9) de uma string, mantendo apenas os números.
 *
 * @param value - String da qual remover caracteres não numéricos
 * @returns String contendo apenas dígitos numéricos
 *
 * @example
 * ```typescript
 * clearString("123.456.789-01")    // "12345678901"
 * clearString("(11) 99999-9999")   // "11999999999"
 * clearString("abc123def456")      // "123456"
 * clearString("R$ 1.234,56")       // "123456"
 * clearString("")                  // ""
 * ```
 *
 * @example
 * ```typescript
 * // Uso para limpar inputs de documentos
 * const cleanCPF = clearString(cpfInput); // Remove pontos e traços
 * const cleanPhone = clearString(phoneInput); // Remove parênteses e traços
 * ```
 *
 * @see {@link mapToNumeric} - Função similar com funcionalidade adicional
 * @since 1.0.0
 */
export function clearString(value: string) {
  return value.replace(/\D/g, "");
}
