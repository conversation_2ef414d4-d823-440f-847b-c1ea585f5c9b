/**
 * Converte uma string para Base64 com suporte a caracteres UTF-8.
 *
 * @description Esta função codifica uma string em Base64, tratando corretamente
 * caracteres especiais e acentos através de encodeURIComponent.
 *
 * @param str - String a ser codificada em Base64
 * @returns String codificada em Base64
 *
 * @example
 * ```typescript
 * toBase64("Hello World")           // "SGVsbG8gV29ybGQ="
 * toBase64("Olá, mundo!")          // "T2zDoSwgbXVuZG8h"
 * toBase64('{"name": "<PERSON>"}')     // "eyJuYW1lIjogIkpvw6NvIn0="
 * ```
 *
 * @see {@link fromBase64} - Função para decodificar Base64
 * @since 1.0.0
 */
export function toBase64(str: string): string {
  return btoa(
    encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (_, p1) => {
      return String.fromCharCode(parseInt(p1, 16));
    })
  );
}

/**
 * Decodifica uma string Base64 para string UTF-8.
 *
 * @description Esta função decodifica uma string Base64, tratando corretamente
 * caracteres especiais e acentos através de decodeURIComponent.
 *
 * @param encoded - String codificada em Base64
 * @returns String decodificada em UTF-8
 *
 * @example
 * ```typescript
 * fromBase64("SGVsbG8gV29ybGQ=")     // "Hello World"
 * fromBase64("T2zDoSwgbXVuZG8h")     // "Olá, mundo!"
 * fromBase64("eyJuYW1lIjogIkpvw6NvIn0=") // '{"name": "João"}'
 * ```
 *
 * @see {@link toBase64} - Função para codificar em Base64
 * @since 1.0.0
 */
export function fromBase64(encoded: string): string {
  return decodeURIComponent(
    atob(encoded)
      .split("")
      .map(char => {
        return "%" + char.charCodeAt(0).toString(16).padStart(2, "0");
      })
      .join("")
  );
}

/**
 * Converte dados em uma query string codificada.
 *
 * @description Esta função serializa dados em JSON, codifica em Base64 e
 * aplica encodeURIComponent para uso seguro em URLs como parâmetro de query.
 *
 * @param data - Dados a serem convertidos (qualquer tipo serializável)
 * @returns String codificada pronta para uso em URL
 *
 * @example
 * ```typescript
 * const orderData = { id: "123", status: "approved" };
 * const query = makeQuery(orderData);
 * // Resultado: "eyJpZCI6IjEyMyIsInN0YXR1cyI6ImFwcHJvdmVkIn0%3D"
 *
 * // Uso em navegação
 * router.push(`/success?d=${makeQuery(responseData)}`);
 * ```
 *
 * @example
 * ```typescript
 * // Uso comum no sistema de checkout
 * const paymentData = {
 *   orderId: "order-123",
 *   amount: 99.90,
 *   method: "credit_card"
 * };
 * const encodedData = makeQuery(paymentData);
 * ```
 *
 * @see {@link toBase64} - Função de codificação Base64 utilizada internamente
 * @since 1.0.0
 */
export function makeQuery(data: any) {
  return encodeURIComponent(toBase64(JSON.stringify(data)));
}
