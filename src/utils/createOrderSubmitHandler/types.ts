import { OrderResponse, OrderStatus } from "@/types/orderResponse";
import { PaymentOption } from "@/types/paymentOptions";

export interface SyncPaymentHandler {
  (params: SyncPaymentParams): { response: any; path: string } | null;
}

export interface SyncPaymentHandlers {
  [PaymentOption.Pix]: SyncPaymentHandler;
  [PaymentOption.Boleto]: SyncPaymentHandler;
  [PaymentOption.CreditCard]: SyncPaymentHandler;
  default?: SyncPaymentHandler;
}

export interface ErrorHandlerParams {
  order: OrderResponse;
  setError: (failed: boolean) => void;
  setInfo: (info: { title: string; description: string }) => void;
}

export interface SyncPaymentParams {
  order: OrderResponse;
  selectedOption: PaymentOption;
  locale: string;
}

export interface SuccessResponse {
  order_id: string;
  payment_method: PaymentOption;
  customer_email: string;
  product: any;
  bumps: any[];
  payment: {
    status: OrderStatus;
    description: string | number;
    brand: string | null;
    brand_image_url: string | null;
    first_digits: string | null;
    last_digits: string | null;
    installments: string | null;
  };
}

export interface PixResponse {
  order_id: string;
  payment_method: PaymentOption;
  status: OrderStatus;
  pix: {
    amount: string | number;
    qr_code: string;
    copy_paste: string;
    expires_in: string | number;
  };
  product: any;
  bumps: any[];
}

export interface BoletoResponse {
  order_id: string;
  payment_method: PaymentOption;
  customer_email: string;
  status: OrderStatus;
  boleto: {
    amount: string | number;
    barcode_data: string;
    barcode_image: string;
    expires_date: string;
  };
  product: any;
  bumps: any[];
}
