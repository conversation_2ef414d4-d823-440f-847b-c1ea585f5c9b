/**
 * @fileoverview Utilitários para Manipulação de Envio de Pedidos
 *
 * Este módulo fornece utilitários para lidar com o envio de pedidos e processamento de pagamentos
 * no sistema de checkout. Inclui funções para construir payloads de pagamento,
 * processar diferentes métodos de pagamento (PIX, Boleto, Cartão de Crédito), tratar erros
 * e gerenciar o fluxo de navegação.
 *
 * @module createOrderSubmitHandler
 * @version 1.0.0
 */

// Funções de processamento de pagamento
export { buildOrderPayload } from "./buildOrderPayload";
export { buildSubscriptionPayload } from "./buildSubscriptionPayload";
export { formatBuyerData } from "./formatBuyerData";
export { processPayment } from "./processPayment";

export { handlePaymentError } from "./errorHandler";

export {
  redirectToPendingCreditCardOrder,
  pushWithData,
} from "./navigationUtils";

export {
  handleSyncPixOrderPayment,
  handleSyncBoletoOrderPayment,
  handleSyncCreditCardOrderPayment,
} from "./paymentHandlers";

export { handleSyncPayment } from "./paymentSyncOrchestrator";

export type {
  SyncPaymentHandler,
  SyncPaymentHandlers,
  ErrorHandlerParams,
  SyncPaymentParams,
  SuccessResponse,
  PixResponse,
  BoletoResponse,
} from "./types";
