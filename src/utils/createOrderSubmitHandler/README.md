# Utilitários de Criação de Ordem

Esta pasta contém as funções utilitárias para o domínio de criação de ordens,
refatoradas do hook `useCreateOrderSubmitHandler` para melhor organização e
manutenibilidade.

## Estrutura dos Arquivos

### Processamento de Pagamento

- `buildOrderPayload.ts` - Constrói o payload da ordem
- `buildSubscriptionPayload.ts` - Constrói o payload da assinatura
- `formatBuyerData.ts` - Formata os dados do comprador
- `processPayment.ts` - Processa o pagamento

### Tratamento de Erros

- `errorHandler.ts` - Função utilitária para tratamento de erros de pagamento

### Navegação

- `navigationUtils.ts` - Funções de redirecionamento e navegação

### Handlers de Pagamento

- `paymentHandlers.ts` - Handlers específicos para cada método de pagamento
  (PIX, Boleto, Cartão)
- `syncPaymentHandlers.ts` - Objeto que mapeia os handlers por método de
  pagamento
- `paymentSyncOrchestrator.ts` - Orquestrador que injeta dependências e executa
  a sincronização

### Tipos

- `types.ts` - Definições de tipos específicos do domínio

### Exportações

- `index.ts` - Arquivo de índice que exporta todas as funções utilitárias

## Benefícios da Refatoração

1. **Separação de Responsabilidades**: Cada arquivo tem uma responsabilidade
   específica
2. **Testabilidade**: Funções pequenas e puras são mais fáceis de testar
3. **Reutilização**: Funções podem ser reutilizadas em outros contextos
4. **Manutenibilidade**: Código organizado e bem estruturado
5. **Legibilidade**: Nomes descritivos e arquivos focados

## Como Usar

```typescript
import {
  handleSyncPayment,
  redirectToPendingCreditCardOrder,
  buildOrderPayload,
} from "@/utils/createOrderSubmitHandler";
```

O hook `useCreateOrderSubmitHandler` agora atua como um orquestrador, delegando
as responsabilidades específicas para essas funções utilitárias.
