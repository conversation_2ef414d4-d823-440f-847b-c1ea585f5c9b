import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { malgaService } from "@/services";
import { OrchestratorResponse } from "@/types/fetchOrchestratorData";
import { PaymentOption } from "@/types/paymentOptions";

import { clearString } from "../stringUtils";

/**
 * Processa o pagamento tokenizando dados do cartão de crédito
 *
 * Esta função lida com a tokenização segura dos dados do cartão de crédito
 * através do serviço Malga. Só processa quando o método de pagamento é cartão
 * de crédito e retorna o token para uso posterior na transação.
 *
 * @param data - Dados parciais do formulário do comprador incluindo dados do cartão
 * @param selectedOption - Método de pagamento selecionado
 * @param orchestratorData - Dados do orquestrador com chaves de API
 * @param setTokenizationError - Função para definir estado de erro de tokenização
 * @returns Promise com o token do cartão ou undefined para outros métodos de pagamento
 *
 * @throws Error quando a tokenização falha
 *
 * @example
 * ```typescript
 * const token = await processPayment(
 *   buyerData,
 *   PaymentOption.CreditCard,
 *   orchestratorData,
 *   setError
 * );
 * ```
 */
export const processPayment = async (
  data: BuyerFormValuesPartial,
  selectedOption: PaymentOption,
  orchestratorData: OrchestratorResponse,
  setTokenizationError: (value: boolean) => void
): Promise<string | undefined> => {
  if (selectedOption !== PaymentOption.CreditCard) return;

  if (!orchestratorData) return;

  const cardDetails = {
    cardHolderName: data.cardHolderName?.toUpperCase() as string,
    cardNumber: clearString(data.cardNumber as string),
    cardCvv: data.cardCvv as string,
    cardExpirationDate: `${data.cardExpirationDateMonth as string}/${data.cardExpirationDateYear as string}`,
  };

  try {
    const response = await malgaService.tokenizeCreditCard(
      cardDetails,
      orchestratorData.client_id,
      orchestratorData.public_key
    );
    return response.tokenId;
  } catch (error) {
    setTokenizationError(true);
    throw error;
  }
};
