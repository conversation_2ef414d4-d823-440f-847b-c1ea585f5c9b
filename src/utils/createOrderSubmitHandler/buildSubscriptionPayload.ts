import { getCreditCardNameByNumber } from "@thebank/creditcard-js";

import { SubscriptionRenewalFormValuesPartial } from "@/schemas/subscriptionRenewalSchema";
import { PaymentOption } from "@/types/paymentOptions";
import { clearString } from "@/utils/stringUtils";

/**
 * Interface para o payload de renovação de assinatura
 */
export interface SubscriptionRenewalPayload {
  subscription_id: string;
  payment: {
    method: PaymentOption;
    credit_card?: {
      token: string;
      installments: number;
      first_digits: string;
      last_digits: string;
      brand: string;
    };
  };
  signature?: string;
}

/**
 * Constrói o payload para renovação de assinatura
 *
 * Esta função cria um payload padronizado para renovação de assinatura que pode ser
 * enviado para a API de pagamento. Lida com diferentes métodos de pagamento e inclui
 * dados específicos do cartão de crédito quando necessário.
 *
 * @param subscriptionData - Dados parciais do formulário de renovação de assinatura
 * @param subscriptionId - ID da assinatura a ser renovada
 * @param selectedOption - Método de pagamento selecionado
 * @param token - Token de pagamento opcional (obrigatório para cartão de crédito)
 * @param signature - Assinatura opcional para autenticação
 * @returns Payload completo para renovação de assinatura
 *
 * @example
 * ```typescript
 * const payload = buildSubscriptionPayload(
 *   subscriptionFormData,
 *   "sub-123",
 *   PaymentOption.CreditCard,
 *   "card-token-456",
 *   "signature-hash"
 * );
 * ```
 */
export const buildSubscriptionPayload = (
  subscriptionData: SubscriptionRenewalFormValuesPartial,
  subscriptionId: string,
  selectedOption: PaymentOption,
  token?: string,
  signature?: string
): SubscriptionRenewalPayload => {
  const payload: SubscriptionRenewalPayload = {
    subscription_id: subscriptionId,
    payment: {
      method: selectedOption,
    },
  };

  if (signature) {
    payload.signature = signature;
  }

  if (
    selectedOption === PaymentOption.CreditCard &&
    subscriptionData.cardNumber &&
    token
  ) {
    payload.payment.credit_card = {
      token,
      installments: subscriptionData.instalment || 1,
      first_digits: clearString(subscriptionData.cardNumber).slice(0, 4),
      last_digits: clearString(subscriptionData.cardNumber).slice(-4),
      brand: getCreditCardNameByNumber(
        clearString(subscriptionData.cardNumber)
      ).toLowerCase(),
    };
  }

  return payload;
};
