import { getCreditCardNameByNumber } from "@thebank/creditcard-js";
import { omit } from "lodash";

import { BuyerFormValuesWithOptionalDocument } from "@/types/buyerFormValuesWithOptionalDocument";
import { OrderPayload } from "@/types/order";
import { PaymentOption } from "@/types/paymentOptions";

import { clearString } from "../stringUtils";

/**
 * Constrói o payload do pedido para processamento de pagamento
 *
 * Esta função cria um payload de pedido padronizado que pode ser enviado para a API de pagamento.
 * Ela lida com diferentes métodos de pagamento (PIX, Boleto, Cartão de Crédito) e sanitiza dados
 * sensíveis antes de construir o payload final.
 *
 * @param buyerData - Os dados do formulário do comprador com documento opcional
 * @param productId - O ID do produto principal sendo comprado
 * @param selectedOption - O método de pagamento selecionado (PIX, Boleto ou Cartão de Crédito)
 * @param token - Token de pagamento opcional (obrigatório para pagamentos com cartão de crédito)
 * @param selectedProductBumpsId - Array opcional de IDs de produtos adicionais (bumps/upsells)
 * @returns O payload completo do pedido pronto para envio à API
 *
 * @example
 * ```typescript
 * const payload = buildOrderPayload(
 *   buyerFormData,
 *   "product-123",
 *   PaymentOption.CreditCard,
 *   "card-token-456",
 *   ["bump-1", "bump-2"]
 * );
 * ```
 */
export const buildOrderPayload = (
  buyerData: BuyerFormValuesWithOptionalDocument,
  productId: string,
  selectedOption: PaymentOption,
  token?: string,
  selectedProductBumpsId?: string[]
): OrderPayload => {
  const sanitizedData = omit(buyerData, [
    "cardCvv",
    "cardExpirationDateMonth",
    "cardExpirationDateYear",
    "cardHolderName",
    "cardNumber",
    "instalment",
  ]);

  const products = [
    { id: productId, quantity: 1 },
    ...(selectedProductBumpsId?.map(productId => ({
      id: productId,
      quantity: 1,
    })) || []),
  ];

  return {
    products,
    main_product_id: productId,
    buyer: {
      name: buyerData.name,
      email: buyerData.email,
      confirm_email: buyerData.confirm_email,
      document: sanitizedData.document ?? undefined,
      phone: buyerData.phone
        ? {
            ddi: buyerData.phone.ddi,
            number: buyerData.phone.number,
          }
        : { ddi: "", number: "" },
    },
    payment: {
      method: selectedOption,
      ...(selectedOption === PaymentOption.CreditCard && {
        credit_card: {
          token,
          installments: buyerData.instalment,
          first_digits: clearString(buyerData.cardNumber as string).slice(0, 4),
          last_digits: clearString(buyerData.cardNumber as string).slice(-4),
          brand: buyerData.cardNumber
            ? getCreditCardNameByNumber(
                clearString(buyerData.cardNumber as string)
              ).toLocaleLowerCase()
            : undefined,
        },
        // ...(buyerData.billing_address && {
        //   billing_address: {
        //     ...(streetNumber && {
        //       number: streetNumber,
        //     }),
        //     street: buyerData.billing_address.street,
        //     district: buyerData.billing_address.district,
        //     city: buyerData.billing_address.city,
        //     state: buyerData.billing_address.state,
        //     zipcode: buyerData.billing_address.zipcode,
        //     complement: buyerData.billing_address.complement,
        //     country: buyerData.billing_address.country,
        //   },
        // }),
      }),
    },
  };
};
