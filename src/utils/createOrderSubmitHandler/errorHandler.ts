import { OrderStatus } from "@/types/orderResponse";

import { ErrorHandlerParams } from "./types";

/**
 * Manipula erros de pagamento durante o processamento de pedidos
 *
 * Esta função verifica se a transação falhou e, em caso positivo,
 * configura o estado de erro na interface com as informações
 * apropriadas para exibição ao usuário.
 *
 * @param params - Objeto contendo order, setError e setInfo
 * @param params.order - Dados do pedido com informações da transação
 * @param params.setError - Função para definir estado de erro
 * @param params.setInfo - Função para definir informações de erro
 * @returns true se houve erro e foi tratado, false caso contrário
 */
export const handlePaymentError = ({
  order,
  setError,
  setInfo,
}: ErrorHandlerParams): boolean => {
  if (
    order.transaction?.status === OrderStatus.FAILED &&
    order?.transaction?.error
  ) {
    setError(true);
    setInfo({
      title: order?.transaction?.error?.title,
      description: order?.transaction?.error?.description,
    });
    return true;
  }
  return false;
};
