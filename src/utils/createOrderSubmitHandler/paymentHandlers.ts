import { OrderStatus } from "@/types/orderResponse";
import { PaymentOption } from "@/types/paymentOptions";

import {
  BoletoResponse,
  PixResponse,
  SuccessResponse,
  SyncPaymentParams,
} from "./types";

/**
 * Manipula o pagamento sincronizado de pedidos via PIX
 *
 * Esta função processa a resposta do pagamento PIX e determina o próximo passo
 * no fluxo de checkout. Se o pedido estiver em trial, redireciona para sucesso,
 * caso contrário, prepara os dados do PIX para exibição.
 *
 * @param params - Parâmetros contendo order, selectedOption e locale
 * @returns Objeto com response e path de redirecionamento, ou null se inválido
 */
export const handleSyncPixOrderPayment = ({
  order,
  selectedOption,
  locale,
}: SyncPaymentParams): {
  response: SuccessResponse | PixResponse;
  path: string;
} | null => {
  if (order?.order.status === OrderStatus.TRIALED) {
    const response: SuccessResponse = {
      order_id: order?.order?.id,
      payment_method:
        (order?.transaction?.payment_method as PaymentOption) ?? selectedOption,
      customer_email: order?.buyer?.email,
      product: order?.product,
      bumps: order?.bumps || [],
      payment: {
        status: order?.order?.status,
        description: order?.transaction?.description ?? "R$ 0,00",
        brand: null,
        brand_image_url: null,
        first_digits: null,
        last_digits: null,
        installments: null,
      },
    };
    return { response, path: `/${locale}/success` };
  }

  const response: PixResponse = {
    order_id: order?.order?.id,
    payment_method:
      (order?.transaction?.payment_method as PaymentOption) ?? selectedOption,
    status: (order?.transaction?.status as OrderStatus) ?? order?.order?.status,
    pix: {
      amount: order?.transaction?.amount,
      qr_code: order?.pix?.qr_code,
      copy_paste: order?.pix?.copy_paste,
      expires_in: order?.pix?.expires,
    },
    product: order?.product,
    bumps: order?.bumps || [],
  };

  return { response, path: `/${locale}/order/${selectedOption}` };
};

/**
 * Manipula o pagamento sincronizado de pedidos via Boleto
 *
 * Esta função processa a resposta do pagamento boleto e determina o próximo passo
 * no fluxo de checkout. Se o pedido estiver em trial, redireciona para sucesso,
 * caso contrário, prepara os dados do boleto para exibição.
 *
 * @param params - Parâmetros contendo order, selectedOption e locale
 * @returns Objeto com response e path de redirecionamento, ou null se inválido
 */
export const handleSyncBoletoOrderPayment = ({
  order,
  selectedOption,
  locale,
}: SyncPaymentParams): {
  response: SuccessResponse | BoletoResponse;
  path: string;
} | null => {
  if (order?.order.status === OrderStatus.TRIALED) {
    const response: SuccessResponse = {
      order_id: order?.order?.id,
      payment_method:
        (order?.transaction?.payment_method as PaymentOption) ?? selectedOption,
      customer_email: order?.buyer?.email,
      product: order?.product,
      bumps: order?.bumps || [],
      payment: {
        status: order?.order?.status,
        description: order?.transaction?.description ?? "R$ 0,00",
        brand: null,
        brand_image_url: null,
        first_digits: null,
        last_digits: null,
        installments: null,
      },
    };
    return { response, path: `/${locale}/success` };
  }

  const formatDate = (stringDate: string): string =>
    new Date(stringDate).toLocaleDateString("pt-BR");

  const response: BoletoResponse = {
    order_id: order?.order?.id,
    payment_method:
      (order?.transaction?.payment_method as PaymentOption) ?? selectedOption,
    customer_email: order?.buyer?.email,
    status: (order?.transaction?.status as OrderStatus) ?? order?.order?.status,
    boleto: {
      amount: order?.transaction?.amount ?? "R$ 0,00",
      barcode_data: order?.boleto?.barcode_data ?? "",
      barcode_image: order?.boleto?.barcode_image_url ?? "",
      expires_date: formatDate(order?.boleto?.expires_date ?? ""),
    },
    product: order?.product,
    bumps: order?.bumps || [],
  };

  return { response, path: `/${locale}/order/${selectedOption}` };
};

/**
 * Manipula o pagamento sincronizado de pedidos via Cartão de Crédito
 *
 * Esta função processa a resposta do pagamento com cartão de crédito.
 * Só retorna dados se o pagamento foi aprovado ou está em trial,
 * preparando a resposta de sucesso com os dados do cartão.
 *
 * @param params - Parâmetros contendo order, selectedOption e locale
 * @returns Objeto com response de sucesso e path, ou null se pagamento não aprovado
 */
export const handleSyncCreditCardOrderPayment = ({
  order,
  selectedOption,
  locale,
}: SyncPaymentParams): { response: SuccessResponse; path: string } | null => {
  if (
    order?.transaction?.status === OrderStatus.APPROVED ||
    order?.order.status === OrderStatus.TRIALED
  ) {
    const response: SuccessResponse = {
      order_id: order?.order?.id,
      payment_method:
        (order?.transaction?.payment_method as PaymentOption) ?? selectedOption,
      customer_email: order?.buyer?.email,
      product: order?.product,
      bumps: order?.bumps || [],
      payment: {
        status:
          (order?.transaction?.status as OrderStatus) ?? order?.order?.status,
        description: order?.transaction?.description ?? 0,
        brand: order?.credit_card?.brand ?? null,
        brand_image_url: null,
        first_digits: order?.credit_card?.first_digits.toString() ?? null,
        last_digits: order?.credit_card?.last_digits.toString() ?? null,
        installments: order?.credit_card?.installments.toString() ?? null,
      },
    };
    return { response, path: `/${locale}/success` };
  }

  return null;
};
