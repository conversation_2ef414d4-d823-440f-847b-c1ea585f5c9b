import { omit } from "lodash";

import { BuyerFormValuesPartial } from "@/schemas/createBuyerSchema";
import { BuyerFormValuesWithOptionalDocument } from "@/types/buyerFormValuesWithOptionalDocument";

/**
 * Formata os dados do comprador baseado na disponibilidade de documento brasileiro
 *
 * Esta função condiciona os dados do comprador dependendo se ele possui ou não
 * um documento brasileiro válido. Remove o campo de documento quando não aplicável.
 *
 * @param data - Dados parciais do formulário do comprador
 * @param haveBrazilianDocument - Indica se o comprador possui documento brasileiro
 * @returns Dados do comprador formatados com documento opcional
 */
export const formatBuyerData = (
  data: BuyerFormValuesPartial,
  haveBrazilianDocument: boolean
): BuyerFormValuesWithOptionalDocument =>
  haveBrazilianDocument ? data : omit(data, "document");
