import { OrderResponse } from "@/types/orderResponse";
import { makeQuery } from "@/utils/stringUtils/enconding";

/**
 * Redireciona para a página de cartão de crédito pendente.
 *
 * @description Esta função redireciona o usuário para a página específica de
 * processamento de cartão de crédito quando o pagamento está com status pendente.
 *
 * @param data - Resposta da ordem contendo os dados do pagamento
 * @param locale - Código do idioma/localização (ex: "pt-BR", "en-US")
 * @param productId - ID do produto para incluir na URL
 * @param router - Instância do roteador do Next.js
 *
 * @example
 * ```typescript
 * redirectToPendingCreditCardOrder(
 *   orderResponse,
 *   "pt-BR",
 *   "product-123",
 *   router
 * );
 * // Redireciona para: /pt-BR/order/credit-card/order-456?product=product-123&d=...
 * ```
 *
 * @since 1.0.0
 */
export const redirectToPendingCreditCardOrder = (
  data: OrderResponse,
  locale: string,
  productId: string,
  router: any
): void => {
  router.push(
    `/${locale}/order/credit-card/${data?.order?.id}?product=${productId}&d=${makeQuery(data)}`
  );
};

/**
 * Navega para uma rota com dados codificados na query string.
 *
 * @description Esta função utilitária navega para uma rota específica,
 * codificando os dados fornecidos e adicionando-os como parâmetro 'd' na URL.
 *
 * @template T - Tipo dos dados a serem codificados
 * @param data - Dados a serem codificados e incluídos na URL
 * @param path - Caminho de destino da navegação
 * @param router - Instância do roteador do Next.js
 *
 * @example
 * ```typescript
 * const responseData = { orderId: "123", status: "approved" };
 * pushWithData(responseData, "/pt-BR/success", router);
 * // Navega para: /pt-BR/success?d=eyJvcmRlcklkIjoiMTIzIiwic3RhdHVzIjoiYXBwcm92ZWQifQ==
 * ```
 *
 * @since 1.0.0
 */
export const pushWithData = <T>(data: T, path: string, router: any): void => {
  const payload = makeQuery(data);
  router.push(`${path}?d=${payload}`);
};
