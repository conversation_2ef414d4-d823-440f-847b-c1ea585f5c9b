import { OrderResponse } from "@/types/orderResponse";
import { PaymentOption } from "@/types/paymentOptions";

import { handlePaymentError } from "./errorHandler";
import { pushWithData } from "./navigationUtils";
import { syncPaymentHandlers } from "./syncPaymentHandlers";

interface PaymentSyncOrchestratorParams {
  orderResponse: OrderResponse;
  selectedOption: PaymentOption;
  locale: string;
  cardBrandIcon?: string;
  router: any;
  setPixError: (failed: boolean) => void;
  setFailedPixInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setBoletoError: (failed: boolean) => void;
  setFailedBoletoInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setPaymentError: (failed: boolean) => void;
  setFailedPaymentInformation: (info: {
    title: string;
    description: string;
  }) => void;
  setIsLoading: (loading: boolean) => void;
}

export const handleSyncPayment = ({
  orderResponse,
  selectedOption,
  locale,
  router,
  setPixError,
  setFailedPixInformation,
  setBoletoError,
  setFailedBoletoInformation,
  setPaymentError,
  setFailedPaymentInformation,
  setIsLoading,
}: PaymentSyncOrchestratorParams): void => {
  const errorHandlersMap = {
    [PaymentOption.Pix]: {
      setError: setPixError,
      setInfo: setFailedPixInformation,
    },
    [PaymentOption.Boleto]: {
      setError: setBoletoError,
      setInfo: setFailedBoletoInformation,
    },
    [PaymentOption.CreditCard]: {
      setError: setPaymentError,
      setInfo: setFailedPaymentInformation,
    },
  } as const;

  const defaultErrorHandlers = {
    setError: setPaymentError,
    setInfo: setFailedPaymentInformation,
  };

  const { setError, setInfo } =
    errorHandlersMap[selectedOption] || defaultErrorHandlers;

  if (handlePaymentError({ order: orderResponse, setError, setInfo })) {
    setIsLoading(false);
    return;
  }

  const handler = syncPaymentHandlers[selectedOption];
  if (!handler) {
    console.error(`No handler found for payment option: ${selectedOption}`);
    setIsLoading(false);
    return;
  }

  const result = handler({
    order: orderResponse,
    selectedOption,
    locale,
  });

  if (result) {
    pushWithData(result.response, result.path, router);
  }

  setIsLoading(false);
};
