import { SubscriptionRenewalResponse } from "@/types/subscriptionRenewalResponse";

export interface SuccessPageData {
  order_id: string;
  payment_method: string;
  customer_email: string;
  product: {
    id: string;
    title: string;
    description: string;
    charge_type: string;
    platform: {
      name: string;
      url: string;
    };
  };
  bumps: any[];
  payment: {
    status: string;
    description: string;
    brand?: string;
    brand_image_url?: string | null;
    first_digits?: number;
    last_digits?: number;
    installments?: number;
  };
}

export function convertSubscriptionResponseToSuccessData(
  response: SubscriptionRenewalResponse,
  subscriptionId: string
): SuccessPageData {
  return {
    order_id: response.code || subscriptionId,
    payment_method: response.transaction.payment_method,
    customer_email: response.subscriber?.name || "Customer",
    product: {
      id: subscriptionId,
      title: response.product?.title || "Subscription Product",
      description: response.transaction?.description || "Subscription renewal",
      charge_type: response.periodicity?.type || "subscription",
      platform: {
        name: response.product?.platform?.name || "Platform",
        url: response.product?.platform?.url || "",
      },
    },
    bumps: [],
    payment: {
      status: response.transaction.status,
      description: response.transaction.description,
      brand: response.credit_card?.brand,
      brand_image_url: null,
      first_digits: response.credit_card?.first_digits,
      last_digits: response.credit_card?.last_digits,
      installments: response.credit_card?.installments,
    },
  };
}
