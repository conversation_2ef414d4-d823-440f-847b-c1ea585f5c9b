import { SubscriptionRenewalStatus } from "@/types/subscriptionRenewalResponse";

export interface WebSocketSubscriptionData {
  payment: {
    method: string;
    amount: string;
    status: SubscriptionRenewalStatus;
    error?: {
      title: string;
      description: string;
    };
    description: string;
    brand?: string;
    first_digits?: number;
    last_digits?: number;
    installments?: number;
  };
  product: {
    title: string;
    description: string;
    platform: {
      name: string;
      url: string;
    };
  };
}

export interface SuccessPageData {
  order_id: string;
  payment_method: string;
  customer_email: string;
  product: {
    id: string;
    title: string;
    description: string;
    platform: {
      name: string;
      url: string;
    };
  };
  bumps: any[];
  payment: {
    status: string;
    description: string;
    brand?: string;
    brand_image_url?: string | null;
    first_digits?: number;
    last_digits?: number;
    installments?: number;
  };
}

export function convertWebSocketSubscriptionToSuccessData(
  webSocketData: WebSocketSubscriptionData,
  subscriptionId: string,
  customerEmail: string = "Customer"
): SuccessPageData {
  return {
    order_id: subscriptionId,
    payment_method: webSocketData.payment.method,
    customer_email: customerEmail,
    product: {
      id: subscriptionId,
      title: webSocketData.product.title,
      description: webSocketData.product.description,
      platform: {
        name: webSocketData.product.platform.name,
        url: webSocketData.product.platform.url,
      },
    },
    bumps: [],
    payment: {
      status: webSocketData.payment.status,
      description: webSocketData.payment.description,
      brand: webSocketData.payment.brand,
      brand_image_url: null,
      first_digits: webSocketData.payment.first_digits,
      last_digits: webSocketData.payment.last_digits,
      installments: webSocketData.payment.installments,
    },
  };
}
