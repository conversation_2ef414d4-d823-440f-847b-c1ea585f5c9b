/* eslint-disable no-useless-escape */

/**
 * Regex para identificar caracteres não numéricos.
 *
 * @description Esta regex corresponde a qualquer caractere que não seja um dígito (0-9).
 * Utilizada para limpar strings e manter apenas números.
 *
 * @example
 * ```typescript
 * "123abc456".replace(NON_NUMERIC_REGEX, ""); // "123456"
 * "R$ 1.234,56".replace(NON_NUMERIC_REGEX, ""); // "123456"
 * ```
 */
export const NON_NUMERIC_REGEX = /[^\d]/g;

/**
 * Padrão regex para validação de CPF brasileiro.
 *
 * @description Valida CPF nos formatos:
 * - 11 dígitos consecutivos (ex: "12345678901")
 * - Formato com pontos e hífen (ex: "123.456.789-01")
 *
 * @example
 * ```typescript
 * CPF_PATTERN.test("12345678901");     // true
 * CPF_PATTERN.test("123.456.789-01"); // true
 * CPF_PATTERN.test("123456789");       // false
 * ```
 */
export const CPF_PATTERN = /^(\d{11}|\d{3}\.\d{3}\.\d{3}\-\d{2})$/;

/**
 * Padrão regex para validação de CNPJ brasileiro.
 *
 * @description Valida CNPJ nos formatos:
 * - 14 dígitos consecutivos (ex: "12345678000190")
 * - Formato com pontos, barra e hífen (ex: "12.345.678/0001-90")
 *
 * @example
 * ```typescript
 * CNPJ_PATTERN.test("12345678000190");       // true
 * CNPJ_PATTERN.test("12.345.678/0001-90");  // true
 * CNPJ_PATTERN.test("1234567800019");        // false
 * ```
 */
export const CNPJ_PATTERN = /^(\d{14}|\d{2}\.\d{3}\.\d{3}\/\d{4}\-\d{2})$/;

/**
 * Padrão regex para validação de telefone brasileiro.
 *
 * @description Valida telefones brasileiros em diversos formatos:
 * - Com ou sem código do país (+55)
 * - Com ou sem DDD entre parênteses
 * - Com ou sem o 9 adicional para celulares
 * - Com hífen, espaço ou sem separador
 *
 * @example
 * ```typescript
 * PHONE_PATTERN.test("(11) 99999-9999");     // true
 * PHONE_PATTERN.test("+55 11 99999-9999");   // true
 * PHONE_PATTERN.test("11999999999");         // true
 * PHONE_PATTERN.test("11 9999-9999");        // true
 * ```
 */
export const PHONE_PATTERN = /^(\+55)? ?\(?(\d{2})?\)? ?9? ?\d{4}[-| ]?\d{4}$/;

/**
 * Constante para conversão entre centavos e reais.
 *
 * @description Representa quantos centavos equivalem a 1 real.
 * Utilizada para conversões monetárias precisas.
 *
 * @example
 * ```typescript
 * const reais = centavos / CENTS_IN_REAL;     // 1000 centavos = 10 reais
 * const centavos = reais * CENTS_IN_REAL;     // 10 reais = 1000 centavos
 * ```
 */
export const CENTS_IN_REAL = 100;
