/**
 * Extrai um código de localização (locale) de uma string.
 *
 * @description Esta função utiliza regex para extrair um padrão de locale
 * no formato "idioma-região" (ex: "pt-BR", "en-US") de uma string maior.
 *
 * @param locale - String contendo o locale a ser extraído
 * @returns Código de locale extraído ou `null` se não encontrado
 *
 * @example
 * ```typescript
 * extractLocaleFromString("pt-BR")           // "pt-BR"
 * extractLocaleFromString("en-US")           // "en-US"
 * extractLocaleFromString("es-ES")           // "es-ES"
 * extractLocaleFromString("user-pt-BR-data") // "pt-BR"
 * extractLocaleFromString("invalid")         // null
 * extractLocaleFromString("")                // null
 * ```
 *
 * @example
 * ```typescript
 * // Uso em roteamento internacionalizado
 * const urlPath = "/pt-BR/checkout/success";
 * const locale = extractLocaleFromString(urlPath); // "pt-BR"
 *
 * // Uso em configuração de idioma
 * const userPreference = "user-settings-en-US";
 * const extractedLocale = extractLocaleFromString(userPreference); // "en-US"
 * ```
 *
 * @see {@link useParams} - Hook do Next.js para obter parâmetros de rota
 * @since 1.0.0
 */
export const extractLocaleFromString = (locale: string): string | null => {
  const match = RegExp(/\b[a-zA-Z]{1,3}-[a-zA-Z0-9]{2}\b/).exec(locale);
  return match ? match[0] : null;
};
