/**
 * Adiciona separadores de milhares (pontos) a uma string numérica.
 *
 * @description Esta função adiciona pontos como separadores de milhares em uma
 * string numérica, seguindo o padrão brasileiro de formatação de números.
 *
 * @param value - String numérica para adicionar separadores
 * @returns String com pontos como separadores de milhares
 *
 * @example
 * ```typescript
 * addPeriods("1234567")      // "1.234.567"
 * addPeriods("1000000")      // "1.000.000"
 * addPeriods("123")          // "123"
 * addPeriods("1234,56")      // "1.234,56"
 * addPeriods("")             // ""
 * ```
 *
 * @example
 * ```typescript
 * // Uso em formatação de moeda
 * const amount = "123456";
 * const formatted = `R$ ${addPeriods(amount)},00`; // "R$ 123.456,00"
 *
 * // Uso em exibição de números grandes
 * const population = "2170000";
 * const display = addPeriods(population); // "2.170.000"
 * ```
 *
 * @see {@link formatToBRL} - Função que utiliza addPeriods para formatação de moeda
 * @since 1.0.0
 */
const addPeriods = (value: string): string =>
  value.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");

export default addPeriods;
