import { InternalAxiosRequestConfig } from "axios";
import Cookies from "js-cookie";

import { api } from "@/http/api-client";
import { HttpMethod } from "@/types/httpMeethod";

export const XsrfRequestInterceptor = async (
  config: InternalAxiosRequestConfig
) => {
  config.headers["X-XSRF-TOKEN"] = Cookies.get("XSRF-TOKEN");

  if (
    (config.method == HttpMethod.POST ||
      config.method == HttpMethod.PUT ||
      config.method == HttpMethod.DELETE) &&
    !Cookies.get("XSRF-TOKEN")
  ) {
    await api.get("/sanctum/csrf-cookie").then(() => {
      config.headers["X-XSRF-TOKEN"] = Cookies.get("XSRF-TOKEN");
    });
  }

  return config;
};
