import { isValid } from "@thebank/creditcard-js";
import { z } from "zod";

import { DocumentType } from "@/types/document";
import { PaymentOption } from "@/types/paymentOptions";
import { clearString } from "@/utils/stringUtils";
import { isCNPJ, isCPF } from "@/utils/validations";

export const createBuyerSchema = (
  t: (key: string) => string,
  haveBrazilianDocument: boolean,
  selectedOption: PaymentOption
) => {
  const baseSchema = {
    email: z
      .string({ required_error: t("enter_email") })
      .min(1, { message: t("enter_email") })
      .max(254, { message: t("invalid_email") })
      .email(t("invalid_email"))
      .optional(),
    confirm_email: z
      .string({ required_error: t("enter_email") })
      .min(1, { message: t("enter_email") })
      .max(254, { message: t("invalid_email") })
      .email(t("invalid_email")),
    document: haveBrazilianDocument
      ? z
          .object(
            {
              type: z.nativeEnum(DocumentType),
              number: z
                .string({ required_error: t("document_error") })
                .min(1, { message: t("document_error") })
                .transform(value => clearString(value)),
            },
            { required_error: t("document_error") }
          )
          .refine(
            value => {
              const cleanValue = clearString(value.number);
              return value.type === DocumentType.CPF
                ? isCPF(cleanValue)
                : isCNPJ(cleanValue);
            },
            {
              message: t("invalid_document"),
              path: ["number"],
            }
          )
      : z.undefined(),
    name: z
      .string({ required_error: t("full_name_error") })
      .min(2, { message: t("full_name_error") })
      .max(256, { message: t("full_name_error") })
      .refine(
        value => {
          const nameParts = value.trim().split(/\s+/);
          return nameParts.length >= 2;
        },
        {
          message: t("full_name_error"),
        }
      ),
    phone: z.object(
      {
        ddi: z.string(),
        number: z
          .string({ required_error: t("phone_error") })
          .min(1, { message: t("phone_error") })
          .transform(value => clearString(value)),
      },
      { required_error: t("phone_error") }
    ),
    instalment: z.coerce.number().optional(),
  };

  const creditCardFields = {
    cardHolderName: z
      .string({ required_error: t("full_name_error") })
      .min(2, { message: t("full_name_error") })
      .max(256, { message: t("full_name_max_leght") })
      .refine(
        value => {
          const nameParts = value.trim().split(/\s+/);
          return nameParts.length >= 2;
        },
        {
          message: t("full_name_error"),
        }
      ),
    cardNumber: z
      .string({ required_error: t("card_number_placeholder") })
      .min(1, { message: t("card_number_placeholder") })
      .refine(value => isValid(value), {
        message: t("invalid_card_number"),
      }),
    cardCvv: z
      .string({ required_error: t("card_code_placeholder") })
      .min(3, { message: t("card_code_placeholder") })
      .max(4, { message: t("cvv_card_max") }),
    cardExpirationDateMonth: z.string({ required_error: t("select_month") }),
    cardExpirationDateYear: z.string({ required_error: t("select_year") }),
  };

  // const billingAddressSchema = z.object({
  //   zipcode: z
  //     .string({ required_error: t("cep_error") })
  //     .min(1, { message: t("cep_error") })
  //     .max(9, { message: t("cep_max_length") })
  //     .refine(
  //       value => {
  //         const cleanValue = clearString(value);
  //         return isCEP(cleanValue);
  //       },
  //       {
  //         message: t("valid_cep"),
  //       }
  //     )
  //     .refine(value => clearString(value).length === 8, {
  //       message: t("cep_exact_length"),
  //     }),
  //   street: z
  //     .string({ required_error: t("street_error") })
  //     .min(1, { message: t("street_error") })
  //     .max(256, { message: t("street_max_length") }),
  //   district: z
  //     .string({ required_error: t("district_error") })
  //     .min(1, { message: t("district_error") })
  //     .max(100, { message: t("district_max_length") }),
  //   complement: z
  //     .string()
  //     .max(50, { message: t("complement_max_length") })
  //     .optional()
  //     .nullable(),
  //   city: z
  //     .string({ required_error: "Informe a cidade" })
  //     .min(1, { message: "Informe a cidade" })
  //     .max(100, { message: "A cidade deve ter no máximo 100 caracteres" }),

  //   state: z.string({
  //     required_error: t("select_state"),
  //   }),
  //   country: z.string().default("br"),
  // });

  const schema =
    selectedOption === PaymentOption.CreditCard
      ? z
          .object({
            ...baseSchema,
            ...creditCardFields,
          })
          .superRefine((data, ctx) => {
            if (
              selectedOption === PaymentOption.CreditCard &&
              (!data.instalment || data.instalment < 1)
            ) {
              ctx.addIssue({
                code: "custom",
                path: ["instalment"],
                message: t("instalment_required"),
              });
            }
          })
      : z.object(baseSchema);

  return schema.refine(data => data.email === data.confirm_email, {
    message: t("email_mismatch"),
    path: ["confirm_email"],
  });
};

export type BuyerFormValues = z.infer<ReturnType<typeof createBuyerSchema>>;

export type BuyerFormValuesPartial = Partial<{
  cardHolderName: string;
  cardNumber: string;
  cardCvv: string;
  cardExpirationDateMonth: string;
  cardExpirationDateYear: string;
}> & {
  name: string;
  email: string;
  confirm_email: string;
  instalment: number;
  main_product_id: string;
  phone: {
    ddi: string;
    number: string;
  };
  document?: {
    type: DocumentType;
    number: string;
  };
  billing_address: {
    zipcode: string;
    street: string;
    district: string;
    city: string;
    state: string;
    complement: string;
    country: string;
    number?: string;
  };
};
