import { z } from "zod";

import { PaymentOption } from "@/types/paymentOptions";
import { clearString } from "@/utils/stringUtils";

export const createSubscriptionRenewalSchema = (
  t: (key: string) => string,
  selectedOption: PaymentOption
) => {
  const creditCardFields = {
    cardNumber: z
      .string({ required_error: t("card_number_placeholder") })
      .min(1, { message: t("card_number_placeholder") })
      .transform(value => clearString(value)),
    cardHolderName: z
      .string({ required_error: t("full_name_error") })
      .min(2, { message: t("full_name_error") })
      .max(256, { message: t("full_name_max_leght") }),
    cardCvv: z
      .string({ required_error: t("card_code_placeholder") })
      .min(3, { message: t("card_code_placeholder") })
      .max(4, { message: t("cvv_card_max") }),
    cardExpirationDateMonth: z
      .string({ required_error: t("select_month") })
      .min(1, { message: t("select_month") }),
    cardExpirationDateYear: z
      .string({ required_error: t("select_year") })
      .min(1, { message: t("select_year") }),
    instalment: z
      .number({ required_error: t("instalment_required") })
      .min(1, { message: t("instalment_required") }),
  };

  const schema =
    selectedOption === PaymentOption.CreditCard
      ? z
          .object({
            ...creditCardFields,
          })
          .superRefine((data, ctx) => {
            if (
              selectedOption === PaymentOption.CreditCard &&
              (!data.instalment || data.instalment < 1)
            ) {
              ctx.addIssue({
                code: "custom",
                path: ["instalment"],
                message: t("instalment_required"),
              });
            }
          })
      : z.object({});

  return schema;
};

export type SubscriptionRenewalFormValues = z.infer<
  ReturnType<typeof createSubscriptionRenewalSchema>
>;

export type SubscriptionRenewalFormValuesPartial = Partial<{
  cardHolderName: string;
  cardNumber: string;
  cardCvv: string;
  cardExpirationDateMonth: string;
  cardExpirationDateYear: string;
  instalment: number;
}>;
