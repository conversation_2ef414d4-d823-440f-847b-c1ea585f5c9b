import { DocumentType } from "@/types/document";
import { formatToCNPJ, formatToCPF } from "@/utils/formatting";
import { isCNPJ, isCPF } from "@/utils/validations";

interface DocumentHandler {
  validate: (document: string) => boolean;
  format: (document: string) => string;
}

const documentHandlers: Record<DocumentType, DocumentHandler> = {
  [DocumentType.CPF]: {
    validate: isCPF,
    format: formatToCPF,
  },
  [DocumentType.CNPJ]: {
    validate: isCNPJ,
    format: formatToCNPJ,
  },
};

export function getDocumentHandler(type: DocumentType): DocumentHandler {
  const handler = documentHandlers[type];

  if (!handler) {
    throw new Error(`Unsupported document type: ${type}`);
  }

  return handler;
}
