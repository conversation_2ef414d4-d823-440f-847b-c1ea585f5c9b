import crypto from "crypto";
import { merge } from "lodash";

import {
  AwaitingPaymentOrder,
  AwaitingPaymentSubscription,
} from "@/types/awaitingPayment";
import { OrderStatus } from "@/types/orderResponse";
import { RecursivePartial } from "@/types/utils";

function makeAwaitingPaymentBase() {
  return {
    transaction: {
      id: crypto.randomUUID(),
      status: OrderStatus.PENDING,
      amount: 1490,
      payment_method: "credit_card",
      description: "1x de R$14,90",
      error: {
        title: "Erro ao processar",
        description: "Verifique com a operadora do cartão.",
      },
    },
    credit_card: {
      token: crypto.randomUUID(),
      brand: "visa",
      first_digits: 411111,
      last_digits: 4242,
      installments: 1,
    },
    product: {
      id: crypto.randomUUID(),
      title: "Produto Teste",
      description: "Descrição do produto teste",
      platform: {
        name: "Plataforma XPTO",
        url: "https://example.com",
      },
    },
    bumps: [],
  };
}

export function makeAwaitingPaymentData(
  type: "order",
  overrides?: RecursivePartial<AwaitingPaymentOrder>
): AwaitingPaymentOrder;
export function makeAwaitingPaymentData(
  type: "subscription",
  overrides?: RecursivePartial<AwaitingPaymentSubscription>
): AwaitingPaymentSubscription;
export function makeAwaitingPaymentData(
  type: "order" | "subscription",
  overrides?: RecursivePartial<
    AwaitingPaymentOrder | AwaitingPaymentSubscription
  >
): AwaitingPaymentOrder | AwaitingPaymentSubscription {
  const baseData = makeAwaitingPaymentBase();

  if (type === "order") {
    const defaultOrder: AwaitingPaymentOrder = {
      ...baseData,
      order_id: crypto.randomUUID(),
    };
    return merge({}, defaultOrder, overrides);
  }

  const defaultSubscription: AwaitingPaymentSubscription = {
    ...baseData,
    subscription_code: crypto.randomUUID(),
    transaction: {
      ...baseData.transaction,
      amount: 3560,
      description: "1x de R$35,60",
    },
    credit_card: {
      ...baseData.credit_card,
      brand: "mastercard",
      first_digits: 517912,
      last_digits: 3590,
    },
    product: {
      ...baseData.product,
      title: "InvestLab: Laboratório de Investimentos",
      description: "Assinatura mensal do laboratório de investimentos",
      platform: {
        name: "Plataforma InvestLab",
        url: "https://investlab.example.com",
      },
    },
  };
  return merge({}, defaultSubscription, overrides);
}

export function makeAwaitingPaymentOrder(
  overrides?: RecursivePartial<AwaitingPaymentOrder>
): AwaitingPaymentOrder {
  return makeAwaitingPaymentData("order", overrides);
}

export function makeAwaitingPaymentSubscription(
  overrides?: RecursivePartial<AwaitingPaymentSubscription>
): AwaitingPaymentSubscription {
  return makeAwaitingPaymentData("subscription", overrides);
}
