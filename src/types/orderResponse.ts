export interface OrderResponse {
  buyer: Buyer;
  order: Order;
  transaction: Transaction;
  credit_card: CreditCard;
  pix: Pix;
  boleto: Boleto;
  product: Product;
  bumps: Bump[];
}

export interface Buyer {
  id: string;
  name: string;
  email: string;
}

export interface Order {
  id: string;
  status: string;
}

export interface Transaction {
  id: string;
  status: string;
  amount: number;
  payment_method: string;
  description: string;
  error: {
    title: string;
    description: string;
  };
}

export interface CreditCard {
  token: string;
  brand: string;
  first_digits: number;
  last_digits: number;
  installments: number;
}

export interface Pix {
  expires: number;
  qr_code: string;
  copy_paste: string;
}

export interface Boleto {
  expires_date: string;
  barcode_data: string;
  barcode_image_url: string;
  instructions: any;
}

export interface Product {
  id: string;
  title: string;
  description: string;
  platform: Platform;
}

export interface Platform {
  name: string;
  url: string;
}

export interface Bump {
  id: string;
  title: string;
  description: string;
}

export enum OrderStatus {
  PENDING = "pending",
  PAID = "paid",
  FAILED = "failed",
  APPROVED = "approved",
  TRIALED = "trialed",
}
