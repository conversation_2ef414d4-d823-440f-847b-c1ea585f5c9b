/// <reference types="msw" />

declare global {
  interface Params {
    [key: string]: string;
  }

  type HandlerArgs = {
    req: import("msw").MockedRequest & { params: Params };
    res: import("msw").ResponseComposition<any>;
    ctx: import("msw").DefaultContext;
  };

  type HttpMethods =
    | "GET"
    | "POST"
    | "PUT"
    | "DELETE"
    | "PATCH"
    | "HEAD"
    | "OPTIONS";

  type MswCoreHandler = (props: HandlerArgs) => ReturnType<HandlerArgs["res"]>;

  type MswCoreScenario = {
    scenarioOf: string;
    id: string;
    order?: number;
    description: string;
    handler: Msw<PERSON>oreHandler;
  };

  type MswCoreResource = {
    id: string;
    url: string;
    method: HttpMethods;
    order?: number;
    delay?: number;
    enabled?: boolean;
    randomDelay?: boolean;
    title?: string;
    description: string;
    handler: <PERSON>w<PERSON>ore<PERSON>and<PERSON>;
    currentScenario?: string | null;
  };

  type UpdatableResource = Pick<
    MswCoreResource,
    "enabled" | "delay" | "randomDelay" | "currentScenario"
  >;

  type MswCoreHandlerPresentation = Pick<
    MswCoreResource,
    | "id"
    | "method"
    | "url"
    | "delay"
    | "title"
    | "description"
    | "enabled"
    | "randomDelay"
  > & {
    currentScenario?: string;
    scenarios: Pick<MswCoreScenario, "id" | "description">[];
  };

  type MwsCookie = {
    key: string;
    value: string;
    domain: string;
  };
}

export {};
