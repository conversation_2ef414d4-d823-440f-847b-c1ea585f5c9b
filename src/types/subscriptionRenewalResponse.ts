export interface SubscriptionRenewalTransaction {
  id: string;
  status: "approved" | "failed" | "pending";
  amount: number;
  payment_method: "credit_card" | "pix" | "boleto";
  description: string;
  error?: {
    title: string;
    description: string;
  };
}

export interface SubscriptionRenewalCreditCard {
  token: string;
  brand: string;
  first_digits: number;
  last_digits: number;
  installments: number;
}

export interface SubscriptionRenewalPix {
  expires: number;
  qr_code: string;
  copy_paste: string;
}

export interface SubscriptionRenewalBoleto {
  expires_date: number;
  barcode_image_url: string;
  barcode_data: string;
}

export interface SubscriptionRenewalProduct {
  title: string;
  platform: {
    name: string;
    url: string;
  };
}

export interface SubscriptionRenewalSubscriber {
  name: string;
}

export interface SubscriptionRenewalPeriodicity {
  type: string;
  interval: number;
}

export interface SubscriptionRenewalResponse {
  code?: string;
  price?: string;
  product?: SubscriptionRenewalProduct;
  periodicity?: SubscriptionRenewalPeriodicity;
  subscriber?: SubscriptionRenewalSubscriber;
  transaction: SubscriptionRenewalTransaction;
  credit_card?: SubscriptionRenewalCreditCard;
  pix?: SubscriptionRenewalPix;
  boleto?: SubscriptionRenewalBoleto;
}

export enum SubscriptionRenewalStatus {
  APPROVED = "approved",
  FAILED = "failed",
  PENDING = "pending",
}
