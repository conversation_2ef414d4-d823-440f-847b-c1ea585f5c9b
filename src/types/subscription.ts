export interface SubscriptionInstallment {
  installments: number;
  total: number;
  description: string;
}

export interface SubscriptionProduct {
  title: string;
  payment_rules: string[];
  platform: {
    name: string;
    url: string;
  };
}

export interface SubscriptionPeriodicity {
  type: string;
  label: string;
  interval: number;
  cycle_count: number;
}

export interface SubscriptionSubscriber {
  name: string;
  email: string;
  document: string;
  phone: string;
}

export interface SubscriptionApiResponse {
  price: string;
  credit_card_installments: SubscriptionInstallment[];
  product: SubscriptionProduct;
  periodicity: SubscriptionPeriodicity;
  subscriber: SubscriptionSubscriber;
}

export interface SubscriptionProductForComponents {
  id: string;
  title: string;
  price: number;
  payment_methods: Array<"credit_card" | "pix" | "boleto">;
  platform: {
    name: string;
    url: string;
  };
  subscription: {
    interval: number;
    periodicity: "monthly";
    trial_days: number | null;
  };
}

export function createSubscriptionProductForComponents(
  subscriptionId: string,
  apiData: SubscriptionApiResponse
): SubscriptionProductForComponents {
  return {
    id: subscriptionId,
    title: apiData.product.title,
    price: apiData.credit_card_installments[0]?.total || 0,
    payment_methods: apiData.product.payment_rules as Array<
      "credit_card" | "pix" | "boleto"
    >,
    platform: {
      name: apiData.product.platform.name,
      url: apiData.product.platform.url,
    },
    subscription: {
      interval: apiData.periodicity.interval,
      periodicity: "monthly",
      trial_days: null,
    },
  };
}
