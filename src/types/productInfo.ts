export type InfoData = {
  id: string;
  title: string;
  description: string;
  charge_type?: string;
  subscription?: {
    periodicity: string;
    interval: number;
    trial_days?: number;
    next_billing_at?: string;
  };
  platform: {
    name: string;
  };
};

export type AdditionalProductInfo = {
  id: string;
  title: string;
  description: string;
};

export type ProductInfoProps = {
  className?: string;
  infoData?: InfoData;
  additionalProductInfo?: AdditionalProductInfo[];
};
