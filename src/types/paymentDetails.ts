export type PixPaymentDetails = {
  payment_method: "pix";
  order_id?: string;
  subscription_id?: string;
  status: string;
  pix: {
    amount: number;
    expires_in: number;
    qr_code: string;
    copy_paste: string;
  };
  product: {
    id: string;
    title: string;
    description: string;
    platform: {
      name: string;
    };
  };
  bumps: {
    id: string;
    title: string;
    description: string;
  }[];
};

export type BoletoPaymentDetails = {
  order_id?: string;
  subscription_id?: string;
  payment_method: string;
  status: string;
  customer_email: string;
  boleto: {
    amount: number;
    barcode_data: string;
    barcode_image: string;
    expires_date: string;
  };
  bumps: {
    id: string;
    title: string;
    description: string;
  }[];
  product: {
    id: string;
    title: string;
    description: string;
    platform: {
      name: string;
    };
  };
};
