type Product = {
  id: string;
  quantity: number;
};

type Document = {
  type: "cpf" | "cnpj";
  number: string;
};

type Phone = {
  ddi: string;
  number: string;
};

type CreditCard = {
  card_token: string;
  first_digits: string;
  last_digits: string;
  brand: string;
};

type Payment = {
  method: "credit_card" | "pix" | "boleto";
  card_token?: string;
  installments?: number;
};

type Buyer = {
  name: string;
  email: string;
  confirm_email: string;
  document?: Document;
  phone: Phone;
  credit_card?: CreditCard;
};

export type OrderPayload = {
  products: Product[];
  buyer: Buyer;
  main_product_id: string;
  payment: Payment;
};
