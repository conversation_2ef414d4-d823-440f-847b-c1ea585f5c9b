export enum OrganizationStatus {
  COMPLETED = "completed",
  REFUSED = "refused",
  PENDING = "pending",
  MISSING = "missin",
}

type ProductPlatform = {
  id: string;
  name: string;
  url: string;
};

type Organization = {
  id: string;
  status: {
    value: string;
    message: string;
  };
};

export type ProductBump = {
  id: string;
  product_id: string;
  title: string;
  position: number;
  cta: string;
  description: string;
  price: number;
  charge_type: ProductChargeType;
  subscription?: {
    interval: number;
    periodicity: ProductPeriodicity;
    trial_days: number | null;
  };
};

export enum ProductPeriodicity {
  MONTHLY = "monthly",
}

export enum ProductChargeType {
  ONE_TIME = "oneoff",
  SUBSCRIPTION = "subscription",
}

export type Product = {
  id: string;
  organization_id: string;
  title: string;
  description: string;
  image: string | null;
  price: number;
  installment_details: string;
  warranty_days: number;
  sales_page: string;
  charge_type: ProductChargeType;
  payment_methods: Array<"credit_card" | "pix" | "boleto">;
  platform: ProductPlatform;
  organization: Organization;
  bumps: ProductBump[];
  pixels: Pixel[];
  subscription?: {
    interval: number;
    periodicity: ProductPeriodicity;
    trial_days: number | null;
  };
};

export interface PixelCredential {
  id: string;
  domain: string;
}

export interface PixelCredentials {
  events: string[];
  pixels: PixelCredential[];
}

export interface Pixel {
  provider: PixelProviders;
  events: null;
  credentials: PixelCredentials;
}

export enum PixelProviders {
  META = "meta",
  GOOGLE_ANALYTICS = "google_analytics",
  TIKTOK = "tiktok",
  GOOGLE_ADS = "google_ads",
}

export interface GoogleAdsCredential {
  pixel_id: string;
  conversion_label: string;
  events: string[];
}
