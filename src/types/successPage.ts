import { Bump } from "./orderResponse";

type Platform = {
  name: string;
  url: string;
};

export type Product = {
  id: string;
  title: string;
  description: string;
  charge_type?: string;
  subscription?: {
    periodicity: string;
    interval: number;
    trial_days?: number;
  };
  platform: Platform;
};

export type Payment = {
  status: string;
  brand: string;
  brand_url_image: string;
  description: string;
  first_digits: string;
  last_digits: string;
  installments?: string;
  error?: {
    title: string;
    description: string;
  };
};

export type OrderPaymentResponse = {
  subscription_id?: number;
  order_id?: number;
  thank_you_page_url?: string;
  payment_method: string;
  customer_email: string;
  product: Product;
  payment: Payment;
  bumps: Bump[];
};
