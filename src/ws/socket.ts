import * as Ably from "ably";
import Echo from "laravel-echo";
import Pusher from "pusher-js";

declare global {
  interface Window {
    Echo: Echo<"pusher" | "ably">;
    Ably: typeof Ably;
    Pusher: typeof Pusher;
  }
}

// Singleton para controlar a inicialização do socket
let socketInitialized = false;
let socketInitializing = false;
let initializationPromise: Promise<void> | null = null;

export function createSocketConnection(): Promise<void> {
  // Se já foi inicializado, resolve imediatamente
  if (socketInitialized && typeof window !== "undefined" && window.Echo) {
    return Promise.resolve();
  }

  // Se está em processo de inicialização, retorna a promise existente
  if (socketInitializing && initializationPromise) {
    return initializationPromise;
  }

  // Marca como inicializando e cria nova promise
  socketInitializing = true;
  initializationPromise = new Promise((resolve, reject) => {
    try {
      if (typeof window !== "undefined") {
        // Verifica novamente se já existe (race condition)
        if (window.Echo && socketInitialized) {
          socketInitializing = false;
          resolve();
          return;
        }

        const provider = process.env.NEXT_PUBLIC_WEBSOCKET_PROVIDER || "ably";

        // Log apenas em desenvolvimento
        if (process.env.NODE_ENV === "development") {
          console.log("Initializing WebSocket provider:", provider);
        }

        if (provider === "soketi") {
          window.Pusher = Pusher;
          window.Echo = new Echo({
            broadcaster: "pusher",
            key: process.env.NEXT_PUBLIC_SOKETI_APP_KEY,
            wsHost: process.env.NEXT_PUBLIC_SOKETI_HOST,
            wsPort: Number(process.env.NEXT_PUBLIC_SOKETI_PORT),
            forceTLS: false,
            disableStats: true,
            enabledTransports: ["ws", "wss"],
            authEndpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/broadcasting/auth`,
          });
        } else if (provider === "ably") {
          window.Ably = Ably;
          window.Echo = new Echo({
            broadcaster: "ably",
            key: process.env.NEXT_PUBLIC_ABLY_KEY,
            authEndpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/broadcasting/auth`,
          });
        } else {
          throw new Error(`Unsupported WebSocket provider: ${provider}`);
        }

        socketInitialized = true;
        socketInitializing = false;

        if (process.env.NODE_ENV === "development") {
          console.log("WebSocket connection initialized successfully");
        }

        resolve();
      } else {
        socketInitializing = false;
        reject(new Error("Window is not defined"));
      }
    } catch (error) {
      socketInitializing = false;
      socketInitialized = false;
      console.error("Error creating socket connection:", error);
      reject(error);
    }
  });

  return initializationPromise;
}

// Função para verificar se o socket está disponível
export function isSocketReady(): boolean {
  return socketInitialized && typeof window !== "undefined" && !!window.Echo;
}

// Função para resetar o estado (útil para testes)
export function resetSocketState(): void {
  socketInitialized = false;
  socketInitializing = false;
  initializationPromise = null;

  if (typeof window !== "undefined") {
    if (window.Echo && typeof window.Echo.disconnect === "function") {
      window.Echo.disconnect();
    }
    (window as any).Echo = undefined;
    (window as any).Ably = undefined;
    (window as any).Pusher = undefined;
  }
}
