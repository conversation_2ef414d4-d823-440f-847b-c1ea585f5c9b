import * as Ably from "ably";
import Echo from "laravel-echo";
import Pusher from "pusher-js";

declare global {
  interface Window {
    Echo: Echo<"pusher" | "ably">;
    Ably: typeof Ably;
    Pusher: typeof Pusher;
  }
}

export function createSocketConnection(): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      if (typeof window !== "undefined") {
        if (window.Echo) {
          console.log("Echo already initialized");
          resolve();
          return;
        }

        const provider = process.env.NEXT_PUBLIC_WEBSOCKET_PROVIDER || "ably";
        console.log("WebSocket provider:", provider);

        if (provider === "soketi") {
          window.Pusher = Pusher;
          window.Echo = new Echo({
            broadcaster: "pusher",
            key: process.env.NEXT_PUBLIC_SOKETI_APP_KEY,
            wsHost: process.env.NEXT_PUBLIC_SOKETI_HOST,
            wsPort: Number(process.env.NEXT_PUBLIC_SOKETI_PORT),
            forceTLS: false,
            disableStats: true,
            enabledTransports: ["ws", "wss"],
            authEndpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/broadcasting/auth`,
          });
        } else if (provider === "ably") {
          window.Ably = Ably;
          window.Echo = new Echo({
            broadcaster: "ably",
            key: process.env.NEXT_PUBLIC_ABLY_KEY,
            authEndpoint: `${process.env.NEXT_PUBLIC_BASE_URL}/broadcasting/auth`,
          });
        } else {
          throw new Error(`Unsupported WebSocket provider: ${provider}`);
        }

        console.log("Echo initialized:", !!window.Echo);
        resolve();
      } else {
        reject(new Error("Window is not defined"));
      }
    } catch (error) {
      console.error("Error creating socket connection:", error);
      reject(error);
    }
  });
}
