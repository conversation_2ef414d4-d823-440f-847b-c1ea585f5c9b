import { withSentryConfig } from "@sentry/nextjs";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin("./i18n/request.ts");

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: [
      "logospng.org",
      "upload.wikimedia.org",
      "placehold.co",
      "example.com",
      "transactions-qrcode-pix-prod.s3.amazonaws.com",
      "e7.pngegg.com",
    ],
  },
  experimental: {
    instrumentationHook: true,
  },
};

const sentryWebpackPluginOptions = {
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  silent: true,
};

export default withSentryConfig(
  withNextIntl(nextConfig),
  sentryWebpackPluginOptions
);
