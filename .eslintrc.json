{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:jsx-a11y/recommended", "prettier"], "plugins": ["@typescript-eslint", "react", "jsx-a11y", "simple-import-sort"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/jsx-sort-props": "off", "jsx-a11y/anchor-is-valid": "warn", "jsx-a11y/heading-has-content": "off", "@next/next/no-img-element": "off", "jsx-a11y/alt-text": "off", "simple-import-sort/imports": "error", "import/order": "off", "no-console": "off", "prefer-const": "error"}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", ".next/", "out/"]}