{"home": {"info_purchase": "*The value is split into installments.", "purchase_disclaimer": "By completing the purchase, you declare that you have read and agree (i) that TheMembers checkout is processing this order on behalf of {organization} and has no responsibility for the content and/or prior control of it; (ii) with our", "privacy_policy": "privacy policy", "and": "and with the", "terms_of_use": "terms of use", "help_text": "Can't complete this purchase?", "help_page": "Visit our help page", "all_rights_reserved": "All rights reserved", "purchase_disclaimer_credit_card": "The value is split into installments.", "card_brand_not_supported": "Card brand not supported for subscription. Use a different one.", "card_brand_not_supported_one_time": "Card brand not supported. Use a different one.", "country_not_supported": "No country found"}, "common": {"back": "Back"}, "language_switcher": {"english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portuguese": "Português", "spanish": "Espanhol"}, "buyer_form": {"first_charge_on": "First charge on", "author": "Author:", "at_view": "at view", "or": "or", "submit_button_subscription": "Subscribe now", "purchase_summary": "Purchase Summary", "group_buy": "Take advantage and buy together:", "add_product": "Add product", "remove_product": "Remove product", "add_subscription": "Add subscription", "remove_subscription": "Remove subscription", "instalment_required": "Select the instalment", "full_name": "Full Name", "full_name_error": "Enter your full name", "full_name_max_leght": "Maximum 256 characters", "email": "Email", "confirm_email": "Confirm Email", "cpf_or_cnpj": "CPF or CNPJ", "phone_number": "Phone Number", "submit_button": "Buy Now", "pix_generate": "Generate Pix", "generate_boleto": "Generate Boleto", "name_placeholder": "Ex: <PERSON>", "email_placeholder": "Ex: <EMAIL>", "confirm_email_placeholder": "Ex: <EMAIL>", "document_placeholder": "Enter the document", "phone_placeholder": "(00) 90000-0000", "email_mismatch": "The emails do not match", "invalid_email": "Report or email again", "invalid_document": "Enter a valid document", "invalid_phone": "Enter a valid phone number", "required_field": "This field is required", "dont_have_brazilianDocument": "I don't have a Brazilian document", "card_number_placeholder": "Enter the card number", "card_code_placeholder": "Enter the code", "select_month": "Select the month", "select_year": "Select the year", "enter_email": "Enter an email", "document_error": "Enter the document number", "phone_error": "Provide a cell phone number", "card_number_max_length": "Must have a maximum of 16 digits", "card": "Card", "card_number": "Card number", "owner_card": "Cardholder's name (as on the card)", "month": "Month", "year": "Year", "generate_pix": "Generate Pix", "cvv_card": "Card security code", "cvv_help": "The card security code, known as CVV or CVC, is a sequence of 3or 4 digits printed on the card. It is present only on cards that support online purchases and helps to ensure the security of transactions.", "invalid_card_number": "Invalid card number", "cvv_card_min": "Minimum 3 digits", "cvv_card_max": "Maximum 4 digits", "credit_card": "Card", "pix": "Pix", "boleto": "Boleto", "installment": "Installment", "select_installment": "Select the installment", "amount_to_be_paid": "Total to be paid:", "safe_buy": "Safe Buy", "data_protection_card_security": "We protect your card data using encryption to provide bank-level security", "pay_until_due_date": "Pay until the due date to ensure your purchase", "payment_processing_time": "It may take up to 3 business days for the payment to be processed", "immediate_approval": "Immediate approval with a short processing time", "secure_transaction_bcb": "Secure transaction guaranteed by the Central Bank", "easy_payment_via_bank_app": "Pay easily through your bank's app", "tokenization_error_title": "Unable to process payment", "tokenization_error_description": "Try again in a few moments.", "seller_registration_error_title": "Purchase not processed", "seller_registration_error_description": "The author of this product has not yet completed the registration on our platform, so the purchase cannot be made.", "understood": "Understood", "cep_error": "Enter the ZIP code", "cep_max_length": "The ZIP code must be at most 9 characters long", "valid_cep": "Enter a valid ZIP code", "cep_exact_length": "The ZIP code must be exactly 8 digits long", "street_error": "Enter the street", "street_max_length": "The street must be at most 256 characters long", "district_error": "Enter the district", "district_max_length": "The district must be at most 100 characters long", "complement_max_length": "The complement must be at most 50 characters long", "city_error": "Enter the city", "city_max_length": "The city must be at most 100 characters long", "select_state": "Select the state", "street": "Street", "district": "District", "number": "Number", "complement": "Complement", "city": "City", "state": "State", "street_placeholder": "Enter the street", "district_placeholder": "Enter the district", "number_placeholder": "Enter the number", "complement_placeholder": "House, Apartment", "city_placeholder": "Enter the city", "state_placeholder": "Select the state", "without_number": "Without number", "without_number_error": "Enter a number.", "free_trial": "Free trial of {days} days", "subscription": {"every": "every", "renews_every": "Renews every", "trial_period": "for", "day": "day", "days": "days", "month": "month", "months": "months", "year": "year", "years": "years", "then": "then", "first_charge_on": "First charge on"}}, "pix_page": {"change_payment_method": "Change payment method", "copy_pix_code": "Copy Pix code", "finalize_with_pix": "Complete the payment with Pix", "cant_finalize_payment": "Unable to complete payment?", "access_help_page": "Access our help page.", "payment_instructions": "Payment instructions", "pix_app_instruction_1": "Open your financial institution's app and access the Pix area;", "pix_app_instruction_2": "Choose the option to Scan the QR Code;", "pix_app_instruction_3": "Scan the QR Code and confirm the payment;", "amount": "Amount:", "product": "Your product:", "author": "Author:", "qr_code_instruction": "Alternatively, scan the QR Code below in your bank's app:", "order_success_message": "Your order has been placed, and after completing the payment, you can access your product content.", "subscription_success_message": "Your order has been placed, and after completing the payment, you can continue accessing the content of your subscription.", "approval_time": "Approval takes a maximum of 2 minutes", "copy_code_instruction": "Alternatively, copy the code below:", "copied_text": "Code copied", "additional_items": "Additional items:"}, "success": {"subscription_success_message": "The transaction was successfully completed and now you can continue accessing the content of your subscription. Happy studying!", "title": "Congratulations on your purchase!", "subscription_title": "Congratulations, your subscription has started!", "subscription_trial_message": "The transaction has been completed and in a few moments you will be able to access your product with the data we sent to <bold>{email}</bold>. Happy studying!", "subscription_help_message": "This purchase includes subscription products. If payment was made with a card, you will be notified by email one day before renewal. If it was via Pix or bank slip, we will send an email 3 days before with the link to access the QR Code or barcode, which will be valid for 4 days. Once payment is confirmed, your subscription will be guaranteed for the next period.", "description": "The transaction has been completed and in a few moments you will be able to access your product with the data we sent to your {email}. Happy studying!", "product_order": "Order:", "payment_info": "Payment Method", "payment_info_value": "Amount", "access_product": "Access my product", "help_page": "If you need help with this purchase, please contact the creator or", "help_page_link": "visit our help page.", "payment_info_success": "Pix to view", "payment_info_boleto": "Boleto to view", "create_access": "We are creating your access..."}, "boleto_page": {"payment_boleto": "Make payment with your boleto", "copy_boleto_code": "Copy boleto code", "change_payment_method": "Change payment method", "order_confirmation": "Your order has been placed and will be processed after payment approval. After that, you will receive access data in {email}.", "subscription_order_confirmation": "Your order has been placed and will be processed after payment approval. After that, you will receive access data in {email}.", "due_date": "Due date", "amount": "Amount", "boleto_code": "Boleto code", "view_boleto": "View boleto", "approval_delay": "Approval can take up to 3 business days to occur", "copied_text": "Code copied!"}, "credit_card_page": {"header": "Your payment is almost there...", "description": "Your purchase is being processed and may take a few moments, but we'll notify you as soon as it's approved.", "order": "Order:", "payment_method": "Payment method", "value": "Amount", "error": {"title": "We couldn't process your payment", "description": "You can go back to the purchase page and try again. If the issue persists, contact your card's support center for help.", "button": "Back to purchase page"}}, "signature": {"title": "Update your subscription payment", "your_data": "Your data", "full_name": "Full name", "email": "Email", "phone": "Phone", "document": "Document"}}