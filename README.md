# The Bank Checkout - Aplicação Next.js

Uma aplicação de checkout abrangente construída com Next.js 15, com
internacionalização, múltiplos métodos de pagamento e capacidades de
gerenciamento de assinaturas.

## 🚀 Visão Geral do Projeto

O Bank Checkout é uma solução moderna de checkout de e-commerce que suporta:

- **Compras de produtos** com múltiplos métodos de pagamento (Cartão de Crédito,
  PIX, Boleto)
- **Renovações de assinatura** com processamento automatizado de pagamentos
- **Suporte multi-idioma** (Português, Inglês, Espanhol)
- **Status de pagamento em tempo real** via conexões WebSocket
- **Design responsivo** otimizado para mobile e desktop

## 📁 Estrutura do Projeto

```
src/
├── app/                    # Páginas do Next.js App Router
│   ├── [locale]/          # Rotas internacionalizadas
│   │   ├── (app)/         # Páginas principais da aplicação
│   │   │   ├── [id]/      # Página de checkout do produto
│   │   │   ├── order/     # Páginas relacionadas a pedidos
│   │   │   ├── subscriptions/ # Páginas de assinatura
│   │   │   └── success/   # Página de sucesso
│   │   └── layout.tsx     # Layout específico do locale
│   └── layout.tsx         # Layout raiz
├── components/            # Componentes UI reutilizáveis
├── hooks/                # Hooks React customizados
├── services/             # Camadas de serviço da API
├── types/                # Definições de tipos TypeScript
├── utils/                # Funções utilitárias
└── ws/                   # Configuração WebSocket
```

## 🛣️ Sistema de Roteamento

### Rotas Principais

| Padrão da Rota                 | Propósito                      | Parâmetros Obrigatórios           |
| ------------------------------ | ------------------------------ | --------------------------------- |
| `/[locale]`                    | Redirecionamento raiz          | `locale` (pt-BR, en-US, es-ES)    |
| `/[locale]/[id]`               | Página de checkout do produto  | `locale`, `id` (ID do produto)    |
| `/[locale]/subscriptions/[id]` | Renovação de assinatura        | `locale`, `id` (ID da assinatura) |
| `/[locale]/success`            | Página de sucesso do pagamento | `locale`, `d` (dados codificados) |
| `/[locale]/order/[id]`         | Página de retry do pedido      | `locale`, `id` (ID do pedido)     |

### Rotas do Fluxo de Pagamento

| Rota                              | Propósito                  | Fonte de Dados                 |
| --------------------------------- | -------------------------- | ------------------------------ |
| `/[locale]/order/pix`             | Página de pagamento PIX    | Dados de pagamento codificados |
| `/[locale]/order/boleto`          | Página de pagamento Boleto | Dados de pagamento codificados |
| `/[locale]/order/waiting-payment` | Processamento de pagamento | Estado de carregamento         |

### Parâmetros de Query

- **Páginas de produto**: `?/pt-BR/<id produto admin>`
- **Páginas de assinatura**: `?signature=<signature>` (obrigatório para
  renovações)
- **Página de sucesso**: `?d=<base64_encoded_data>` (dados do resultado do
  pagamento)
- **Retry de pedido**: `?product=<product_id>&d=<encoded_data>` (contexto de
  retry)

## 🏗️ Arquitetura das Páginas

### Padrões de Busca de Dados

**Renderização do Lado do Servidor (SSR)**

- Páginas de produto usam `getProductData()` para dados iniciais
- Páginas de assinatura usam `getSubscriptionData()` para informações de
  renovação
- Todas as páginas buscam dados no servidor para SEO e performance

**Funcionalidades do Lado do Cliente**

- Status de pagamento em tempo real via WebSocket
- Validação e submissão de formulários
- Troca dinâmica de idioma

### Hierarquia de Componentes

```
Layout da Página:
├── LanguageSwitcher (canto superior direito)
├── ProductCard (informações do produto)
├── BuyerForm / SubscriptionRenewalPaymentForm
└── Footer (links legais)
```

### Parâmetros Obrigatórios por Página

**Checkout de Produto (`/[locale]/[id]`)**

- `id`: UUID do produto
- `locale`: Código do idioma
- Opcional: `signature` para acesso autenticado

**Renovação de Assinatura (`/[locale]/subscriptions/[id]`)**

- `id`: UUID da assinatura
- `locale`: Código do idioma
- Obrigatório: `signature` para autenticação

**Página de Sucesso (`/[locale]/success`)**

- `locale`: Código do idioma
- `d`: Dados do resultado do pagamento codificados em Base64

## 🧩 Componentes Principais

### Componentes Centrais

| Componente                       | Propósito                        | Props                                    |
| -------------------------------- | -------------------------------- | ---------------------------------------- |
| `ProductCard`                    | Exibir info e preço do produto   | `product`, `productInstallments`         |
| `BuyerForm`                      | Formulário principal de checkout | `product`, `productInstallments`         |
| `SubscriptionRenewalPaymentForm` | Pagamento de assinatura          | `product`, `subscriptionId`, `signature` |
| `LanguageSwitcher`               | Seleção de idioma                | `isRetryOrderPage?`                      |
| `Footer`                         | Links legais e marca             | `organization`                           |

### Componentes de Pagamento

| Componente             | Propósito                       |
| ---------------------- | ------------------------------- |
| `CreditCardFormFields` | Formulário de cartão de crédito |
| `PixCard`              | Opção de pagamento PIX          |
| `BoletoCard`           | Opção de pagamento Boleto       |
| `PaymentSelectCard`    | Seletor de método de pagamento  |

### Componentes Especializados

| Componente                | Propósito                                 |
| ------------------------- | ----------------------------------------- |
| `SubscriptionHeader`      | Exibição de info do cliente da assinatura |
| `LoadingOverlay`          | Indicador de processamento de pagamento   |
| `AlertSellerRegistration` | Avisos de verificação do vendedor         |

## 🔌 Integração com API

### Serviços Principais

**API de Produto**

```typescript
// Buscar dados do produto com parcelamentos
const { product, productInstallments } = await getProductData(
  id,
  acceptLanguage
);
```

**API de Assinatura**

```typescript
// Buscar dados da assinatura
const subscriptionData = await getSubscriptionData(
  subscriptionId,
  signature,
  acceptLanguage
);

// Renovar assinatura
await subscriptionService.renewSubscription(subscriptionId, payload, signature);
```

### Endpoints da API

| Endpoint                          | Método | Propósito                 |
| --------------------------------- | ------ | ------------------------- |
| `/v1/checkout/products/{id}`      | GET    | Detalhes do produto       |
| `/v1/checkout/subscriptions/{id}` | GET    | Informações da assinatura |
| `/v1/checkout/subscriptions/{id}` | POST   | Renovar assinatura        |
| `/v1/checkout/orders`             | POST   | Criar pedido              |

### Integração WebSocket

**Canais**

- `order.{orderId}` - Status de pagamento do pedido
- `subscription.{subscriptionId}` - Status de pagamento da assinatura

**Eventos**

- `payment.status` - Aprovação/rejeição do pagamento

## 🛠️ Configuração de Desenvolvimento

### Pré-requisitos

- Node.js 20+
- Gerenciador de pacotes Yarn

### Instalação

```bash
# Clonar o repositório
git clone <repository-url>
cd tb-web-checkout

# Instalar dependências
yarn install

# Configurar variáveis de ambiente
cp .env.example .env.local
# Configure suas variáveis de ambiente
```

### Variáveis de Ambiente

```bash
NEXT_PUBLIC_BASE_URL=<api-base-url>
NEXT_PUBLIC_TOKENIZATION_MALGA=<malga-tokenization-url>
SENTRY_ORG=<sentry-organization>
SENTRY_PROJECT=<sentry-project>
```

### Comandos de Desenvolvimento

```bash
# Iniciar servidor de desenvolvimento
yarn dev

# Build para produção
yarn build

# Iniciar servidor de produção
yarn start

# Executar testes
yarn test

# Executar testes com cobertura
yarn test:coverage

# Lint do código
yarn lint

# Corrigir problemas de linting automaticamente
yarn lint:fix

# Formatar código
yarn format

# Verificar se o código está formatado corretamente
yarn format:check

# Verificar linting e formatação
yarn check

# Corrigir linting e formatação
yarn fix

# Configurar ambiente completo
yarn setup:dev
```

## 🎨 Configuração do Ambiente de Desenvolvimento

### Extensões Obrigatórias do VSCode

Para garantir que todos sigam as mesmas regras de formatação e linting, instale
as seguintes extensões:

#### Essenciais

- **Prettier - Code formatter** (`esbenp.prettier-vscode`)
- **ESLint** (`dbaeumer.vscode-eslint`)
- **Tailwind CSS IntelliSense** (`bradlc.vscode-tailwindcss`)

#### Recomendadas

- **i18n Ally** (`lokalise.i18n-ally`)
- **TypeScript Importer** (`pmneo.tsimporter`)
- **Auto Rename Tag** (`formulahendry.auto-rename-tag`)
- **Path Intellisense** (`christian-kohler.path-intellisense`)

### Configuração Automática

O projeto já está configurado com:

1. **Formatação automática ao salvar** - O Prettier formatará automaticamente
   seus arquivos
2. **ESLint com correção automática** - Problemas de linting serão corrigidos
   automaticamente quando possível
3. **Organização de imports** - O plugin `simple-import-sort` organizará seus
   imports automaticamente
4. **Git hooks** - O código será verificado e formatado antes de cada commit

### Configuração Inicial

Para novos desenvolvedores:

```bash
# Após clonar o repositório e instalar dependências
yarn setup:dev
```

Este comando irá:

- Configurar os hooks do Git
- Formatar todo o código
- Verificar se tudo está funcionando

### Configurações Importantes

#### Desabilitar Conflitos

Se você tem outras extensões de formatação instaladas, certifique-se de que elas
não entrem em conflito:

1. **Desabilite** extensões como "Beautify" ou outros formatadores
2. **Desabilite** a organização automática de imports do TypeScript (já
   configurado no projeto)
3. **Use apenas** o Prettier como formatador padrão

#### Configurações do VSCode

O projeto inclui configurações específicas em `.vscode/settings.json` que:

- Definem o Prettier como formatador padrão
- Habilitam formatação ao salvar
- Configuram o ESLint para correção automática
- Desabilitam organizadores de import conflitantes

## 🔧 Guia de Organização de Imports

### Problema Comum

Às vezes o Simple Import Sort organiza os imports corretamente e depois eles
voltam a ficar "desarrumados". Isso acontece quando há conflito entre diferentes
organizadores de import no VSCode.

### Solução para Problemas de Conflito

Se você está enfrentando problemas com a organização de imports, siga estes
passos:

#### Passo 1: Verificar Extensões Conflitantes

1. Abrir VSCode
2. Ir em Extensions (Ctrl+Shift+X)
3. **Desabilitar** estas extensões se estiverem instaladas:
   - TypeScript Importer
   - Auto Import - ES6, TS, JSX, TSX
   - Organize Imports
   - Qualquer outra extensão de organização de imports

#### Passo 2: Verificar Configurações do Usuário

1. Abrir Settings (Ctrl+,)
2. Procurar por "organize imports"
3. **Desmarcar** todas as opções relacionadas a organização automática de
   imports
4. Procurar por "typescript organize"
5. **Desmarcar** "TypeScript › Preferences: Organize Imports"

#### Passo 3: Recarregar VSCode

1. Pressionar Ctrl+Shift+P
2. Digitar "Developer: Reload Window"
3. Pressionar Enter

#### Passo 4: Testar a Configuração

1. Abrir qualquer arquivo .tsx/.ts
2. Adicionar um import no meio dos outros imports
3. Salvar (Ctrl+S)
4. Verificar se os imports foram organizados corretamente pelo Simple Import
   Sort

### Configuração Manual (Se Necessário)

Se ainda não funcionar, adicione estas configurações no settings.json do
usuário:

```json
{
  "typescript.preferences.organizeImports": false,
  "typescript.suggest.organizeImports": false,
  "editor.codeActionsOnSave": {
    "source.organizeImports": "never"
  }
}
```

### Verificação Final

Para verificar se está funcionando:

1. Execute `yarn lint:fix` no terminal
2. Os imports devem ser organizados seguindo esta ordem:
   - Imports de bibliotecas externas (react, next, etc.)
   - Linha em branco
   - Imports relativos (./components, ../utils, etc.)

### Comandos de Emergência

Se nada funcionar:

```bash
# Reinstalar node_modules
rm -rf node_modules && yarn install

# Forçar formatação
yarn fix

# Verificar se está tudo ok
yarn check
```

### Regras do Simple Import Sort

O plugin organiza os imports nesta ordem:

1. **Side effect imports** (import './styles.css')
2. **Node modules** (import React from 'react')
3. **Absolute imports** (import { Button } from 'components/Button')
4. **Relative imports** (import './Component.css')

Dentro de cada grupo, os imports são ordenados alfabeticamente.

### Exemplo de Organização Correta

```typescript
// Side effects
import "./globals.css";

// Node modules
import React from "react";
import { NextPage } from "next";
import { useRouter } from "next/router";

// Absolute imports
import { Button } from "components/Button";
import { Header } from "components/Header";

// Relative imports
import { useLocalState } from "../hooks/useLocalState";
import "./Component.module.css";
```

## 🚨 Resolução de Problemas

### Imports sendo reorganizados incorretamente

Se os imports estão sendo reorganizados de forma inconsistente:

1. Verifique se você tem apenas o plugin `eslint-plugin-simple-import-sort`
   ativo
2. Desabilite outras extensões de organização de imports
3. Reinicie o VSCode

### Formatação inconsistente

Se o código não está sendo formatado consistentemente:

1. Verifique se o Prettier está definido como formatador padrão
2. Execute `yarn format` manualmente
3. Verifique se não há conflitos com outras extensões

### Git hooks não funcionando

Se os hooks do Git não estão executando:

1. Execute `yarn prepare` para reinstalar os hooks
2. Verifique se o Husky está instalado corretamente

### Comandos de Emergência

Se algo der errado, você pode sempre executar:

```bash
# Reinstalar dependências
rm -rf node_modules && yarn install

# Reinstalar hooks do Git
yarn prepare

# Formatar todo o projeto
yarn fix

# Configurar ambiente completo
yarn setup:dev
```

### Testes

O projeto inclui cobertura abrangente de testes:

- **Testes unitários** para componentes e utilitários
- **Testes de integração** para serviços da API
- **Testes E2E** para fluxos críticos do usuário

Execute os testes com:

```bash
yarn test:watch    # Modo watch
yarn test:coverage # Relatório de cobertura
```

## 🌍 Internacionalização

Suporta três idiomas:

- **Português (pt-BR)** - Padrão
- **Inglês (en-US)**
- **Espanhol (es-ES)**

Arquivos de tradução localizados em `/locales/{locale}/common.json`

## 🔧 Tecnologias Principais

- **Next.js 15** - Framework React com App Router
- **TypeScript** - Segurança de tipos
- **Tailwind CSS** - Estilização
- **next-intl** - Internacionalização
- **React Hook Form** - Gerenciamento de formulários
- **Zod** - Validação de esquemas
- **Axios** - Cliente HTTP
- **Ably** - Conexões WebSocket
- **Sentry** - Monitoramento de erros

## 📱 Suporte a Navegadores

- Navegadores modernos (Chrome, Firefox, Safari, Edge)
- Design responsivo para mobile
- Recursos de Progressive Web App

## 🚀 Deploy

### Produção

Quando um merge é feito na branch main, ocorre deploy automático para produção
na AWS. Não é necessário acompanhar pelo GitHub Actions.

### Ambiente de QA

1. Vá para a aba Actions no GitHub
2. Selecione "Deploy to QA"
3. Escolha a branch que deseja fazer deploy

**Nota**: Ao fazer merge com a main, o ambiente de QA será automaticamente
excluído.

---

Para documentação detalhada de componentes e especificações da API, consulte os
arquivos individuais no codebase.
