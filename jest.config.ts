import type { Config } from "jest";
import nextJest from "next/jest.js";

const createJestConfig = nextJest({
  dir: "./src",
});

const config: Config = {
  coverageProvider: "v8",
  testEnvironment: "jsdom",
  setupFilesAfterEnv: ["<rootDir>/.jest/setup.ts"],
  moduleNameMapper: {
    "@/(.*)$": "<rootDir>/src/$1",
  },
  collectCoverage: true,
  collectCoverageFrom: [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/mocks/**/*",
    "!src/**/*page.tsx",
    "!src/**/*loading.tsx",
    "!src/**/datasets/**/*",
    "!src/**/components/ui/**/*",
    "!src/**/types/**/*",
    "!src/http/**/*",
    "!src/storage/**/*",
    "!src/factories/**/*",
    "!src/store/**/*",
    "!src/**/types.ts",
    "!src/**/schemas/**/*",
    "!src/components/CreditCardFormFields/AddressFields.tsx",
    "!src/**/index.ts",
  ],
  coverageDirectory: "<rootDir>/coverage",
  coverageReporters: ["text", "lcov"],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  testPathIgnorePatterns: ["/node_modules/", "/src/__tests__/datasets/"],
  // Optimize test performance
  testTimeout: 5000,
  // Force exit after tests complete to prevent hanging
  forceExit: true,
  // Cache test results for faster subsequent runs
  cache: true,
  // Only suppress console output when JEST_SILENT env var is set
  silent: process.env.JEST_SILENT === "true",
};

export default createJestConfig(config);
