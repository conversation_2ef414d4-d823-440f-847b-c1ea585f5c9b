name: Deploy to QA

on:
  workflow_dispatch:
    inputs:
      deploy_target:
        description: Environment to be deployed
        required: true
        type: choice
        options:
          - hml
          - safira

permissions:
  contents: read
  id-token: write

jobs:
  deploy:
    name: Deploy to Amplify
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Exit if the branch is main
        if: ${{ github.ref_name == 'main' }}
        run: |
          echo "Branch is main, exiting."
          exit 0

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/TheBankCheckoutRole
          aws-region: us-east-1

      - name: Validate Branch on Amplify
        continue-on-error: true
        id: branch
        run: |
          branch_exists=$(aws amplify get-branch \
          --app-id ${{ secrets.AMPLIFY_APP_ID }} \
          --branch-name ${{ github.ref_name }} | jq '.branch.branchName')
          echo "branch=$branch_exists" >> $GITHUB_OUTPUT

      - name: Deploy on Amplify
        id: deploy-on-amplify
        if: ${{ ! steps.branch.outputs.branch }}
        run: |
          aws amplify create-branch \
          --app-id ${{ secrets.AMPLIFY_APP_ID }} \
          --branch-name ${{ github.ref_name }} \
          --stage DEVELOPMENT \
          --no-enable-auto-build

      - name: Start build
        id: start-build
        run: |
          aws amplify start-job \
          --app-id ${{ secrets.AMPLIFY_APP_ID }} \
          --branch-name ${{ github.ref_name }} \
          --job-type RELEASE

      - name: Update domain
        run: |
          aws amplify update-domain-association \
          --app-id ${{ secrets.AMPLIFY_APP_ID }} \
          --domain-name checkout-${{ inputs.deploy_target }}.thebank.com.br \
          --sub-domain-settings prefix=,branchName=${{ github.ref_name }}
