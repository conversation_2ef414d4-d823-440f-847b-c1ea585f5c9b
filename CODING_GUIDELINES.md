# Diretrizes de Código - TB Web Checkout

Este documento estabelece diretrizes de código consistentes para todo o projeto
TB Web Checkout, baseadas na stack tecnológica identificada e aplicando
princípios SOLID, Clean Architecture e melhores práticas modernas.

## 📋 Stack Tecnológica

### Core Dependencies

| Tecnologia       | Versão | Propósito       | Diretrizes                                            |
| ---------------- | ------ | --------------- | ----------------------------------------------------- |
| **Next.js**      | 15.2.4 | Framework React | Use App Router, Server Components, evite `use client` |
| **React**        | 19.0.0 | Biblioteca UI   | Componentes funcionais, hooks, early returns          |
| **TypeScript**   | 5.x    | Type Safety     | Tipagem forte, interfaces, enums                      |
| **Tailwind CSS** | 3.4.17 | Estilização     | Classes utilitárias, design tokens, responsivo        |

### State Management

| Tecnologia          | Versão | Propósito     | Diretrizes                               |
| ------------------- | ------ | ------------- | ---------------------------------------- |
| **Zustand**         | 5.0.3  | Estado global | Stores focados, imutabilidade, selectors |
| **React Hook Form** | 7.54.2 | Formulários   | zodResolver, mode: onBlur, handleSubmit  |
| **Zod**             | 3.24.2 | Validação     | Schemas compostos, validação condicional |
| **TanStack Query**  | 5.69.0 | Server state  | Cache, invalidação, error handling       |

### UI Components

| Tecnologia                   | Versão  | Propósito        | Diretrizes                                 |
| ---------------------------- | ------- | ---------------- | ------------------------------------------ |
| **Radix UI**                 | Várias  | Componentes base | Acessibilidade, composição, customização   |
| **Lucide React**             | 0.484.0 | Ícones           | Consistência, tamanhos padrão, object maps |
| **Framer Motion**            | 12.6.2  | Animações        | Performance, gestos, layouts               |
| **Class Variance Authority** | 0.7.1   | Variantes CSS    | Tipagem, consistência, extensibilidade     |

### Data Fetching & Integration

| Tecnologia    | Versão | Propósito           | Diretrizes                            |
| ------------- | ------ | ------------------- | ------------------------------------- |
| **Axios**     | 1.8.4  | HTTP Client         | Interceptors, error handling, timeout |
| **Ably**      | 1.x    | WebSocket           | Reconnection, error handling, cleanup |
| **next-intl** | 3.26.5 | Internacionalização | Namespaces, type safety, SSR          |

### Development Tools

| Tecnologia   | Versão | Propósito  | Diretrizes                           |
| ------------ | ------ | ---------- | ------------------------------------ |
| **ESLint**   | 8.57.1 | Linting    | Regras customizadas, auto-fix, CI/CD |
| **Prettier** | 3.5.3  | Formatação | Configuração consistente, pre-commit |
| **Jest**     | 29.7.0 | Testes     | Comportamento, mocks, coverage 80%+  |
| **Husky**    | 9.1.7  | Git hooks  | Pre-commit, pre-push, mensagens      |

## 🏗️ Arquitetura e Estrutura

### Organização de Pastas

```typescript
// ✅ Estrutura seguindo Clean Architecture
src/
├── app/                    // PRESENTATION LAYER
│   ├── [locale]/          // Rotas internacionalizadas
│   │   ├── (app)/         // Páginas principais
│   │   │   ├── [id]/      // Checkout produto
│   │   │   ├── order/     // Páginas de pedido
│   │   │   ├── subscriptions/ // Páginas assinatura
│   │   │   └── success/   // Página sucesso
│   │   ├── layout.tsx     // Layout por locale
│   │   └── page.tsx       // Página inicial
│   ├── layout.tsx         // Layout global
│   ├── not-found.tsx      // Página 404
│   └── providers.tsx      // Providers globais
├── components/            // PRESENTATION LAYER
│   ├── ui/               // Componentes base (Button, Input, Card)
│   ├── features/         // Componentes específicos (BuyerForm, PaymentCard)
│   └── index.ts          // Barrel exports
├── hooks/                // APPLICATION LAYER
│   ├── useCreateOrder.ts // Use Cases
│   ├── usePaymentForm.ts // Application Services
│   └── index.ts          // Barrel exports
├── stores/               // APPLICATION LAYER
│   ├── buyerStore.ts     // Estado do comprador
│   ├── paymentStore.ts   // Estado de pagamento
│   └── index.ts          // Barrel exports
├── types/                // DOMAIN LAYER
│   ├── entities/         // Entidades de negócio
│   ├── valueObjects/     // Objetos de valor
│   ├── enums/           // Enumerações
│   └── index.ts         // Barrel exports
├── utils/                // DOMAIN LAYER
│   ├── validations/      // Regras de negócio
│   ├── calculations/     // Lógica de domínio
│   ├── formatting/       // Formatação de dados
│   └── index.ts          // Barrel exports
├── services/             // INFRASTRUCTURE LAYER
│   ├── api/             // Integrações externas
│   ├── storage/         // Persistência
│   ├── websocket/       // Comunicação tempo real
│   └── index.ts         // Barrel exports
├── schemas/              // INFRASTRUCTURE LAYER
│   ├── validation/       // Schemas Zod
│   └── index.ts          // Barrel exports
└── lib/                  // INFRASTRUCTURE LAYER
    ├── config/          // Configurações
    ├── utils.ts         // Utilitários gerais
    └── index.ts         // Barrel exports
```

### Convenções de Nomenclatura

```typescript
// ✅ Entidades de Domínio
interface Product {
  readonly id: ProductId;
  readonly title: string;
  readonly price: Money;
}

// ✅ Value Objects
class Money {
  constructor(
    private readonly amount: number,
    private readonly currency: Currency = "BRL"
  ) {}
}

// ✅ Enums tipados
enum PaymentStatus {
  PENDING = "pending",
  APPROVED = "approved",
  FAILED = "failed",
  CANCELLED = "cancelled",
}

// ✅ Componentes funcionais
export function PaymentForm({ product, onSubmit }: PaymentFormProps) {
  // Implementação
}

// ✅ Hooks customizados
export function useCreateOrder(): CreateOrderHook {
  // Implementação
}

// ✅ Stores Zustand
export const usePaymentStore = create<PaymentState>(set => ({
  // Implementação
}));

// ✅ Services
export class PaymentService {
  async process(data: PaymentData): Promise<PaymentResult> {
    // Implementação
  }
}

// ✅ Constantes
export const PAYMENT_METHODS = {
  CREDIT_CARD: "credit_card",
  PIX: "pix",
  BOLETO: "boleto",
} as const;
```

## 🎨 Padrões de Código

### 1. Componentes React

```typescript
// ✅ Estrutura padrão de componente
interface PaymentFormProps {
  product: Product;
  onSubmit: (data: PaymentData) => void;
  isLoading?: boolean;
  className?: string;
}

export function PaymentForm({
  product,
  onSubmit,
  isLoading = false,
  className
}: PaymentFormProps) {
  // 1. Early returns para guards
  if (!product) return <LoadingSpinner />;
  if (product.error) return <ErrorMessage error={product.error} />;

  // 2. Hooks e estado
  const { form, handleSubmit } = usePaymentForm();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>();

  // 3. Handlers e callbacks
  const handleMethodChange = useCallback((method: PaymentMethod) => {
    setSelectedMethod(method);
  }, []);

  // 4. Effects
  useEffect(() => {
    // Side effects
  }, []);

  // 5. Render helpers
  const renderPaymentMethod = (method: PaymentMethod) => {
    const methodComponents = {
      [PaymentMethod.CREDIT_CARD]: <CreditCardForm />,
      [PaymentMethod.PIX]: <PixForm />,
      [PaymentMethod.BOLETO]: <BoletoForm />,
    };

    return methodComponents[method] || null;
  };

  // 6. JSX principal
  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn('space-y-4', className)}
    >
      {/* Implementação */}
    </form>
  );
}

// 7. Memoização quando necessário
export const MemoizedPaymentForm = React.memo(PaymentForm);
```

### 2. Hooks Customizados (Error Handling)

```typescript
// ❌ EVITE: try/catch desnecessário com useMutation
const badExample = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: FormData) => {
    setIsLoading(true);
    try {
      await apiCall(data); // useMutation já gerencia isso!
    } catch (error) {
      console.error(error); // useMutation.onError é melhor
    } finally {
      setIsLoading(false); // useMutation.isPending é melhor
    }
  };
};

// ✅ BOM: Use useMutation para operações assíncronas
export function useCreateOrder(): CreateOrderHook {
  const mutation = useMutation({
    mutationFn: async (orderData: CreateOrderData) => {
      // 1. Validação (Domain) - validação síncrona
      const validation = OrderDomainService.validate(orderData);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // 2. Processamento (Infrastructure)
      const paymentResult = await paymentService.process(orderData.payment);
      if (!paymentResult.success) {
        throw new Error(paymentResult.error);
      }

      // 3. Criar pedido (Infrastructure)
      return orderService.create({
        ...orderData,
        paymentToken: paymentResult.data.token,
      });
    },
    onSuccess: order => {
      // Lógica de sucesso (navegação, notificações, etc.)
      logger.info("Order created successfully", { orderId: order.id });
    },
    onError: error => {
      // Lógica de erro (logging, notificações, etc.)
      logger.error("Order creation failed", error);
    },
  });

  return {
    createOrder: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error?.message || null,
    isError: mutation.isError,
    isSuccess: mutation.isSuccess,
  };
}

// ✅ Use try/catch APENAS para:
const validUseCases = () => {
  // 1. Validações síncronas
  const validateData = (data: unknown) => {
    try {
      return schema.parse(data);
    } catch (error) {
      return { error: "Dados inválidos" };
    }
  };

  // 2. Transformações de dados que podem falhar
  const parseJSON = (jsonString: string) => {
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  };

  // 3. Operações críticas que precisam de cleanup específico
  const criticalOperation = async () => {
    const resource = acquireResource();
    try {
      return await performOperation(resource);
    } finally {
      releaseResource(resource); // Cleanup obrigatório
    }
  };
};

// ✅ Hook para formulário
export function usePaymentForm(defaultValues?: Partial<PaymentFormData>) {
  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues,
    mode: "onBlur",
  });

  const handleSubmit = form.handleSubmit(async data => {
    // Lógica de submissão
  });

  return { form, handleSubmit };
}
```

### 3. Stores Zustand

```typescript
// ✅ Store com responsabilidade única
interface PaymentState {
  // Estado imutável
  readonly currentPayment: Payment | null;
  readonly processingStatus: ProcessingStatus;
  readonly errors: Record<string, string>;

  // Ações puras
  setPayment: (payment: Payment) => void;
  setProcessingStatus: (status: ProcessingStatus) => void;
  setError: (field: string, error: string) => void;
  clearErrors: () => void;
  reset: () => void;
}

const initialState = {
  currentPayment: null,
  processingStatus: ProcessingStatus.IDLE,
  errors: {},
};

export const usePaymentStore = create<PaymentState>((set, get) => ({
  ...initialState,

  setPayment: payment => set({ currentPayment: payment }),

  setProcessingStatus: status => set({ processingStatus: status }),

  setError: (field, error) =>
    set(state => ({
      errors: { ...state.errors, [field]: error },
    })),

  clearErrors: () => set({ errors: {} }),

  reset: () => set(initialState),
}));

// ✅ Selectors para otimização
export const usePaymentAmount = () =>
  usePaymentStore(state => state.currentPayment?.amount);

export const useHasPaymentErrors = () =>
  usePaymentStore(state => Object.keys(state.errors).length > 0);

export const useIsProcessingPayment = () =>
  usePaymentStore(
    state => state.processingStatus === ProcessingStatus.PROCESSING
  );
```

### 4. Validação com Zod

```typescript
// ✅ Schemas compostos e reutilizáveis
const baseEntitySchema = z.object({
  id: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const documentSchema = z
  .object({
    type: z.enum(["cpf", "cnpj"]),
    number: z.string(),
  })
  .refine(
    data =>
      data.type === "cpf" ? isValidCPF(data.number) : isValidCNPJ(data.number),
    { message: "Documento inválido" }
  );

const addressSchema = z.object({
  zipcode: z.string().regex(/^\d{5}-?\d{3}$/, "CEP inválido"),
  street: z.string().min(5, "Endereço deve ter pelo menos 5 caracteres"),
  number: z.string().min(1, "Número é obrigatório"),
  complement: z.string().optional(),
  district: z.string().min(2, "Bairro é obrigatório"),
  city: z.string().min(2, "Cidade é obrigatória"),
  state: z.string().length(2, "Estado deve ter 2 caracteres"),
});

const buyerSchema = z
  .object({
    name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
    email: z.string().email("Email inválido"),
    confirmEmail: z.string().email("Email inválido"),
    document: documentSchema,
    phone: z.object({
      ddi: z.string().min(2, "DDI é obrigatório"),
      number: z.string().min(8, "Número deve ter pelo menos 8 dígitos"),
    }),
    billingAddress: addressSchema,
  })
  .refine(data => data.email === data.confirmEmail, {
    message: "Emails não coincidem",
    path: ["confirmEmail"],
  });

// ✅ Schema principal com validação condicional
export const createPaymentSchema = (paymentMethod: PaymentMethod) =>
  z.object({
    buyer: buyerSchema,
    paymentMethod: z.nativeEnum(PaymentMethod),
    amount: z.number().min(0.01, "Valor deve ser maior que zero"),

    // Validação condicional baseada no método
    creditCard:
      paymentMethod === PaymentMethod.CREDIT_CARD
        ? z.object({
            holderName: z.string().min(2, "Nome do portador é obrigatório"),
            number: z
              .string()
              .refine(isValidCreditCard, "Número do cartão inválido"),
            expirationDate: z
              .string()
              .regex(/^\d{2}\/\d{2}$/, "Data inválida (MM/AA)"),
            cvv: z.string().min(3, "CVV deve ter pelo menos 3 dígitos"),
          })
        : z.undefined(),

    installments:
      paymentMethod === PaymentMethod.CREDIT_CARD
        ? z.number().min(1).max(12)
        : z.undefined(),
  });

// ✅ Tipos inferidos do schema
export type PaymentFormData = z.infer<ReturnType<typeof createPaymentSchema>>;
export type BuyerData = z.infer<typeof buyerSchema>;
export type AddressData = z.infer<typeof addressSchema>;
```

### 5. Services e APIs

```typescript
// ✅ Service com interface clara
interface PaymentService {
  process(data: PaymentData): Promise<PaymentResult>;
  tokenize(cardData: CardData): Promise<string>;
  validateCard(cardNumber: string): Promise<boolean>;
}

export class MalgaPaymentService implements PaymentService {
  constructor(
    private readonly httpClient: AxiosInstance,
    private readonly config: MalgaConfig
  ) {}

  async process(data: PaymentData): Promise<PaymentResult> {
    try {
      // Log estruturado
      logger.info("Payment processing started", {
        paymentMethod: data.method,
        amount: data.amount,
      });

      // Validação
      const validation = this.validatePaymentData(data);
      if (!validation.isValid) {
        throw new PaymentError(validation.error);
      }

      // Processamento
      const response = await this.httpClient.post("/payments", {
        ...data,
        credentials: this.config.credentials,
      });

      // Log de sucesso
      logger.info("Payment processed successfully", {
        paymentId: response.data.id,
        status: response.data.status,
      });

      return this.transformResponse(response.data);
    } catch (error) {
      // Log de erro
      logger.error("Payment processing failed", error, {
        paymentMethod: data.method,
        amount: data.amount,
      });

      throw this.handleError(error);
    }
  }

  private validatePaymentData(data: PaymentData): ValidationResult {
    // Validação específica do serviço
  }

  private transformResponse(data: any): PaymentResult {
    // Transformação de dados
  }

  private handleError(error: unknown): PaymentError {
    // Tratamento de erros específico
  }
}

// ✅ Factory para services
export function createPaymentService(
  provider: PaymentProvider
): PaymentService {
  const services = {
    [PaymentProvider.MALGA]: () =>
      new MalgaPaymentService(httpClient, malgaConfig),
    [PaymentProvider.STRIPE]: () =>
      new StripePaymentService(httpClient, stripeConfig),
  };

  const createService = services[provider];
  if (!createService) {
    throw new Error(`Unsupported payment provider: ${provider}`);
  }

  return createService();
}
```

### 6. Estilização com Tailwind CSS

```typescript
// ✅ Variantes com Class Variance Authority
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

export function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: ButtonProps) {
  const Comp = asChild ? Slot : 'button';
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

// ✅ Design tokens consistentes
export const designTokens = {
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
  },
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
  },
} as const;

// ✅ Classes utilitárias customizadas
export const utilityClasses = {
  // Layout
  container: 'mx-auto max-w-7xl px-4 sm:px-6 lg:px-8',
  flexCenter: 'flex items-center justify-center',
  gridCenter: 'grid place-items-center',

  // Typography
  heading1: 'text-3xl font-bold tracking-tight sm:text-4xl',
  heading2: 'text-2xl font-semibold tracking-tight',
  body: 'text-base leading-6',
  caption: 'text-sm text-muted-foreground',

  // States
  loading: 'animate-pulse bg-muted',
  disabled: 'opacity-50 cursor-not-allowed',
  error: 'text-destructive border-destructive',
  success: 'text-green-600 border-green-600',
} as const;
```

## 🧪 Testes

### Estratégia de Testes

```typescript
// ✅ Testes de componentes
describe('PaymentForm', () => {
  // Setup comum
  const defaultProps = {
    product: ProductFactory.create(),
    onSubmit: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all required fields', () => {
    render(<PaymentForm {...defaultProps} />);

    expect(screen.getByLabelText(/nome/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/documento/i)).toBeInTheDocument();
  });

  it('should submit valid payment data', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    // Arrange - Preencher formulário
    await user.type(screen.getByLabelText(/nome/i), 'João Silva');
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.selectOptions(screen.getByLabelText(/método/i), 'credit_card');

    // Act - Submeter
    await user.click(screen.getByRole('button', { name: /finalizar/i }));

    // Assert - Verificar chamada
    await waitFor(() => {
      expect(defaultProps.onSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          buyer: expect.objectContaining({
            name: 'João Silva',
            email: '<EMAIL>',
          }),
        })
      );
    });
  });

  it('should display validation errors', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    // Submeter sem preencher
    await user.click(screen.getByRole('button', { name: /finalizar/i }));

    // Verificar erros
    expect(screen.getByText(/nome é obrigatório/i)).toBeInTheDocument();
    expect(screen.getByText(/email é obrigatório/i)).toBeInTheDocument();
  });
});

// ✅ Testes de hooks
describe('useCreateOrder', () => {
  it('should create order successfully', async () => {
    // Mock dependencies
    const mockOrderData = OrderDataFactory.create();
    mockPaymentService.process.mockResolvedValue({
      success: true,
      data: { token: 'token123' }
    });

    const { result } = renderHook(() => useCreateOrder());

    // Act
    await act(async () => {
      await result.current.createOrder(mockOrderData);
    });

    // Assert
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(mockPaymentService.process).toHaveBeenCalledWith(mockOrderData);
  });

  it('should handle errors correctly', async () => {
    const mockError = new Error('Payment failed');
    mockPaymentService.process.mockRejectedValue(mockError);

    const { result } = renderHook(() => useCreateOrder());

    await act(async () => {
      try {
        await result.current.createOrder(OrderDataFactory.create());
      } catch (error) {
        // Expected error
      }
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe('Payment failed');
  });
});

// ✅ Testes de Domain Services
describe('PaymentDomainService', () => {
  describe('calculateInstallments', () => {
    it('should calculate installments correctly', () => {
      const amount = new Money(1000); // R$ 10,00
      const maxInstallments = 3;

      const installments = PaymentDomainService.calculateInstallments(
        amount,
        maxInstallments
      );

      expect(installments).toHaveLength(3);
      expect(installments[0].number).toBe(1);
      expect(installments[0].amount.amount).toBe(1000);
      expect(installments[2].number).toBe(3);
      expect(installments[2].amount.amount).toBeCloseTo(333.33);
    });
  });

  describe('validatePaymentMethod', () => {
    it('should validate supported payment methods', () => {
      const product = ProductFactory.create({
        paymentMethods: [PaymentMethod.CREDIT_CARD, PaymentMethod.PIX]
      });

      expect(
        PaymentDomainService.validatePaymentMethod(product, PaymentMethod.CREDIT_CARD)
      ).toBe(true);

      expect(
        PaymentDomainService.validatePaymentMethod(product, PaymentMethod.BOLETO)
      ).toBe(false);
    });
  });
});

// ✅ Factories para testes
export const ProductFactory = {
  create: (overrides?: Partial<Product>): Product => ({
    id: 'product-123',
    title: 'Produto Teste',
    price: new Money(9999), // R$ 99,99
    paymentMethods: [PaymentMethod.CREDIT_CARD, PaymentMethod.PIX],
    ...overrides,
  }),
};

export const OrderDataFactory = {
  create: (overrides?: Partial<CreateOrderData>): CreateOrderData => ({
    buyer: {
      name: 'João Silva',
      email: '<EMAIL>',
      document: { type: 'cpf', number: '12345678901' },
    },
    paymentMethod: PaymentMethod.CREDIT_CARD,
    amount: 9999,
    ...overrides,
  }),
};
```

## 🔧 Configurações

### ESLint

```json
// .eslintrc.json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "plugins": ["@typescript-eslint", "simple-import-sort", "jsx-a11y"],
  "rules": {
    // Imports
    "simple-import-sort/imports": "error",
    "simple-import-sort/exports": "error",

    // TypeScript
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",

    // React
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "react/display-name": "off",

    // Accessibility
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/aria-props": "error",
    "jsx-a11y/aria-proptypes": "error",

    // General
    "prefer-const": "error",
    "no-var": "error",
    "no-console": ["warn", { "allow": ["warn", "error"] }]
  }
}
```

### Prettier

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

### TypeScript

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## 🚀 Performance

### Bundle Optimization

```typescript
// ✅ Dynamic imports para code splitting
const PaymentChart = lazy(() => import("./PaymentChart"));
const AdminPanel = lazy(() => import("./AdminPanel"));

// ✅ Conditional loading
const loadPaymentProcessor = async (method: PaymentMethod) => {
  switch (method) {
    case PaymentMethod.CREDIT_CARD:
      return import("./processors/CreditCardProcessor");
    case PaymentMethod.PIX:
      return import("./processors/PixProcessor");
    case PaymentMethod.BOLETO:
      return import("./processors/BoletoProcessor");
    default:
      throw new Error(`Unsupported payment method: ${method}`);
  }
};

// ✅ Preload critical resources
export function preloadCriticalResources() {
  // Preload fonts
  const fontLink = document.createElement("link");
  fontLink.rel = "preload";
  fontLink.href = "/fonts/inter-var.woff2";
  fontLink.as = "font";
  fontLink.type = "font/woff2";
  fontLink.crossOrigin = "anonymous";
  document.head.appendChild(fontLink);

  // Preload critical images
  const imageLink = document.createElement("link");
  imageLink.rel = "preload";
  imageLink.href = "/images/logo.svg";
  imageLink.as = "image";
  document.head.appendChild(imageLink);
}
```

### React Optimization

```typescript
// ✅ Memoização consciente
const ExpensiveComponent = React.memo(({ data }: { data: ComplexData }) => {
  // Component que realmente precisa de memoização
  const processedData = useMemo(() => {
    return expensiveCalculation(data);
  }, [data]);

  return <div>{processedData}</div>;
});

// ✅ useCallback para funções estáveis
const PaymentForm = ({ onSubmit }: { onSubmit: (data: any) => void }) => {
  const handleSubmit = useCallback((data: PaymentData) => {
    // Validação local
    const validation = validatePaymentData(data);
    if (validation.isValid) {
      onSubmit(data);
    }
  }, [onSubmit]);

  return <form onSubmit={handleSubmit} />;
};

// ✅ Evite memoização desnecessária
const SimpleComponent = ({ title }: { title: string }) => {
  // Não precisa de memo - React 19 otimiza automaticamente
  return <h1>{title}</h1>;
};
```

## 🔐 Segurança

### Validação de Dados

```typescript
// ✅ Sanitização de inputs
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, "") // Remove caracteres perigosos
    .slice(0, 1000); // Limita tamanho
}

// ✅ Validação dupla (client + server)
export function validatePaymentData(data: PaymentData): ValidationResult {
  // Client-side validation
  const schema = paymentSchema;
  const result = schema.safeParse(data);

  if (!result.success) {
    return {
      isValid: false,
      errors: result.error.flatten().fieldErrors,
    };
  }

  // Additional business rules
  if (data.amount <= 0) {
    return {
      isValid: false,
      errors: { amount: ["Valor deve ser maior que zero"] },
    };
  }

  return { isValid: true };
}

// ✅ Nunca armazene dados sensíveis
export function tokenizeCardData(cardData: CardData): Promise<string> {
  // Sempre tokenize no servidor
  return paymentService.tokenize(cardData);
}

// ✅ Use HTTPS sempre
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});
```

### Error Handling

```typescript
// ✅ Error boundaries específicos
export class PaymentErrorBoundary extends Component<
  React.PropsWithChildren<{}>,
  { hasError: boolean; error?: Error }
> {
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log estruturado
    logger.error('Payment Error Boundary', error, {
      componentStack: errorInfo.componentStack,
      errorBoundary: 'PaymentErrorBoundary',
    });

    // Enviar para monitoramento
    errorTrackingService.captureException(error, {
      context: 'payment',
      extra: errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-red-800 font-semibold">Erro no Pagamento</h3>
          <p className="text-red-600">
            Ocorreu um erro inesperado. Tente novamente ou entre em contato com o suporte.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded"
          >
            Recarregar Página
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## 📊 Monitoramento

### Logging Estruturado

```typescript
// ✅ Logger centralizado
interface LogContext {
  userId?: string;
  sessionId?: string;
  paymentMethod?: PaymentMethod;
  amount?: number;
  [key: string]: any;
}

export const logger = {
  info: (message: string, context?: LogContext) => {
    console.log(
      JSON.stringify({
        level: "info",
        message,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        ...context,
      })
    );
  },

  error: (message: string, error?: Error, context?: LogContext) => {
    console.error(
      JSON.stringify({
        level: "error",
        message,
        error: error?.message,
        stack: error?.stack,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        ...context,
      })
    );
  },

  warn: (message: string, context?: LogContext) => {
    console.warn(
      JSON.stringify({
        level: "warn",
        message,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        ...context,
      })
    );
  },
};

// ✅ Métricas de performance
export function trackPerformance(name: string, fn: () => Promise<any>) {
  return async (...args: any[]) => {
    const startTime = performance.now();

    try {
      const result = await fn.apply(this, args);
      const duration = performance.now() - startTime;

      logger.info("Performance metric", {
        metric: name,
        duration: Math.round(duration),
        status: "success",
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;

      logger.error("Performance metric failed", error, {
        metric: name,
        duration: Math.round(duration),
        status: "error",
      });

      throw error;
    }
  };
}

// ✅ Uso do tracking
const trackedProcessPayment = trackPerformance(
  "processPayment",
  paymentService.process
);
```

## 📋 Checklist de Qualidade

### Antes do Commit

- [ ] **Arquitetura**: Código segue Clean Architecture
- [ ] **SOLID**: Princípios SOLID aplicados
- [ ] **TypeScript**: Tipagem forte sem `any`
- [ ] **Testes**: Cobertura adequada (80%+)
- [ ] **Performance**: Otimizações conscientes
- [ ] **Segurança**: Dados sensíveis protegidos
- [ ] **Acessibilidade**: ARIA labels e navegação por teclado
- [ ] **Internacionalização**: Strings traduzidas
- [ ] **Responsividade**: Mobile-first testado
- [ ] **Error Handling**: Tratamento adequado de erros

### Antes do Deploy

- [ ] **Build**: Compilação sem erros
- [ ] **Linting**: ESLint sem warnings
- [ ] **Formatação**: Prettier aplicado
- [ ] **Testes**: Todos os testes passando
- [ ] **Bundle Size**: Tamanho otimizado
- [ ] **Performance**: Lighthouse score > 90
- [ ] **Security**: Auditoria de segurança
- [ ] **Monitoring**: Logs e métricas configurados

---

## 🎯 Resumo das Diretrizes

1. **Arquitetura**: Clean Architecture com camadas bem definidas
2. **SOLID**: Aplicar todos os 5 princípios consistentemente
3. **TypeScript**: Tipagem forte em todas as camadas
4. **React**: Componentes funcionais, hooks, early returns
5. **Estado**: Zustand para global, React Hook Form para formulários
6. **Validação**: Zod com schemas compostos
7. **Estilização**: Tailwind CSS com design tokens
8. **Testes**: Comportamento, não implementação
9. **Performance**: Otimização consciente, não prematura
10. **Segurança**: Validação dupla, tokenização, HTTPS

**Prioridade**: Experiência do usuário → Segurança → Manutenibilidade →
Performance
