import "@testing-library/jest-dom/jest-globals";
import "@testing-library/jest-dom";

global.WebSocket = class MockWebSocket {
  constructor() {
    // Mock WebSocket that does nothing
  }
  close() {}
  send() {}
  addEventListener() {}
  removeEventListener() {}
} as any;

// Store original console methods
const originalError = console.error;
const originalWarn = console.warn;
const originalLog = console.log;

// AGGRESSIVE CONSOLE SUPPRESSION - Override immediately and permanently
const silentFunction = () => {};

// Override ALL console methods globally to prevent ANY logs
console.warn = silentFunction;
console.error = silentFunction;
console.info = silentFunction;
console.debug = silentFunction;

// Also override on global object to catch <PERSON><PERSON>'s console buffering
if (typeof global !== "undefined") {
  global.console.warn = silentFunction;
  global.console.error = silentFunction;
  global.console.info = silentFunction;
  global.console.debug = silentFunction;
}

// Override on window object if available
if (typeof window !== "undefined") {
  window.console.warn = silentFunction;
  window.console.error = silentFunction;
  window.console.info = silentFunction;
  window.console.debug = silentFunction;
}

// Additional safety: Mock Ably completely to prevent any initialization
if (typeof global !== "undefined") {
  (global as any).Ably = undefined;
  (global as any).AblyAuth = undefined;
}

if (typeof window !== "undefined") {
  (window as any).Ably = undefined;
  (window as any).AblyAuth = undefined;
}

beforeAll(() => {
  // Suppress ALL console.error messages during tests
  console.error = () => {
    // Completely silent - suppress all errors
  };

  // Suppress ALL console.warn messages during tests
  console.warn = () => {
    // Completely silent - suppress all warnings
  };

  // Suppress ALL console.log messages that start with "error" during tests
  console.log = (...args) => {
    const message = String(args[0]);

    // Suppress any logs that start with "error"
    if (/^error\s/.test(message)) {
      return;
    }

    // Allow other console.log messages (for debugging if needed)
    originalLog.call(console, ...args);
  };
});

afterEach(() => {
  // Clear all timers after each test
  jest.clearAllTimers();

  // Clean up window.Echo and window.Ably only if they exist
  if (typeof window !== "undefined") {
    if ((window as any).Echo) delete (window as any).Echo;
    if ((window as any).Ably) delete (window as any).Ably;
  }
});

afterAll(() => {
  // Restore original console methods
  console.error = originalError;
  console.warn = originalWarn;
  console.log = originalLog;

  // Clear all timers
  jest.clearAllTimers();
});
