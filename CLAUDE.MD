# TB Web Checkout – cursor.rules

- ARQUITETURA: Clean Architecture (Presentation → Application → Domain → Infra).
  Dependências sempre apontam para dentro; Domain não conhece Infra.
- SOLID: SRP • OCP (extensão via object literals) • LSP • ISP • DIP.
- TYPESCRIPT: Prefira funções e FCs; `interface` > `type`; enums + object
  literals; early return + Result pattern para erros.
- FUNCIONAL: Funções puras, imutabilidade e composição simples; evite estados
  ocultos.
- REACT/NEXT: FCs + hooks; hash maps para render condicional; `React.memo`,
  `useMemo`, `useCallback` só quando medem.
- ESTADO: Zustand para estado global; React Hook Form + Zod para formulários;
  use seletores para minimizar re-renders.
- DOMÍNIO: Entidades imutáveis e Value Objects (ex.: `Money`); serviços de
  domínio concentram regras.
- USE CASES: `useMutation` para chamadas; valide de forma síncrona antes de
  chamar APIs; handlers `onSuccess`/`onError`.
- PADRÕES: Strategy (pagamentos), Observer (WebSocket); factory para instanciar
  estratégias.
- VALIDAÇÃO & ERROS: Schemas Zod compostos; `useMutation` trata async;
  `try/catch` só para validações síncronas ou cleanup; Error Boundary específico
  para pagamentos.
- TAILWIND/UI: Classes utilitárias consistentes, `clsx` para condição, design
  tokens; `gap` > `justify-between`.
- TESTES: Foque em comportamento (RTL); hooks testados isoladamente; cobertura
  alta; factories para dados mock.
- PERFORMANCE: Lazy loading (dynamic import + Suspense), memoização consciente,
  code splitting onde faz sentido.
- SEGURANÇA: Sanitização de entrada, HTTPS, tokenização no backend; nunca
  armazene dados sensíveis no localStorage.
- LOGGING: Logging estruturado + error tracking em pontos críticos.

## DOMÍNIO ESPECÍFICO

### PAGAMENTOS

- **Métodos**: CreditCard (tokenização + validação real-time) • PIX (QR +
  WebSocket polling) • Boleto (geração + download).
- **Fluxos**: Produto único vs Assinatura vs Trial; trials → success direto;
  preservar dados originais de parcelas.
- **WebSocket**: Ably channels `order.{id}` | `subscription.{id}`; reconexão
  automática; estados de conexão.
- **Tokenização**: `processPayment()` + `@thebank/creditcard-js`; nunca dados
  raw no payload.

### ASSINATURAS

- **Endpoints**: `POST /v1/checkout/subscriptions/{id}?signature={sig}` •
  signature obrigatória.
- **Interfaces**: `SubscriptionProduct` (simplificada) vs `Product` (completa).
- **Transformação**: WebSocket response → success page format;
  `sessionStorage.buyerData` para trials.
- **UI**: Seção purple com dados do cliente; bumps mostram "Adicionar
  assinatura".

### INTERNACIONALIZAÇÃO

- **next-intl**: `useTranslations('namespace')` • namespaces por funcionalidade.
- **Estrutura**: `/locales/{locale}/*.json` • pt-BR, en-US, es-ES.

### STORES & HOOKS

- **buyerStore**: tokenização/payment errors • `setTokenizationError()`,
  `setPaymentError()`.
- **Hooks**: `useCreateOrderSubmitHandler` (produtos) • `useBuyerFormSchema`
  (validação).
- **Formulários**: React Hook Form + zodResolver • FormProvider para complexos.

### CONVENÇÕES

- **Componentes**: PascalCase inglês (`BuyerForm`, `SubscriptionRenewalForm`).
- **Arquivos**: camelCase/kebab-case consistente • index.tsx para exports.
- **Renderização**: Object literals > ternários aninhados • hash maps para
  componentes.

```typescript
// Exemplo: Payment Strategy
const paymentHandlers = {
  creditCard: () => processCardPayment(),
  pix: () => generatePixQR(),
  boleto: () => generateBoleto(),
};

// Hash map para componentes
const paymentCards = {
  creditCard: <CreditCardForm />,
  pix: <PixCard />,
  boleto: <BoletoCard />,
};
```
