import { getRequestConfig } from "next-intl/server";

import { globalDefaultLang } from "../src/support/lang/config";

export default getRequestConfig(async ({ requestLocale }) => {
  const locale = (await requestLocale) || globalDefaultLang;

  if (!locale) {
    throw new Error("Locale is not defined");
  }

  try {
    const messages = (await import(`../locales/${locale}/common.json`)).default;
    return {
      locale,
      messages,
    };
  } catch (error) {
    console.error(`Failed to load messages for locale ${locale}:`, error);
    throw error;
  }
});
